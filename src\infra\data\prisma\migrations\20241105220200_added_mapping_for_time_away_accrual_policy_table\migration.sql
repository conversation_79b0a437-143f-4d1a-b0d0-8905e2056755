/*
  Warnings:

  - You are about to drop the column `accrualFrequency` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `accrualFrequencyStart` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `accrualFrequencyUnit` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `accrualStart` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `accrualStartUnit` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `carryOverAmount` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `carryOverAmountUnit` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `carryOverDate` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `carryOverRule` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - You are about to drop the column `timeAwayTypeId` on the `time_away_accrual_policies` table. All the data in the column will be lost.
  - Added the required column `accrual_frequency` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accrual_frequency_start` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accrual_frequency_unit` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accrual_start` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accrual_start_unit` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carry_over_amount` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carry_over_amount_unit` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carry_over_date` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `carry_over_rule` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `timeAway_type_id` to the `time_away_accrual_policies` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "time_away_accrual_policies" DROP CONSTRAINT "time_away_accrual_policies_timeAwayTypeId_fkey";

-- AlterTable
ALTER TABLE "time_away_accrual_policies" DROP COLUMN "accrualFrequency",
DROP COLUMN "accrualFrequencyStart",
DROP COLUMN "accrualFrequencyUnit",
DROP COLUMN "accrualStart",
DROP COLUMN "accrualStartUnit",
DROP COLUMN "carryOverAmount",
DROP COLUMN "carryOverAmountUnit",
DROP COLUMN "carryOverDate",
DROP COLUMN "carryOverRule",
DROP COLUMN "timeAwayTypeId",
ADD COLUMN     "accrual_frequency" INTEGER NOT NULL,
ADD COLUMN     "accrual_frequency_start" INTEGER NOT NULL,
ADD COLUMN     "accrual_frequency_unit" "TimeAwayUnits" NOT NULL,
ADD COLUMN     "accrual_start" INTEGER NOT NULL,
ADD COLUMN     "accrual_start_unit" "TimeAwayUnits" NOT NULL,
ADD COLUMN     "carry_over_amount" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "carry_over_amount_unit" "TimeAwayUnits" NOT NULL,
ADD COLUMN     "carry_over_date" DATE NOT NULL,
ADD COLUMN     "carry_over_rule" "CarryOverRule" NOT NULL,
ADD COLUMN     "timeAway_type_id" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "time_away_accrual_policies" ADD CONSTRAINT "time_away_accrual_policies_timeAway_type_id_fkey" FOREIGN KEY ("timeAway_type_id") REFERENCES "time_away_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
