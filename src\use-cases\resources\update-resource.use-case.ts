import { EmailSender } from '@/core/abstracts/email-sender';
import { FilesManager } from '@/core/abstracts/files-manager';
import { UseCase } from '@/core/base/use-case';
import { FileEntity } from '@/core/domain/entities/file.entity';
import { CreatedFileMapper } from '@/core/domain/mappers/files/created-file.mapper';
import { CreatedResourceMapper } from '@/core/domain/mappers/resources/created-resource.mapper';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { MailBuilderService } from '@/infra/framework/mailer/mail-builder.service';
import { FileDto } from '@/shared/dtos/files/file.dto';
import { CreatedResourceDto } from '@/shared/dtos/resources/created-resource.dto';
import { UpdateResourceDto } from '@/shared/dtos/resources/update-resource.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class UpdateResourceUseCase implements UseCase<CreatedResourceDto> {
  private createdResourceMapper: CreatedResourceMapper;
  private createdFileMapper: CreatedFileMapper;

  constructor(
    private readonly resourcesRepository: ResourcesRepository,
    private readonly filesRepository: FilesRepository,

    private readonly filesManager: FilesManager,
    private readonly mailBuilder: MailBuilderService,
    private readonly emailSender: EmailSender,
    private readonly prisma: PrismaService,
  ) {
    this.createdResourceMapper = new CreatedResourceMapper();
    this.createdFileMapper = new CreatedFileMapper();
  }

  public async execute(
    id: string,
    data: UpdateResourceDto,
    updater: CreatedUserDetailsDto,
  ): Promise<CreatedResourceDto> {
    const { attachmentIds, draft, ...rest } = data;

    let resourceEntity = await this.resourcesRepository.findOne({ id });

    if (!resourceEntity || resourceEntity.accountId !== updater.accountId) {
      throw new EntityNotFoundException('Resource not found');
    }

    const { data: files } = await this.filesRepository.findAll({
      resourceId: resourceEntity?.id,
    });

    for (const file of files) {
      if (data.attachmentIds?.includes(file.id)) {
        continue;
      } else await this.filesRepository.update(file.id, { resourceId: null });
    }

    // Resource will be published
    resourceEntity = await this.resourcesRepository.update(id, {
      ...rest,
      publishDate: draft === true ? null : new Date(),
    });

    const attachments: FileEntity[] = [];
    const mailAttachments: FileDto[] = [];

    for (const attachmentId of data.attachmentIds ?? []) {
      if (attachments.find((file) => file.id === attachmentId)) {
        continue;
      }
      const updatedFile = await this.filesRepository.update(attachmentId, {
        resourceId: resourceEntity.id,
      });
      attachments.push(updatedFile);

      if (!data.draft) {
        const content = await this.filesManager.download(updatedFile.fileName);
        mailAttachments.push({
          ...this.createdFileMapper.map(updatedFile),
          content,
        });
      }
    }

    if (!data.draft) {
      const account = await this.prisma.account.findUnique({
        where: { id: updater.accountId },
        include: {
          users: {
            select: { firstName: true, email: true },
            where: { activatedAt: { not: null } },
          },
        },
      });
      if (account?.users) {
        for (const user of account.users) {
          const mailData = await this.mailBuilder.buildAnnouncementEmail(
            resourceEntity.title,
            resourceEntity.content,
            updater.firstName,
            user.firstName,
            user.email,
            resourceEntity.resourceType,
            mailAttachments,
          );
          void this.emailSender.send(mailData);
        }
      }
    }

    return this.createdResourceMapper.map({
      ...resourceEntity,
      attachments: attachments,
    });
  }
}
