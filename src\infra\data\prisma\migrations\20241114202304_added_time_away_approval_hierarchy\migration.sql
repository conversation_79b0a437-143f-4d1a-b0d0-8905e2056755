-- Create<PERSON>num
CREATE TYPE "time_away_approver_types" AS ENUM ('DEPARTMENT_HEAD', 'SPECIFIC_ROLE', 'SPECIFIC_PERSON');

-- DropForeignKey
ALTER TABLE "attendances" DROP CONSTRAINT "attendances_end_picture_id_fkey";

-- DropForeignKey
ALTER TABLE "attendances" DROP CONSTRAINT "attendances_start_picture_id_fkey";

-- DropForeignKey
ALTER TABLE "attendances" DROP CONSTRAINT "attendances_user_id_fkey";

-- DropForeignKey
ALTER TABLE "files" DROP CONSTRAINT "files_uploader_id_fkey";

-- DropForeignKey
ALTER TABLE "resources" DROP CONSTRAINT "resources_uploader_id_fkey";

-- DropForeignKey
ALTER TABLE "time_aways" DROP CONSTRAINT "time_aways_user_id_fkey";

-- AlterTable
ALTER TABLE "resources" ALTER COLUMN "uploader_id" DROP NOT NULL;

-- CreateTable
CREATE TABLE "time_away_approval_hierarchies" (
    "id" TEXT NOT NULL,
    "account_id" TEXT NOT NULL,

    CONSTRAINT "time_away_approval_hierarchies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "time_away_approvers" (
    "id" TEXT NOT NULL,
    "time_away_approval_hierarchy_id" TEXT NOT NULL,
    "approver_type" "time_away_approver_types" NOT NULL,
    "approver_user_id" TEXT,
    "approver_role_id" TEXT,
    "previous_approver_id" TEXT,

    CONSTRAINT "time_away_approvers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "time_away_approval_history" (
    "id" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "isApproved" BOOLEAN NOT NULL,
    "approverId" TEXT NOT NULL,
    "timeAwayId" TEXT,

    CONSTRAINT "time_away_approval_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "time_away_approval_hierarchies_account_id_key" ON "time_away_approval_hierarchies"("account_id");

-- CreateIndex
CREATE UNIQUE INDEX "time_away_approvers_previous_approver_id_key" ON "time_away_approvers"("previous_approver_id");

-- AddForeignKey
ALTER TABLE "time_aways" ADD CONSTRAINT "time_aways_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "resources" ADD CONSTRAINT "resources_uploader_id_fkey" FOREIGN KEY ("uploader_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "files" ADD CONSTRAINT "files_uploader_id_fkey" FOREIGN KEY ("uploader_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "attendances" ADD CONSTRAINT "attendances_start_picture_id_fkey" FOREIGN KEY ("start_picture_id") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "attendances" ADD CONSTRAINT "attendances_end_picture_id_fkey" FOREIGN KEY ("end_picture_id") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "attendances" ADD CONSTRAINT "attendances_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_hierarchies" ADD CONSTRAINT "time_away_approval_hierarchies_account_id_fkey" FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approvers" ADD CONSTRAINT "time_away_approvers_time_away_approval_hierarchy_id_fkey" FOREIGN KEY ("time_away_approval_hierarchy_id") REFERENCES "time_away_approval_hierarchies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approvers" ADD CONSTRAINT "time_away_approvers_approver_user_id_fkey" FOREIGN KEY ("approver_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approvers" ADD CONSTRAINT "time_away_approvers_approver_role_id_fkey" FOREIGN KEY ("approver_role_id") REFERENCES "roles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approvers" ADD CONSTRAINT "time_away_approvers_previous_approver_id_fkey" FOREIGN KEY ("previous_approver_id") REFERENCES "time_away_approvers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history" ADD CONSTRAINT "time_away_approval_history_approverId_fkey" FOREIGN KEY ("approverId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history" ADD CONSTRAINT "time_away_approval_history_timeAwayId_fkey" FOREIGN KEY ("timeAwayId") REFERENCES "time_aways"("id") ON DELETE SET NULL ON UPDATE CASCADE;
