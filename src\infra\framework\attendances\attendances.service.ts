import { OpusRoles } from '@/core/enums/role.enum';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { ExportAttendanceInputDto } from '@/shared/dtos/attendances/export-attendance.dto';
import { DaylogStatus } from '@/shared/dtos/attendances/grouped-day-attendance.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import {
  eachDayOfIntervalAtTimezone,
  startOfDayAtTimezone,
  toZonedTime,
} from '@/shared/helpers/date';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { IncomingWebhook } from '@slack/webhook';
import { differenceInMinutes, formatDate } from 'date-fns';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { unparse } from 'papaparse';
import { MailBuilderService } from '../mailer/mail-builder.service';
import { EmailSender } from '@/core/abstracts/email-sender';
import { LoggerService } from '../logger/logger.service';

@Injectable()
export class AttendancesService {
  static REMINDERS_HOURS_START = 8;
  static REMINDERS_HOURS_RECUR = 4;

  private readonly logger = new LoggerService('Attendances');

  constructor(
    private readonly prisma: PrismaService,
    private readonly mailBuilderService: MailBuilderService,
    private readonly mailSender: EmailSender,
  ) {}

  async sendSlackNotif(user: CreatedUserDetailsDto, clockType: 'in' | 'out') {
    const account = await this.prisma.account.findUnique({
      where: { id: user.accountId ?? undefined },
    });

    if (!account) {
      throw new EntityNotFoundException('Account does not exist');
    }

    if (!account.attendanceWebhookUrl) {
      throw new EntityNotFoundException('Webhook url does not exist');
    }

    const webhook = new IncomingWebhook(account.attendanceWebhookUrl);

    let message;

    switch (clockType) {
      case 'in':
        message = `Good morning! *${user.firstName}* has clocked in ☀️!`;
        break;
      case 'out':
        message = `See you again! *${user.firstName}* has clocked out 👋!`;
    }

    await webhook.send({
      blocks: [{ type: 'section', text: { text: message, type: 'mrkdwn' } }],
    });
  }

  async groupAttendance(
    filter: ExportAttendanceInputDto,
    requester: CreatedUserDetailsDto,
  ) {
    if (requester.role.title !== OpusRoles.Admin) {
      throw new ForbiddenException();
    }

    const intervals = eachDayOfIntervalAtTimezone(
      filter.startDate,
      filter.endDate,
      requester.timezone,
    );

    const users = await this.prisma.user.findMany({
      where: { accountId: requester.accountId, activatedAt: { not: null } },
      select: { id: true, firstName: true, lastName: true },
    });
    const attendances = await this.prisma.attendance.findMany({
      where: {
        user: { accountId: requester.accountId },
        startDatetime: { gte: filter.startDate },
        endDatetime: { lte: filter.endDate },
      },
      orderBy: { startDatetime: 'asc' },
      include: {
        user: { select: { id: true } },
      },
    });

    const timeAways = await this.prisma.timeAway.findMany({
      where: {
        user: { accountId: requester.accountId },
        isApproved: true,
        isUserRequest: true,
        AND: [
          {
            startDate: { gte: filter.startDate },
          },
          { endDate: { lte: filter.endDate } },
        ],
      },
    });

    /**
     * logs [day, userId, firstName, lastName, clockIn, clockOut, hours, status]
     */
    const logs = {
      logs: [] as {
        day: Date;
        userId: string;
        firstName: string;
        lastName: string;
        clockIn?: Date;
        clockOut?: Date;
        millis: number;
        status: DaylogStatus;
      }[],
      counts: [] as {
        day: Date;
        present: number;
        onLeave: number;
        pending: number;
        totalEmployees: number;
      }[],
    };

    for (const user of users) {
      for (const currDay of intervals) {
        const leaveAtDay = timeAways.find(
          (timeAway) =>
            timeAway.userId == user.id &&
            timeAway.startDate <= currDay &&
            timeAway.endDate > currDay,
        );
        const attendance = attendances.find((attendance) => {
          return (
            attendance.user.id == user.id &&
            startOfDayAtTimezone(
              attendance.startDatetime,
              requester.timezone,
            ).getTime() == currDay.getTime()
          );
        });

        const status = leaveAtDay
          ? DaylogStatus.onLeave
          : !attendance
            ? DaylogStatus.pending
            : DaylogStatus.present;
        logs.logs.push({
          day: currDay,
          userId: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          clockIn: attendance?.startDatetime,
          clockOut: attendance?.endDatetime ?? undefined,
          millis: attendance?.millis ?? 0,
          status,
        });
        let dayCount = logs.counts.find((count) => count.day === currDay);

        if (!dayCount) {
          dayCount = {
            day: currDay,
            totalEmployees: users.length,
            present: 0,
            onLeave: 0,
            pending: 0,
          };
          logs.counts.push(dayCount);
        }
        dayCount.onLeave += status == DaylogStatus.onLeave ? 1 : 0;
        dayCount.pending += status == DaylogStatus.pending ? 1 : 0;
        dayCount.present += status == DaylogStatus.present ? 1 : 0;
      }
    }
    return logs;
  }

  async exportAttendanceCsv(
    filter: ExportAttendanceInputDto,
    requester: CreatedUserDetailsDto,
  ) {
    const grouped = await this.groupAttendance(filter, requester);

    const logs = grouped.logs.map((log) => ({
      'Employee Name': `${log.lastName}, ${log.firstName}`,
      Date: formatDate(log.day, 'yyyy-MM-dd'),
      'Clock In': log.clockIn
        ? formatDate(toZonedTime(log.clockIn, requester.timezone), 'hh:mm a')
        : ' ',
      'Clock Out': log.clockIn
        ? formatDate(toZonedTime(log.clockIn, requester.timezone), 'hh:mm a')
        : ' ',
      'Total (Hours)': (log.millis / 3600000).toFixed(2),
    }));

    return unparse(logs);
  }

  async exportAttendancePdf(
    filter: ExportAttendanceInputDto,
    user: CreatedUserDetailsDto,
  ) {
    const grouped = await this.groupAttendance(filter, user);
    const doc = new jsPDF();
    doc.text(`${user.account?.accountName} - Attendance logs`, 10, 10);

    // const counts = grouped.counts.map((count) => ({
    //   date: formatDate(count.day, 'yyyy-MM-dd'),
    //   present: count.present,
    //   pending: count.pending,
    //   onLeave: count.onLeave,
    // }));
    // autoTable(doc, {
    //   body: counts,
    //   columns: [
    //     { header: 'Date', dataKey: 'date' },
    //     { header: 'Present', dataKey: 'present' },
    //     { header: 'No Record', dataKey: 'pending' },
    //     { header: 'On Leave', dataKey: 'onLeave' },
    //   ],
    //   tableWidth: 100,
    // });

    const logs = grouped.logs.map((log) => ({
      name: `${log.lastName}, ${log.firstName}`,
      date: formatDate(log.day, 'yyyy-MM-dd'),
      clockIn: log.clockIn
        ? formatDate(toZonedTime(log.clockIn, user.timezone), 'hh:mm a')
        : ' ',
      clockOut: log.clockIn
        ? formatDate(toZonedTime(log.clockIn, user.timezone), 'hh:mm a')
        : ' ',
      hours: (log.millis / 3600000).toFixed(2),
      status: log.status === DaylogStatus.pending ? 'no record' : log.status,
    }));
    autoTable(doc, {
      body: logs,
      columns: [
        { header: 'Employee Name', dataKey: 'name' },
        { header: 'Date', dataKey: 'date' },
        { header: 'Clock In', dataKey: 'clockIn' },
        { header: 'Clock Out', dataKey: 'clockOut' },
        { header: 'Total (Hours)', dataKey: 'hours' },
        // { header: 'Status', dataKey: 'status' },
      ],
    });

    return doc.output('arraybuffer');
  }

  async sendAttendanceReminders() {
    this.logger.log(`Sending Attendance Reminders`);

    const accounts = await this.prisma.account.findMany({
      include: {
        users: {
          where: { activatedAt: { not: null } },
          select: {
            firstName: true,
            lastName: true,
            timezone: true,
            email: true,
            attendances: {
              orderBy: { startDatetime: 'desc' },
              take: 1,
              select: { startDatetime: true, endDatetime: true },
            },
          },
        },
      },
    });

    for (const account of accounts) {
      this.logger.log(`Processing: ${account.accountName}`);

      for (const user of account.users) {
        const attendance = user.attendances.at(0);
        if (attendance?.startDatetime && !attendance?.endDatetime) {
          // Calculate the difference between the last clock in and current time in hours
          const difference =
            differenceInMinutes(new Date(), attendance.startDatetime) / 60;

          const modulus =
            (difference - AttendancesService.REMINDERS_HOURS_START) %
            AttendancesService.REMINDERS_HOURS_RECUR;
          const toSend = modulus > 0 && modulus < 0.5;

          if (!toSend) {
            continue;
          }

          const mailData =
            await this.mailBuilderService.buildClockOutReminderEmail(
              user.email,
              user.firstName,
              attendance.startDatetime,
              user.timezone,
            );

          await this.mailSender.send(mailData);
        }
      }
    }
  }
}
