import { Mapper } from '@/core/base/mapper';
import { UserEntity } from '@/core/domain/entities/user.entity';
import { CreateUserInputDto } from '@/shared/dtos/users/create-user-input.dto';

export class CreateUserMapper
  implements Mapper<CreateUserInputDto, UserEntity>
{
  public map(data: CreateUserInputDto): UserEntity {
    const user = new UserEntity();

    user.email = data.email;
    user.firstName = data.firstName;
    user.lastName = data.lastName;

    if (data.password) {
      user.password = data.password;
    }

    user.accountId = data.accountId ?? null;
    user.roleId = data.roleId!;

    return user;
  }
}
