import { EntityCount } from '@/core/base/entity';
import { TimeAwayTypeEntity } from '@/core/domain/entities/time-away-type.entity';
import { TimeAwayTypesRepository } from '@/core/repositories/time-away-types.repository';
import { ExtendedTransactionalAdapterPrisma } from '@/infra/data/prisma/prisma.service';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';

@Injectable()
export class PrismaTimeAwayTypesRepository implements TimeAwayTypesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(): Promise<TimeAwayTypeEntity> {
    throw new Error('Method not implemented.');
  }

  async findOne(): Promise<TimeAwayTypeEntity | null> {
    throw new Error('Method not implemented.');
  }

  @Transactional()
  async findAll(): Promise<EntityCount<TimeAwayTypeEntity>> {
    const data = await this.txHost.tx.timeAwayType.findMany();
    const count = await this.txHost.tx.timeAwayType.count();
    return { data, count };
  }

  async update(): Promise<TimeAwayTypeEntity> {
    throw new Error('Method not implemented.');
  }

  async remove(): Promise<void> {
    throw new Error('Method not implemented.');
  }
}
