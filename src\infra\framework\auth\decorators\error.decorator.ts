import { DefaultErrorMessageDto } from '@/shared/dtos/message/default-error-message.dto';
import { applyDecorators } from '@nestjs/common';
import { ApiDefaultResponse } from '@nestjs/swagger';

// for docs purposes only, will not test this decorator
export const ApiDefaultErrorMessage = () => {
  return applyDecorators(
    ApiDefaultResponse({
      description: 'Default error response',
      type: DefaultErrorMessageDto,
    }),
  );
};
