name: 'Cache Optimizer'
description: 'Optimize and report on caching strategies for dependencies and build artifacts'
inputs:
  cache-type:
    description: 'Type of cache (npm, docker, build-artifacts)'
    required: true
  cache-key:
    description: 'Primary cache key'
    required: true
  restore-keys:
    description: 'Fallback cache keys (newline separated)'
    required: false
  paths:
    description: 'Paths to cache (newline separated)'
    required: true
  enable-metrics:
    description: 'Enable cache metrics collection'
    required: false
    default: 'true'

outputs:
  cache-hit:
    description: 'Whether cache was hit (true/false)'
    value: ${{ steps.cache.outputs.cache-hit }}
  cache-size:
    description: 'Size of cached data in MB'
    value: ${{ steps.metrics.outputs.cache-size }}
  cache-efficiency:
    description: 'Cache efficiency rating'
    value: ${{ steps.metrics.outputs.efficiency }}
  time-saved:
    description: 'Estimated time saved by cache hit'
    value: ${{ steps.metrics.outputs.time-saved }}

runs:
  using: 'composite'
  steps:
    - name: Record cache operation start time
      id: start-time
      shell: bash
      run: echo "start=$(date +%s)" >> $GITHUB_OUTPUT

    - name: Restore cache with metrics
      id: cache
      uses: actions/cache@v4
      with:
        path: ${{ inputs.paths }}
        key: ${{ inputs.cache-key }}
        restore-keys: ${{ inputs.restore-keys }}
        enableCrossOsArchive: false
        fail-on-cache-miss: false
        lookup-only: false

    - name: Calculate cache metrics
      id: metrics
      if: inputs.enable-metrics == 'true'
      shell: bash
      run: |
        CACHE_HIT="${{ steps.cache.outputs.cache-hit }}"
        START_TIME=${{ steps.start-time.outputs.start }}
        END_TIME=$(date +%s)
        OPERATION_TIME=$((END_TIME - START_TIME))

        echo "📦 Cache Operation Metrics:"
        echo "  Type: ${{ inputs.cache-type }}"
        echo "  Cache Hit: $CACHE_HIT"
        echo "  Operation Time: ${OPERATION_TIME}s"

        # Calculate cache size if paths exist
        CACHE_SIZE=0
        if [ "$CACHE_HIT" = "true" ]; then
          # Calculate size of cached paths
          PATHS="${{ inputs.paths }}"
          while IFS= read -r path; do
            if [ -n "$path" ] && [ -e "$path" ]; then
              SIZE=$(du -sm "$path" 2>/dev/null | cut -f1 || echo "0")
              CACHE_SIZE=$((CACHE_SIZE + SIZE))
            fi
          done <<< "$PATHS"
          
          echo "  Cache Size: ${CACHE_SIZE}MB"
          echo "cache-size=$CACHE_SIZE" >> $GITHUB_OUTPUT
          
          # Estimate time saved based on cache type and size
          case "${{ inputs.cache-type }}" in
            "npm")
              # npm install typically takes 30-120 seconds depending on size
              if [ $CACHE_SIZE -gt 500 ]; then
                TIME_SAVED="90-120s"
              elif [ $CACHE_SIZE -gt 100 ]; then
                TIME_SAVED="45-90s"
              else
                TIME_SAVED="15-45s"
              fi
              ;;
            "docker")
              # Docker layer cache can save significant build time
              if [ $CACHE_SIZE -gt 1000 ]; then
                TIME_SAVED="5-10min"
              elif [ $CACHE_SIZE -gt 200 ]; then
                TIME_SAVED="2-5min"
              else
                TIME_SAVED="30s-2min"
              fi
              ;;
            "build-artifacts")
              # Build artifacts cache saves compilation time
              if [ $CACHE_SIZE -gt 100 ]; then
                TIME_SAVED="1-3min"
              else
                TIME_SAVED="15-60s"
              fi
              ;;
            *)
              TIME_SAVED="Unknown"
              ;;
          esac
          
          echo "  Estimated Time Saved: $TIME_SAVED"
          echo "time-saved=$TIME_SAVED" >> $GITHUB_OUTPUT
          
          # Determine efficiency rating
          if [ $OPERATION_TIME -lt 10 ] && [ $CACHE_SIZE -gt 50 ]; then
            EFFICIENCY="Excellent"
          elif [ $OPERATION_TIME -lt 30 ] && [ $CACHE_SIZE -gt 10 ]; then
            EFFICIENCY="Good"
          elif [ $CACHE_SIZE -gt 0 ]; then
            EFFICIENCY="Moderate"
          else
            EFFICIENCY="Poor"
          fi
          
        else
          echo "  Cache Miss - No time saved"
          echo "cache-size=0" >> $GITHUB_OUTPUT
          echo "time-saved=0s" >> $GITHUB_OUTPUT
          EFFICIENCY="Miss"
        fi

        echo "  Efficiency: $EFFICIENCY"
        echo "efficiency=$EFFICIENCY" >> $GITHUB_OUTPUT

    - name: Generate cache optimization recommendations
      shell: bash
      run: |
        CACHE_HIT="${{ steps.cache.outputs.cache-hit }}"
        CACHE_SIZE="${{ steps.metrics.outputs.cache-size }}"
        EFFICIENCY="${{ steps.metrics.outputs.efficiency }}"

        echo "💡 Cache Optimization Recommendations for ${{ inputs.cache-type }}:"

        if [ "$CACHE_HIT" = "true" ]; then
          echo "  ✅ Cache hit successful!"
          
          case "$EFFICIENCY" in
            "Excellent")
              echo "  🚀 Cache performance is excellent - no changes needed"
              ;;
            "Good")
              echo "  ✅ Cache performance is good"
              echo "  💡 Consider: Review cache key specificity to improve hit rate"
              ;;
            "Moderate")
              echo "  ⚠️ Cache performance could be improved"
              echo "  💡 Consider: Optimize cache key strategy"
              echo "  💡 Consider: Review cached paths for unnecessary files"
              ;;
            "Poor")
              echo "  ❌ Cache performance is poor"
              echo "  💡 Recommendation: Review cache key generation logic"
              echo "  💡 Recommendation: Verify cached paths are correct"
              echo "  💡 Recommendation: Consider cache size optimization"
              ;;
          esac
          
          # Size-based recommendations
          if [ "$CACHE_SIZE" -gt 1000 ]; then
            echo "  💡 Large cache detected (${CACHE_SIZE}MB) - consider cleanup strategies"
          fi
          
        else
          echo "  ❌ Cache miss occurred"
          echo "  💡 Recommendations:"
          echo "    - Verify cache key includes all relevant dependencies"
          echo "    - Check if restore-keys are properly configured"
          echo "    - Ensure cached paths exist and are correct"
          echo "    - Consider cache warming strategies for new branches"
          
          case "${{ inputs.cache-type }}" in
            "npm")
              echo "    - Verify package-lock.json is included in cache key"
              echo "    - Consider using npm ci instead of npm install"
              ;;
            "docker")
              echo "    - Ensure Docker buildx cache is properly configured"
              echo "    - Consider multi-stage build optimization"
              ;;
            "build-artifacts")
              echo "    - Verify source files are included in cache key"
              echo "    - Consider incremental build strategies"
              ;;
          esac
        fi

    - name: Store cache metrics
      if: inputs.enable-metrics == 'true'
      shell: bash
      run: |
        # Create cache metrics file
        mkdir -p .github/performance-metrics/cache
        METRICS_FILE=".github/performance-metrics/cache/${{ inputs.cache-type }}-$(date +%Y%m%d-%H%M%S).json"

        CACHE_METRICS=$(cat << EOF
        {
          "type": "${{ inputs.cache-type }}",
          "cacheHit": ${{ steps.cache.outputs.cache-hit }},
          "cacheKey": "${{ inputs.cache-key }}",
          "cacheSize": ${{ steps.metrics.outputs.cache-size }},
          "efficiency": "${{ steps.metrics.outputs.efficiency }}",
          "timeSaved": "${{ steps.metrics.outputs.time-saved }}",
          "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
          "workflow": {
            "runId": "${{ github.run_id }}",
            "runNumber": "${{ github.run_number }}",
            "branch": "${{ github.ref_name }}",
            "commit": "${{ github.sha }}"
          }
        }
        EOF
        )

        echo "$CACHE_METRICS" > "$METRICS_FILE"
        echo "📁 Cache metrics stored in: $METRICS_FILE"

        # Append to historical cache data
        HISTORY_FILE=".github/performance-metrics/cache-history.jsonl"
        echo "$CACHE_METRICS" >> "$HISTORY_FILE"

    - name: Create cache summary
      shell: bash
      run: |
        cat << EOF > cache-summary-${{ inputs.cache-type }}.md
        # Cache Summary: ${{ inputs.cache-type }}

        ## Results
        - **Cache Hit**: ${{ steps.cache.outputs.cache-hit }}
        - **Cache Size**: ${{ steps.metrics.outputs.cache-size }}MB
        - **Efficiency**: ${{ steps.metrics.outputs.efficiency }}
        - **Time Saved**: ${{ steps.metrics.outputs.time-saved }}

        ## Cache Configuration
        - **Primary Key**: \`${{ inputs.cache-key }}\`
        - **Restore Keys**: 
        $(echo "${{ inputs.restore-keys }}" | sed 's/^/  - `/' | sed 's/$/`/')

        ## Cached Paths
        $(echo "${{ inputs.paths }}" | sed 's/^/- `/' | sed 's/$/`/')

        ## Performance Impact
        $(if [ "${{ steps.cache.outputs.cache-hit }}" = "true" ]; then
          echo "✅ **Positive Impact**: Cache hit reduced build time by approximately ${{ steps.metrics.outputs.time-saved }}"
        else
          echo "❌ **No Impact**: Cache miss - full rebuild required"
        fi)
        EOF

        echo "📄 Cache summary generated: cache-summary-${{ inputs.cache-type }}.md"
