import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { Entity, EntityCount } from './entity';

type EntityPrimitives = string | number | Date | undefined | boolean | null;

export type PickRelation<T> = {
  [P in keyof T as T[P] extends EntityPrimitives ? never : P]?: boolean;
};

export type OmitRelation<T> = {
  [P in keyof T as T[P] extends EntityPrimitives ? P : never]: T[P];
};

export abstract class Repository<TEntity extends Entity, FindFilter, FindSort> {
  abstract create(data: TEntity): Promise<TEntity>;
  abstract findOne(
    filter: FindFilter,
    include?: PickRelation<TEntity>,
    sort?: FindSort,
  ): Promise<TEntity | null>;
  abstract findAll(
    filter: FindFilter,
    paginationMeta?: PaginationMetaDto,
    include?: PickRelation<TEntity>,
    sort?: FindSort,
  ): Promise<EntityCount<TEntity>>;
  abstract update(id: string, data: Partial<TEntity>): Promise<TEntity>;
  abstract remove(id: string): Promise<void>;
}
