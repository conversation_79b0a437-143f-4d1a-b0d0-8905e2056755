import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { fileMeta } from '../files/file-meta.dto';
import { createdRole } from '../roles/created-role.dto';
import { createdUserStatus } from '../user-statuses/created-user-status.dto';

export const createdUser = z
  .object({
    id: IdType.describe('The unique identifier of the user'),
    email: z.string().email().describe('The email address of the user'),
    firstName: z.string().describe('The first name of the user'),
    lastName: z.string().describe('The last name of the user'),
    timezone: z.string(),
    accountId: IdType.describe('The reference id to the account').nullish(),
    roleId: IdType.describe('The reference id to the role'),
    profilePictureId: IdType.describe(
      'The reference id to the profilePicture',
    ).nullish(),
    activatedAt: z.string().datetime().nullable(),
  })
  .describe('The representation of the user that exists in the database');
export class CreatedUserDto extends createZodDto(createdUser) {}

export const createdUserRelationship = createdUser.extend({
  role: createdRole.optional(),
  profilePicture: fileMeta.nullish(),
  userStatus: createdUserStatus.nullish(),
  profile: z.object({ jobTitle: z.string().nullable() }).optional(),
});

export type CreatedUserRelationshipDto = z.infer<
  typeof createdUserRelationship
>;
