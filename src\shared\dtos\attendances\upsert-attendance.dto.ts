import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdAttendance } from './created-attendance.dto';
import { IdType } from '../base/base.dto';

export const individualAttendance = createdAttendance.shape.in
  .omit({ picture: true })
  .extend({ pictureId: IdType });

export const upsertAttendance = createdAttendance
  .omit({ day: true, millis: true })
  .extend({ in: individualAttendance, out: individualAttendance })
  .partial()
  .required({ userId: true });
export class UpsertAttendanceDto extends createZodDto(upsertAttendance) {}
