# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
coverage/

# Development files
.git/
.gitignore
.husky/
.vscode/
.github/
.kiro/

# Documentation
*.md
LICENSE
CHANGELOG*

# Configuration files
.env*
!.env.example
.sonarcloud*
.editorconfig
commitlint*
.eslintrc*
.prettierrc*
.lintstagedrc*

# Docker files
docker-compose*
Dockerfile*
.dockerignore

# Test files
test/
**/*.test.ts
**/*.spec.ts
jest*.json

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# IDE files
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# GraphQL files
*.gql
*.graphql

# Backup files
*.bak
*.backup