import { z } from 'zod';

export const paging = z.object({
  count: z
    .number()
    .describe('The number of queried objects in the current page'),
  currentPage: z.number().describe('The current page number of the pagination'),
  nextPage: z.number().nullable().describe('The next page number, if existing'),
  prevPage: z
    .number()
    .nullable()
    .describe('The next previous number, if existing'),
  totalPages: z.number().describe('The total number of available pages'),
  totalCount: z.number().describe('The total number of entities of the query'),
});
export type Paging = z.infer<typeof paging>;

export const paginateSchema = <Schema extends z.ZodTypeAny>(schema: Schema) =>
  z.object({
    data: z.array(schema),
    paging: paging,
  });
