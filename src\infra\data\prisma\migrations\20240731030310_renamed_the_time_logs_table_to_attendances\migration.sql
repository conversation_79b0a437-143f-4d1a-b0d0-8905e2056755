/*
  Warnings:

  - You are about to drop the `time_logs` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropFore<PERSON>Key
ALTER TABLE "time_logs" DROP CONSTRAINT "time_logs_end_picture_id_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "time_logs" DROP CONSTRAINT "time_logs_start_picture_id_fkey";

-- DropForeignKey
ALTER TABLE "time_logs" DROP CONSTRAINT "time_logs_user_id_fkey";

-- DropTable
DROP TABLE "time_logs";

-- CreateTable
CREATE TABLE "attendances" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "start_lat" DOUBLE PRECISION NOT NULL,
    "start_long" DOUBLE PRECISION NOT NULL,
    "start_datetime" TIMESTAMP(3) NOT NULL,
    "start_picture_id" TEXT NOT NULL,
    "end_lat" DOUBLE PRECISION,
    "end_long" DOUBLE PRECISION,
    "end_datetime" TIMESTAMP(3),
    "end_picture_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "attendances_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "attendances_start_picture_id_key" ON "attendances"("start_picture_id");

-- CreateIndex
CREATE UNIQUE INDEX "attendances_end_picture_id_key" ON "attendances"("end_picture_id");

-- AddForeignKey
ALTER TABLE "attendances" ADD CONSTRAINT "attendances_start_picture_id_fkey" FOREIGN KEY ("start_picture_id") REFERENCES "files"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "attendances" ADD CONSTRAINT "attendances_end_picture_id_fkey" FOREIGN KEY ("end_picture_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "attendances" ADD CONSTRAINT "attendances_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
