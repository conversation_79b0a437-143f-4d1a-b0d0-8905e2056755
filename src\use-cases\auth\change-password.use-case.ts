import { <PERSON>ail<PERSON><PERSON> } from '@/core/abstracts/email-sender';
import { OtpGenerator } from '@/core/abstracts/otp-generator';
import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { UseCase } from '@/core/base/use-case';
import { OtpPurpose } from '@/core/enums/otp-purpose.enum';
import { InvalidCredentialException } from '@/core/exceptions/opus-exceptions';
import { UsersRepository } from '@/core/repositories/users.repository';
import { MailBuilderService } from '@/infra/framework/mailer/mail-builder.service';
import { ChangePasswordInputDto } from '@/shared/dtos/auth/change-password-input.dto';
import { MessageDto } from '@/shared/dtos/message/message.dto';
import { CreatedUserFullDto } from '@/shared/dtos/users/created-user-full.dto';

export class ChangePasswordUseCase implements UseCase<MessageDto> {
  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly hasher: PasswordHasher,
    private readonly otpGenerator: OtpGenerator,
    private readonly emailSender: EmailSender,
    private readonly mailBuilderService: MailBuilderService,
  ) {}

  public async execute(
    requester: CreatedUserFullDto,
    input: ChangePasswordInputDto,
  ): Promise<MessageDto> {
    if (
      !(await this.otpGenerator.validate(
        input.otp,
        requester.id,
        OtpPurpose.ChangePassword,
      ))
    ) {
      throw new InvalidCredentialException('Invalid OTP');
    }

    if (
      requester.password &&
      !(await this.hasher.verifyHash(
        input.oldPassword ?? '',
        requester.password,
      ))
    ) {
      throw new InvalidCredentialException('Old password is incorrect');
    }

    await this.usersRepository.update(requester.id, {
      password: await this.hasher.hash(input.newPassword),
      lastPasswordChange: new Date(),
    });

    const mailData = await this.mailBuilderService.buildPasswordChangeEmail(
      requester.email,
    );

    void this.emailSender.send(mailData);

    return { message: 'Password changed' };
  }
}
