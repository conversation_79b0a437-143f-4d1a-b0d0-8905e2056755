import {
  createdProfile,
  CreatedProfileDto,
} from '@/shared/dtos/profiles/created-profile.dto';
import { Gender, MaritalStatus } from '@prisma/client';
import { format } from 'date-fns/format';

export function profileMapper(input: {
  id: string;
  createdAt: Date;
  birthDate: Date | null;
  maritalStatus: MaritalStatus | null;
  gender: Gender | null;
  addressStreet1: string | null;
  addressStreet2: string | null;
  city: string | null;
  province: string | null;
  postalCode: string | null;
  country: string | null;
  workPhone: string | null;
  mobilePhone: string | null;
  homePhone: string | null;
  personalEmail: string | null;
  emergencyName: string | null;
  emergencyRelationship: string | null;
  emergencyMobilePhone: string | null;
  jobTitle: string | null;
  employmentStatus: string | null;
  departmentId: string | null;
  contractId: string | null;
  joinDate: Date;
  accountId: string | null;
  firstName: string;
  lastName: string;
  profilePictureId: string | null;
  activatedAt: Date | null;
  workEmail: string;
  department: {
    headUser: {
      user: {
        id: string;
        firstName: string;
        lastName: string;
      };
    } | null;
    createdAt: Date;
    departmentName: string;
    accountId: string;
    updatedAt: Date;
    deletedAt: Date | null;
    headId: string | null;
  } | null;
  profilePicture: {
    url: string;
    id: string;
    permission: string;
    fileName: string;
    contentType: string;
    uploaderId: string;
    originalFileName: string;
    fileSize: number;
  } | null;
}): CreatedProfileDto {
  const { birthDate, joinDate, activatedAt, department, ...rest } = input;
  const headUser = department?.headUser?.user;

  return createdProfile.parse({
    ...rest,
    activatedAt: activatedAt ? activatedAt.toISOString() : null,
    createdAt: format(joinDate, 'yyyy-MM-dd'),
    birthDate: birthDate ? format(birthDate, 'yyyy-MM-dd') : null,
    profilePicture: rest.profilePicture,
    department: department
      ? {
          ...department,
          headUser: headUser ? headUser : null,
        }
      : null,
  } as CreatedProfileDto);
}
