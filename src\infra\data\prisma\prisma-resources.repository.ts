import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { EntityConflictException } from '@/core/exceptions/opus-exceptions';
import { FindResourceFilterDto } from '@/shared/dtos/resources/find-resource-filter.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { ExtendedTransactionalAdapterPrisma } from './prisma.service';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { ResourceEntity } from '@/core/domain/entities/resources.entity';

@Injectable()
export class PrismaResourcesRepository implements ResourcesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: ResourceEntity): Promise<ResourceEntity> {
    try {
      await this.txHost.tx.resource.findFirst();
      const resource = await this.txHost.tx.resource.create({
        data: {
          ...ResourcesRepository.removeRelationships(data),
        },
        include: { attachments: true, uploader: true },
      });

      return resource;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }

      throw error;
    }
  }

  async findOne(
    filter: FindResourceFilterDto,
    include?: PickRelation<ResourceEntity> | undefined,
  ): Promise<ResourceEntity | null> {
    return this.txHost.tx.resource.findFirst({
      where: this.processFilter(filter),
      include: { ...include, attachments: true, uploader: true },
      orderBy: this.processSort(),
    });
  }

  @Transactional()
  async findAll(
    filter: FindResourceFilterDto,
    paginationMeta?: PaginationMetaDto,
    include?: PickRelation<ResourceEntity> | undefined,
  ): Promise<EntityCount<ResourceEntity>> {
    const data = await this.txHost.tx.resource.findMany({
      where: this.processFilter(filter),
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      include: { ...include, attachments: true, uploader: true },
      orderBy: this.processSort(),
    });
    const count = await this.txHost.tx.resource.count({
      where: this.processFilter(filter),
    });

    return { data, count };
  }

  async update(
    id: string,
    data: Partial<ResourceEntity>,
  ): Promise<ResourceEntity> {
    try {
      return this.txHost.tx.resource.update({
        where: { id },
        data: ResourcesRepository.removeRelationships(data as ResourceEntity),
        include: { attachments: true, uploader: true },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2025':
            throw new EntityConflictException('Resource does not exist');
        }
      }
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    await this.txHost.tx.resource.delete({
      where: { id },
    });
  }

  private processFilter(
    filter: FindResourceFilterDto,
  ): Prisma.ResourceWhereInput {
    const { accountId, isPublished, startDate, endDate, text, ...rest } =
      filter;
    let where: Prisma.ResourceWhereInput = {
      ...rest,
      uploader: { accountId },
    };

    if (isPublished === true) {
      where = {
        ...where,
        publishDate: { gte: startDate, lte: endDate, not: null },
      };
    } else if (isPublished === false) {
      where = {
        ...where,
        publishDate: null,
        createdAt: { gte: startDate, lte: endDate },
      };
    } else {
      where = {
        ...where,
        OR: [
          {
            publishDate: null,
            createdAt: { gte: startDate, lte: endDate },
          },
          { publishDate: { gte: startDate, lte: endDate, not: null } },
        ],
      };
    }

    if (text) {
      where = {
        ...where,
        OR: [
          { title: { contains: text, mode: 'insensitive' } },
          { content: { contains: text, mode: 'insensitive' } },
        ],
      };
    }

    return where;
  }

  private processSort(): Prisma.ResourceOrderByWithRelationInput {
    return { publishDate: 'desc' };
  }
}
