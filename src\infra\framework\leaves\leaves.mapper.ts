import {
  ApproverDetails,
  createdTime<PERSON>way,
  CreatedTimeAwayDto,
} from '@/shared/dtos/time-aways/created-time-away.dto';
import {
  timeAwayHistory,
  TimeAwayHistoryDto,
} from '@/shared/dtos/time-aways/time-away-history-paginated.dto';
import { TimeAwayApproverType } from '@prisma/client';

export type LeaveMapperInput = {
  id: string;
  createdAt: Date;
  userId: string;
  isUserRequest: boolean;
  hours: number[];
  startDate: Date;
  endDate: Date;
  timeAwayTypeId: string;
  isApproved: boolean | null;
  reason: string | null;
  approvalMessage: string | null;
  isSubtract: boolean;
  accrualDate: Date | null;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    profileInfo: {
      department: {
        id: string;
        headUser: {
          user: {
            id: string;
            firstName: string;
            lastName: string;
          };
        } | null;
      } | null;
    } | null;
  };
  approvalHistory: {
    id: string;
    checkListAnswers: {
      id: string;
      answer: boolean | null;
      checkListItem: {
        id: string;
        question: string;
        approvalHierarchyId: string | null;
        order: number;
      };
    }[];
    approver: {
      id: string;
      approverType: TimeAwayApproverType;
      approverUser: {
        id: string;
        firstName: string;
        lastName: string;
      } | null;
      approverRole: {
        id: string;
        title: string;
      } | null;
    };
    message: string | null;
    isApproved: boolean | null;
    approverUser: {
      id: string;
      firstName: string;
      lastName: string;
    } | null;
    timeAwayId: string | null;
  }[];
  timeAwayType: { description: string };
  finalApproverUser: {
    id: string;
    firstName: string;
    lastName: string;
  } | null;
};

export function leaveMapper(input: LeaveMapperInput): CreatedTimeAwayDto {
  let currentApproverIndex = 0;

  for (let i = 0; i < input.approvalHistory.length; i++) {
    const current = input.approvalHistory[i];

    // Skip if there is no department head
    if (current.approver.approverType === 'DEPARTMENT_HEAD') {
      console.log(input.user.profileInfo?.department?.headUser?.user.firstName);
      if (!input.user.profileInfo?.department?.headUser?.user.id) {
        continue;
      }
    }

    if (input.isApproved === null) {
      if (current.isApproved === null) {
        currentApproverIndex = i;
        break;
      }
    } else {
      if (current.isApproved !== null) {
        currentApproverIndex = i;
      }
    }
  }

  return createdTimeAway.parse({
    ...input,
    startDate: input.startDate.toISOString(),
    endDate: input.endDate.toISOString(),
    createdAt: input.createdAt.toISOString(),
    approvalHistory: input.approvalHistory.map((item) => {
      let approverDetails: ApproverDetails;
      switch (item.approver.approverType) {
        case 'DEPARTMENT_HEAD':
          const headUser = input.user.profileInfo?.department?.headUser?.user;
          approverDetails = {
            approverType: 'DEPARTMENT_HEAD',
            user: headUser ? headUser : null,
          };
          break;
        case 'SPECIFIC_PERSON':
          approverDetails = {
            approverType: 'SPECIFIC_PERSON',
            user: item.approver.approverUser,
          };
          break;
        case 'SPECIFIC_ROLE':
          approverDetails = {
            approverType: 'SPECIFIC_ROLE',
            role: item.approver.approverRole,
          };
          break;
        default:
          approverDetails = {
            approverType: 'SPECIFIC_PERSON',
            user: null,
          };
      }
      return {
        id: item.id,
        approverDetails: approverDetails,
        answers: item.checkListAnswers.map((answer) => ({
          id: answer.id,
          checkListItemId: answer.checkListItem.id,
          answer: answer.answer,
        })),
        approverUser: item.approverUser
          ? {
              id: item.approverUser.id,
              firstName: item.approverUser.firstName,
              lastName: item.approverUser.lastName,
            }
          : null,
        isApproved: item.isApproved,
        message: item.message,
      };
    }),
    checklist:
      input.approvalHistory.length > 0
        ? input.approvalHistory[0]?.checkListAnswers.map(
            (answer) => answer.checkListItem,
          )
        : [],
    currentApproverIndex,
    finalApproverUser: input.finalApproverUser,
  } as CreatedTimeAwayDto);
}

export function leaveHistoryMapper(
  input: LeaveMapperInput,
): TimeAwayHistoryDto {
  return timeAwayHistory.parse({
    ...leaveMapper(input),
    hours:
      input.hours.reduce(
        (sum, curr) => Math.round((sum + curr) * 100000) / 100000,
        0,
      ) * (input.isSubtract ? -1 : 1),
    timeAwayType: input.timeAwayType.description,
  } as TimeAwayHistoryDto);
}
