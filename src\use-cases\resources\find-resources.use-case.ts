import { UseCase } from '@/core/base/use-case';
import { CreatedResourceMapper } from '@/core/domain/mappers/resources/created-resource.mapper';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { PaginatedCreateResourceDto } from '@/shared/dtos/resources/created-resource-paginated.dto';
import { FindResourceFilterDto } from '@/shared/dtos/resources/find-resource-filter.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';

export class FindResourcesUseCase
  implements UseCase<PaginatedCreateResourceDto>
{
  private createdResourceMapper: CreatedResourceMapper;

  constructor(private readonly resourcesRepository: ResourcesRepository) {
    this.createdResourceMapper = new CreatedResourceMapper();
  }

  public async execute(
    filter: FindResourceFilterDto,
    paginationMeta: PaginationMetaDto,
  ): Promise<PaginatedCreateResourceDto> {
    const { data, count } = await this.resourcesRepository.findAll(
      filter,
      paginationMeta,
    );

    return {
      data: data.map(this.createdResourceMapper.map),
      paging: getPaginationDetails(data, paginationMeta, count),
    };
  }
}
