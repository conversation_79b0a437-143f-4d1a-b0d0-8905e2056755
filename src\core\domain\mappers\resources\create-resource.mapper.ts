import { Mapper } from '@/core/base/mapper';
import { CreateResourceDto } from '@/shared/dtos/resources/create-resource.dto';
import { ResourceEntity } from '../../entities/resources.entity';

export class CreateResourceMapper extends Mapper<
  CreateResourceDto,
  ResourceEntity
> {
  public map(data: CreateResourceDto): ResourceEntity {
    const resource = new ResourceEntity();
    resource.accountId = data.accountId;
    resource.uploaderId = data.uploaderId;
    resource.content = data.content;
    resource.resourceType = data.resourceType;
    resource.title = data.title;

    if (!data.draft) {
      resource.publishDate = new Date();
    }

    return resource;
  }
}
