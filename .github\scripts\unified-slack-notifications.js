/**
 * Unified Slack Notification System for CI/CD Pipeline
 *
 * Consolidates functionality from multiple notification scripts:
 * - slack-notifications-complete.js
 * - slack-notifications.js
 * - slack-formatting.js
 * - slack-escalation.js
 * - slack-templates.js
 *
 * Features:
 * - Rich formatting with blocks and attachments
 * - Threading support for conversation continuity
 * - Error handling with retry logic and exponential backoff
 * - Escalation management with severity-based notifications
 * - Template-based message generation
 * - Comprehensive CI/CD pipeline support
 */

const https = require('https');
const fs = require('fs');

class UnifiedSlackNotifier {
  constructor(webhookUrl, options = {}) {
    this.webhookUrl = webhookUrl;
    this.options = {
      channel: options.channel || '#devops-pipeline',
      username: options.username || 'Pipeline Bot',
      iconEmoji: options.iconEmoji || ':robot_face:',
      ...options,
    };

    // Thread management
    this.threadTs = null;
    this.contextFile = process.env.GITHUB_WORKSPACE
      ? `${process.env.GITHUB_WORKSPACE}/.slack-context.json`
      : null;

    // Escalation configuration
    this.escalationConfig = {
      enabled: options.escalationEnabled !== false,
      threshold: options.escalationThreshold || 2,
      delay: options.escalationDelay || 300,
      maxLevel: options.maxEscalationLevel || 5,
      retryAttempts: options.retryAttempts || 3,
    };

    this.escalationHistory = new Map();
  }

  // ============================================================================
  // CORE MESSAGING FUNCTIONALITY
  // ============================================================================

  /**
   * Send message to Slack with comprehensive error handling
   */
  async sendMessage(payload, retries = 3) {
    const messagePayload = {
      channel: this.options.channel,
      username: this.options.username,
      icon_emoji: this.options.iconEmoji,
      ...payload,
    };

    // Add thread_ts if available for threading
    if (this.threadTs) {
      messagePayload.thread_ts = this.threadTs;
    }

    const data = JSON.stringify(messagePayload);

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await this.makeRequest(data);

        if (response.ok || response.status === 200) {
          console.log(
            `✅ Slack message sent successfully (attempt ${attempt})`,
          );
          return response;
        } else {
          throw new Error(
            `Slack API error: ${response.error || 'Unknown error'}`,
          );
        }
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error.message);

        if (attempt === retries) {
          throw new Error(`Failed after ${retries} attempts: ${error.message}`);
        }

        // Exponential backoff with jitter
        const baseDelay = Math.pow(2, attempt) * 1000;
        const jitter = Math.random() * 1000;
        await this.sleep(baseDelay + jitter);
      }
    }
  }

  /**
   * Make HTTP request to Slack webhook
   */
  makeRequest(data) {
    return new Promise((resolve, reject) => {
      const url = new URL(this.webhookUrl);

      const options = {
        hostname: url.hostname,
        port: 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(data),
          'User-Agent': 'GitHub-Actions-Unified-Slack-Notifier/2.0',
        },
      };

      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => (responseData += chunk));
        res.on('end', () => {
          try {
            const parsed = responseData
              ? JSON.parse(responseData)
              : { ok: res.statusCode === 200 };
            resolve({ ...parsed, status: res.statusCode });
          } catch (error) {
            resolve({
              ok: res.statusCode === 200,
              status: res.statusCode,
              raw: responseData,
            });
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(15000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.write(data);
      req.end();
    });
  }

  /**
   * Sleep utility for delays
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Output values for GitHub Actions
   */
  outputGitHubActions(name, value) {
    if (process.env.GITHUB_OUTPUT) {
      try {
        // Escape any special characters in the value
        const escapedValue = String(value)
          .replace(/\n/g, '%0A')
          .replace(/\r/g, '%0D');
        fs.appendFileSync(
          process.env.GITHUB_OUTPUT,
          `${name}=${escapedValue}\n`,
        );
        console.log(`📝 GitHub Actions output: ${name}=${escapedValue}`);
      } catch (error) {
        console.warn(
          `⚠️ Failed to write GitHub Actions output: ${error.message}`,
        );
      }
    }
  }

  // ============================================================================
  // STATUS AND FORMATTING UTILITIES
  // ============================================================================

  /**
   * Enhanced status configurations
   */
  static STATUS_CONFIGS = {
    // Success states
    success: { emoji: '✅', text: 'Success', color: 'good', priority: 'low' },
    passed: { emoji: '✅', text: 'Passed', color: 'good', priority: 'low' },

    // Running/Progress states
    running: {
      emoji: '🔄',
      text: 'Running',
      color: '#36a64f',
      priority: 'low',
    },
    pending: {
      emoji: '⏳',
      text: 'Pending',
      color: '#36a64f',
      priority: 'low',
    },

    // Warning states
    warning: {
      emoji: '⚠️',
      text: 'Warning',
      color: 'warning',
      priority: 'medium',
    },
    unstable: {
      emoji: '⚠️',
      text: 'Unstable',
      color: 'warning',
      priority: 'medium',
    },

    // Failure states
    failure: { emoji: '❌', text: 'Failed', color: 'danger', priority: 'high' },
    failed: { emoji: '❌', text: 'Failed', color: 'danger', priority: 'high' },
    error: { emoji: '🚨', text: 'Error', color: 'danger', priority: 'high' },

    // Critical states
    critical: {
      emoji: '🔥',
      text: 'Critical',
      color: 'danger',
      priority: 'critical',
    },
    emergency: {
      emoji: '💥',
      text: 'Emergency',
      color: 'danger',
      priority: 'critical',
    },

    // Neutral states
    cancelled: {
      emoji: '⏹️',
      text: 'Cancelled',
      color: '#808080',
      priority: 'low',
    },
    skipped: {
      emoji: '⏭️',
      text: 'Skipped',
      color: '#808080',
      priority: 'low',
    },
    unknown: {
      emoji: '❓',
      text: 'Unknown',
      color: '#808080',
      priority: 'low',
    },
  };

  /**
   * Get status configuration with fallback
   */
  getStatusConfig(status) {
    return (
      UnifiedSlackNotifier.STATUS_CONFIGS[status] ||
      UnifiedSlackNotifier.STATUS_CONFIGS.unknown
    );
  }

  // ============================================================================
  // NOTIFICATION METHODS FOR DIFFERENT TYPES
  // ============================================================================

  /**
   * Send basic test notification
   */
  async sendBasicTest(data) {
    const { branch, commit, author, status = 'success' } = data;
    const statusConfig = this.getStatusConfig(status);
    const commitShort = commit ? commit.substring(0, 7) : 'unknown';

    const payload = {
      text: `${statusConfig.emoji} Basic test notification`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusConfig.emoji} *Basic Test Notification*\n\n*Branch:* ${branch}\n*Commit:* ${commitShort}\n*Author:* ${author}\n*Status:* ${statusConfig.text}`,
          },
        },
      ],
    };

    const response = await this.sendMessage(payload);

    // For webhook, simulate thread timestamp and output for GitHub Actions
    if (response.ok || response.status === 200) {
      this.threadTs = Date.now().toString();
      console.log(
        `📝 Deployment start message created with simulated thread ID: ${this.threadTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs);
      this.outputGitHubActions('message-ts', this.threadTs);
    }

    return response;
  }

  /**
   * Create main pipeline message (creates thread)
   */
  async createPipelineMessage(data) {
    const {
      branch,
      commit,
      author,
      environment = 'development',
      status = 'running',
      workflowUrl,
    } = data;

    const statusConfig = this.getStatusConfig(status);
    const commitShort = commit ? commit.substring(0, 7) : 'unknown';
    const envEmoji = environment === 'production' ? '🚀' : '🧪';

    const payload = {
      text: `${envEmoji} CI/CD Pipeline started`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${envEmoji} CI/CD Pipeline - ${environment.toUpperCase()}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Branch:*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit:*\n${commitShort}` },
            { type: 'mrkdwn', text: `*Author:*\n${author}` },
            { type: 'mrkdwn', text: `*Status:*\n${statusConfig.text}` },
          ],
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Started at ${new Date().toISOString()} | Environment: ${environment}`,
            },
          ],
        },
      ],
    };

    // Add action button if URL provided
    if (workflowUrl) {
      payload.blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Workflow',
              emoji: true,
            },
            url: workflowUrl,
            style: 'primary',
          },
        ],
      });
    }

    const response = await this.sendMessage(payload);

    // For webhook, simulate thread timestamp
    if (response.ok || response.status === 200) {
      this.threadTs = Date.now().toString();
      console.log(
        `📝 Main message created with simulated thread ID: ${this.threadTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs);
      this.outputGitHubActions('message-ts', this.threadTs);
    }

    return response;
  }

  /**
   * Send test results
   */
  async sendTestResults(data) {
    const {
      status = 'success',
      testsPassed = 0,
      testsFailed = 0,
      coverage,
    } = data;
    const statusConfig = this.getStatusConfig(status);
    const total = testsPassed + testsFailed;

    const payload = {
      text: `${statusConfig.emoji} Test Results`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusConfig.emoji} *Test Results*`,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Status:*\n${statusConfig.text}` },
            { type: 'mrkdwn', text: `*Passed:*\n${testsPassed}` },
            { type: 'mrkdwn', text: `*Failed:*\n${testsFailed}` },
            { type: 'mrkdwn', text: `*Total:*\n${total}` },
          ],
        },
      ],
    };

    if (coverage) {
      payload.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Coverage:* ${coverage}%`,
        },
      });
    }

    const response = await this.sendMessage(payload);

    // For webhook, simulate thread timestamp and output for GitHub Actions
    if (response.ok || response.status === 200) {
      this.threadTs = Date.now().toString();
      console.log(
        `📝 Deployment start message created with simulated thread ID: ${this.threadTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs);
      this.outputGitHubActions('message-ts', this.threadTs);
    }

    return response;
  }

  /**
   * Send build status
   */
  async sendBuildStatus(data) {
    const { status = 'success', duration = 'Unknown', buildDuration } = data;
    const statusConfig = this.getStatusConfig(status);
    const actualDuration = buildDuration || duration;

    const payload = {
      text: `${statusConfig.emoji} Build Status`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusConfig.emoji} *Build Status*`,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Status:*\n${statusConfig.text}` },
            { type: 'mrkdwn', text: `*Duration:*\n${actualDuration}` },
          ],
        },
      ],
    };

    const response = await this.sendMessage(payload);

    // For webhook, simulate thread timestamp and output for GitHub Actions
    if (response.ok || response.status === 200) {
      this.threadTs = Date.now().toString();
      console.log(
        `📝 Deployment start message created with simulated thread ID: ${this.threadTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs);
      this.outputGitHubActions('message-ts', this.threadTs);
    }

    return response;
  }

  /**
   * Send deployment status
   */
  async sendDeploymentStatus(data) {
    const {
      status = 'success',
      environment = 'staging',
      serviceUrl = '',
      duration = 'Unknown',
    } = data;

    const statusConfig = this.getStatusConfig(status);
    const envEmoji = environment === 'production' ? '🚀' : '🧪';

    const payload = {
      text: `${statusConfig.emoji} Deployment Status`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusConfig.emoji} *Deployment - ${environment.toUpperCase()}*`,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Status:*\n${statusConfig.text}` },
            {
              type: 'mrkdwn',
              text: `*Environment:*\n${environment.toUpperCase()}`,
            },
            { type: 'mrkdwn', text: `*Duration:*\n${duration}` },
          ],
        },
      ],
    };

    if (serviceUrl) {
      payload.blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: `Open ${environment} Service`,
              emoji: true,
            },
            url: serviceUrl,
          },
        ],
      });
    }

    const response = await this.sendMessage(payload);

    // Output thread-ts and message-ts for GitHub Actions if successful
    if (response.ok || response.status === 200) {
      // Use existing thread timestamp if available, otherwise create new one
      const messageTs = this.threadTs || Date.now().toString();
      console.log(
        `📝 Deployment failure message sent with thread ID: ${messageTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs || messageTs);
      this.outputGitHubActions('message-ts', messageTs);
    }

    return response;
  }

  /**
   * Send pipeline summary
   */
  async sendPipelineSummary(data) {
    const {
      status = 'success',
      duration = 'Unknown',
      branch,
      commit,
      environment,
    } = data;

    const statusConfig = this.getStatusConfig(status);
    const commitShort = commit ? commit.substring(0, 7) : 'unknown';

    const payload = {
      text: `${statusConfig.emoji} Pipeline Summary`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusConfig.emoji} *Pipeline Summary*`,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Overall Status:*\n${statusConfig.text}` },
            { type: 'mrkdwn', text: `*Duration:*\n${duration}` },
            { type: 'mrkdwn', text: `*Branch:*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit:*\n${commitShort}` },
          ],
        },
      ],
    };

    return await this.sendMessage(payload);
  }

  /**
   * Send failure escalation
   */
  async sendFailureEscalation(data) {
    const {
      stage,
      error,
      branch,
      commit,
      author,
      environment,
      severity = 'high',
      workflowUrl,
    } = data;

    const commitShort = commit ? commit.substring(0, 7) : 'unknown';

    const payload = {
      text: `🚨 PIPELINE FAILURE - IMMEDIATE ATTENTION REQUIRED`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🚨 PIPELINE FAILURE ESCALATION',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Stage:* ${stage}\n*Error:* ${error}\n*Severity:* ${severity.toUpperCase()}`,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Branch:*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit:*\n${commitShort}` },
            { type: 'mrkdwn', text: `*Author:*\n${author}` },
            { type: 'mrkdwn', text: `*Environment:*\n${environment}` },
          ],
        },
      ],
    };

    if (workflowUrl) {
      payload.blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Failed Workflow',
              emoji: true,
            },
            url: workflowUrl,
            style: 'danger',
          },
        ],
      });
    }

    return await this.sendMessage(payload);
  }

  /**
   * Send feature branch notification
   */
  async sendFeatureBranch(data) {
    const {
      branch,
      commit,
      author,
      status = 'success',
      message = '',
      workflowUrl = '',
      results = {},
    } = data;

    const statusConfig = this.getStatusConfig(status);
    const commitShort = commit ? commit.substring(0, 7) : 'unknown';

    // Determine overall status based on results
    const codeQualityStatus = results.codeQuality || 'unknown';
    const unitTestsStatus = results.unitTests || 'unknown';

    const codeQualityConfig = this.getStatusConfig(codeQualityStatus);
    const unitTestsConfig = this.getStatusConfig(unitTestsStatus);

    const payload = {
      text: `${statusConfig.emoji} Feature Branch CI - ${statusConfig.text.toUpperCase()}`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `🌿 Feature Branch CI - ${statusConfig.text.toUpperCase()}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Branch*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit*\n${commitShort}` },
            { type: 'mrkdwn', text: `*Author*\n${author}` },
            {
              type: 'mrkdwn',
              text: `*Code Quality*\n${codeQualityConfig.emoji} ${codeQualityConfig.text}`,
            },
          ],
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Unit Tests*\n${unitTestsConfig.emoji} ${unitTestsConfig.text}`,
            },
            {
              type: 'mrkdwn',
              text: `*Status*\n${statusConfig.emoji} ${statusConfig.text}`,
            },
          ],
        },
      ],
    };

    // Add commit message if available
    if (message && message.trim() !== '' && message !== 'No commit message') {
      payload.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Commit Message:*\n${message}`,
        },
      });
    }

    // Add workflow URL button if available
    if (workflowUrl) {
      payload.blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Workflow',
              emoji: true,
            },
            url: workflowUrl,
            style: status === 'success' ? 'primary' : 'danger',
          },
        ],
      });
    }

    return await this.sendMessage(payload);
  }

  /**
   * Send deployment start notification
   */
  async sendDeploymentStart(data) {
    const {
      branch,
      commit,
      author,
      message = '',
      workflowUrl = '',
      environment = 'unknown',
      trigger = 'unknown',
    } = data;

    const commitShort = commit ? commit.substring(0, 7) : 'unknown';
    const environmentEmoji =
      {
        production: '🔴',
        staging: '🟡',
        development: '🟢',
        hotfix: '🔥',
      }[environment] || '⚪';

    const payload = {
      text: `🚀 CI/CD Deployment Started - ${environment.toUpperCase()}`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `🚀 CI/CD Deployment Started`,
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Environment*\n${environmentEmoji} ${environment.toUpperCase()}`,
            },
            { type: 'mrkdwn', text: `*Branch*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit*\n${commitShort}` },
            { type: 'mrkdwn', text: `*Author*\n${author}` },
          ],
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Trigger*\n${trigger}` },
            { type: 'mrkdwn', text: `*Status*\n⏳ Initializing...` },
          ],
        },
      ],
    };

    // Add commit message if available
    if (
      message &&
      message.trim() !== '' &&
      message !== 'Manual deployment trigger'
    ) {
      payload.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Commit Message:*\n${message}`,
        },
      });
    }

    // Add workflow URL button
    if (workflowUrl) {
      payload.blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Workflow',
              emoji: true,
            },
            url: workflowUrl,
            style: 'primary',
          },
        ],
      });
    }

    const response = await this.sendMessage(payload);

    // For webhook, simulate thread timestamp and output for GitHub Actions
    if (response.ok || response.status === 200) {
      this.threadTs = Date.now().toString();
      console.log(
        `📝 Deployment start message created with simulated thread ID: ${this.threadTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs);
      this.outputGitHubActions('message-ts', this.threadTs);
    }

    return response;
  }

  /**
   * Send deployment success notification
   */
  async sendDeploymentSuccess(data) {
    const {
      branch,
      commit,
      author,
      message = '',
      workflowUrl = '',
      environment = 'unknown',
      serviceName = 'unknown',
      serviceUrl = '',
      dockerTags = '',
      resources = {},
      buildNumber = '',
      duration = '',
    } = data;

    const commitShort = commit ? commit.substring(0, 7) : 'unknown';
    const environmentEmoji =
      {
        production: '🔴',
        staging: '🟡',
        development: '🟢',
        hotfix: '🔥',
      }[environment] || '⚪';

    const payload = {
      text: `✅ Deployment Successful - ${environment.toUpperCase()}`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `🎉 Deployment Successful!`,
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Environment*\n${environmentEmoji} ${environment.toUpperCase()}`,
            },
            { type: 'mrkdwn', text: `*Service*\n${serviceName}` },
            { type: 'mrkdwn', text: `*Branch*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit*\n${commitShort}` },
          ],
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Author*\n${author}` },
            { type: 'mrkdwn', text: `*Build*\n#${buildNumber}` },
            { type: 'mrkdwn', text: `*Status*\n✅ Live & Running` },
            { type: 'mrkdwn', text: `*Docker Tags*\n${dockerTags}` },
          ],
        },
      ],
    };

    // Add resource information if available
    if (resources.cpu || resources.memory || resources.maxInstances) {
      payload.blocks.push({
        type: 'section',
        fields: [
          { type: 'mrkdwn', text: `*CPU*\n${resources.cpu || 'N/A'}` },
          { type: 'mrkdwn', text: `*Memory*\n${resources.memory || 'N/A'}` },
          {
            type: 'mrkdwn',
            text: `*Max Instances*\n${resources.maxInstances || 'N/A'}`,
          },
        ],
      });
    }

    // Add commit message if available
    if (
      message &&
      message.trim() !== '' &&
      message !== 'Manual deployment trigger'
    ) {
      payload.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Commit Message:*\n${message}`,
        },
      });
    }

    // Add action buttons
    const actionElements = [];

    if (workflowUrl) {
      actionElements.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'View Workflow',
          emoji: true,
        },
        url: workflowUrl,
        style: 'primary',
      });
    }

    if (serviceUrl && serviceUrl !== 'N/A') {
      actionElements.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'Open Service',
          emoji: true,
        },
        url: serviceUrl,
        style: 'primary',
      });
    }

    if (actionElements.length > 0) {
      payload.blocks.push({
        type: 'actions',
        elements: actionElements,
      });
    }

    const response = await this.sendMessage(payload);

    // Output thread-ts and message-ts for GitHub Actions if successful
    if (response.ok || response.status === 200) {
      // Use existing thread timestamp if available, otherwise create new one
      const messageTs = this.threadTs || Date.now().toString();
      console.log(
        `📝 Deployment success message sent with thread ID: ${messageTs}`,
      );

      // Output for GitHub Actions
      this.outputGitHubActions('thread-ts', this.threadTs || messageTs);
      this.outputGitHubActions('message-ts', messageTs);
    }

    return response;
  }

  /**
   * Send deployment failure notification
   */
  async sendDeploymentFailure(data) {
    const {
      branch,
      commit,
      author,
      message = '',
      workflowUrl = '',
      environment = 'unknown',
      serviceName = 'unknown',
      buildNumber = '',
      failedJob = 'unknown',
    } = data;

    const commitShort = commit ? commit.substring(0, 7) : 'unknown';
    const environmentEmoji =
      {
        production: '🔴',
        staging: '🟡',
        development: '🟢',
        hotfix: '🔥',
      }[environment] || '⚪';

    const payload = {
      text: `❌ Deployment Failed - ${environment.toUpperCase()}`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `💥 Deployment Failed!`,
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Environment*\n${environmentEmoji} ${environment.toUpperCase()}`,
            },
            { type: 'mrkdwn', text: `*Service*\n${serviceName}` },
            { type: 'mrkdwn', text: `*Branch*\n${branch}` },
            { type: 'mrkdwn', text: `*Commit*\n${commitShort}` },
          ],
        },
        {
          type: 'section',
          fields: [
            { type: 'mrkdwn', text: `*Author*\n${author}` },
            { type: 'mrkdwn', text: `*Build*\n#${buildNumber}` },
            { type: 'mrkdwn', text: `*Status*\n❌ Failed` },
            { type: 'mrkdwn', text: `*Failed Job*\n${failedJob}` },
          ],
        },
      ],
    };

    // Add commit message if available
    if (
      message &&
      message.trim() !== '' &&
      message !== 'Manual deployment trigger'
    ) {
      payload.blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Commit Message:*\n${message}`,
        },
      });
    }

    // Add workflow URL button
    if (workflowUrl) {
      payload.blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Failed Workflow',
              emoji: true,
            },
            url: workflowUrl,
            style: 'danger',
          },
        ],
      });
    }

    return await this.sendMessage(payload);
  }

  // ============================================================================
  // MAIN NOTIFICATION ROUTER
  // ============================================================================

  /**
   * Route notification to appropriate handler
   */
  async sendNotification(type, data) {
    console.log(
      `📤 Sending ${type} notification with data:`,
      JSON.stringify(data, null, 2),
    );

    // Set thread timestamp if provided for threading
    if (data.threadTs) {
      this.threadTs = data.threadTs;
      console.log(`🧵 Using thread timestamp: ${this.threadTs}`);
    }

    try {
      switch (type) {
        case 'basic-test':
          return await this.sendBasicTest(data);

        case 'create-pipeline':
          return await this.createPipelineMessage(data);

        case 'test-results':
          return await this.sendTestResults(data);

        case 'build-status':
          return await this.sendBuildStatus(data);

        case 'deployment-status':
          return await this.sendDeploymentStatus(data);

        case 'deployment-start':
          return await this.sendDeploymentStart(data);

        case 'deployment-success':
          return await this.sendDeploymentSuccess(data);

        case 'deployment-failure':
          return await this.sendDeploymentFailure(data);

        case 'pipeline-summary':
          return await this.sendPipelineSummary(data);

        case 'failure-escalation':
          return await this.sendFailureEscalation(data);

        case 'feature-branch':
          return await this.sendFeatureBranch(data);

        default:
          throw new Error(`Unknown notification type: ${type}`);
      }
    } catch (error) {
      console.error(`❌ Failed to send ${type} notification: ${error.message}`);
      throw error;
    }
  }
}

// ============================================================================
// MAIN EXECUTION
// ============================================================================

async function main() {
  try {
    // Get command line arguments
    const [, , notificationType, notificationDataStr] = process.argv;

    if (!notificationType) {
      console.error('❌ Error: Notification type is required');
      console.error(
        'Usage: node unified-slack-notifications.js <type> <data-json>',
      );
      process.exit(1);
    }

    // Get webhook URL from environment
    const webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!webhookUrl) {
      console.error(
        '❌ Error: SLACK_WEBHOOK_URL environment variable is required',
      );
      process.exit(1);
    }

    // Parse notification data
    let notificationData = {};
    if (notificationDataStr) {
      try {
        notificationData = JSON.parse(notificationDataStr);
      } catch (error) {
        console.error('❌ Error: Invalid JSON data provided:', error.message);
        console.error('Data received:', notificationDataStr);
        process.exit(1);
      }
    }

    // Create notifier instance
    const notifier = new UnifiedSlackNotifier(webhookUrl, {
      channel: process.env.SLACK_CHANNEL || '#devops-alerts',
    });

    // Send notification
    await notifier.sendNotification(notificationType, notificationData);

    console.log('✅ Notification sent successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Script execution failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Execute main function if this script is called directly
if (require.main === module) {
  main();
}

module.exports = UnifiedSlackNotifier;
