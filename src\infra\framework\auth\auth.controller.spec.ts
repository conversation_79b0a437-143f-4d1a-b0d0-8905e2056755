import { Test, TestingModule } from '@nestjs/testing';
import { Auth<PERSON>ontroller } from './auth.controller';
import { LoginGoogleUseCase } from '@/use-cases/auth/login-google.use-case';
import { LoginEmailUseCase } from '@/use-cases/auth/login-email.use-case';
import { RefreshTokenUseCase } from '@/use-cases/auth/refresh-token.use-case';
import { GenerateOtpUseCase } from '@/use-cases/auth/generate-otp.use-case';
import { ChangePasswordUseCase } from '@/use-cases/auth/change-password.use-case';
import { AuthService } from './auth.service';

/**
 * Test suite for AuthController
 * Tests the authentication controller functionality
 */
describe('AuthController', () => {
  let controller: AuthController;
  let loginGoogleUseCase: jest.Mocked<LoginGoogleUseCase>;
  let loginEmailUseCase: jest.Mocked<LoginEmailUseCase>;
  let refreshTokenUseCase: jest.Mocked<RefreshTokenUseCase>;
  let generateOtpUseCase: jest.Mocked<GenerateOtpUseCase>;
  let changePasswordUseCase: jest.Mocked<ChangePasswordUseCase>;
  let authService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    // Create mock implementations for all dependencies
    const mockLoginGoogleUseCase = {
      execute: jest.fn(),
    };

    const mockLoginEmailUseCase = {
      execute: jest.fn(),
    };

    const mockRefreshTokenUseCase = {
      execute: jest.fn(),
    };

    const mockGenerateOtpUseCase = {
      execute: jest.fn(),
    };

    const mockChangePasswordUseCase = {
      execute: jest.fn(),
    };

    const mockAuthService = {
      login: jest.fn(),
      activateUser: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: LoginGoogleUseCase,
          useValue: mockLoginGoogleUseCase,
        },
        {
          provide: LoginEmailUseCase,
          useValue: mockLoginEmailUseCase,
        },
        {
          provide: RefreshTokenUseCase,
          useValue: mockRefreshTokenUseCase,
        },
        {
          provide: GenerateOtpUseCase,
          useValue: mockGenerateOtpUseCase,
        },
        {
          provide: ChangePasswordUseCase,
          useValue: mockChangePasswordUseCase,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    loginGoogleUseCase = module.get(LoginGoogleUseCase);
    loginEmailUseCase = module.get(LoginEmailUseCase);
    refreshTokenUseCase = module.get(RefreshTokenUseCase);
    generateOtpUseCase = module.get(GenerateOtpUseCase);
    changePasswordUseCase = module.get(ChangePasswordUseCase);
    authService = module.get(AuthService);
  });

  /**
   * Test that the controller is properly instantiated
   */
  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /**
   * Test that all required dependencies are properly injected
   */
  it('should have all use cases and services injected', () => {
    expect(loginGoogleUseCase).toBeDefined();
    expect(loginEmailUseCase).toBeDefined();
    expect(refreshTokenUseCase).toBeDefined();
    expect(generateOtpUseCase).toBeDefined();
    expect(changePasswordUseCase).toBeDefined();
    expect(authService).toBeDefined();
  });
});
