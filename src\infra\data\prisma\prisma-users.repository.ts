import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { UserEntity } from '@/core/domain/entities/user.entity';
import { OpusRoles } from '@/core/enums/role.enum';
import {
  EntityConflictException,
  EntityNotFoundException,
} from '@/core/exceptions/opus-exceptions';
import { UsersRepository } from '@/core/repositories/users.repository';
import { ExtendedTransactionalAdapterPrisma } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { FindUserFilterDto } from '@/shared/dtos/users/find-user-filter.dto';
import { FindUserSortDto } from '@/shared/dtos/users/find-user-sort.dto';
import { objectEntries } from '@/shared/helpers/entries';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaUsersRepository implements UsersRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: UserEntity): Promise<UserEntity> {
    try {
      let roleId = data.roleId;
      if (!data.roleId) {
        const staffRole = await this.txHost.tx.role.findUnique({
          where: {
            title: OpusRoles.Staff,
          },
        });

        if (!staffRole) {
          throw new EntityConflictException('Roles have not been seeded');
        }

        roleId = staffRole.id;
      }

      const user = await this.txHost.tx.user.create({
        data: {
          ...UsersRepository.removeRelationships(data),
          roleId,
          userStatus: { create: {} },
          profile: { create: {} },
        },
        include: { role: true },
      });
      return user;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Email is already used');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }

      throw error;
    }
  }

  async findOne(
    filter: FindUserFilterDto,
    include: PickRelation<UserEntity> = {},
    sort: FindUserSortDto = {},
  ): Promise<UserEntity | null> {
    return this.txHost.tx.user.findFirst({
      where: filter,
      include: this.processInclude(include),
      orderBy: this.processSort(sort),
    });
  }

  @Transactional()
  async findAll(
    filter: FindUserFilterDto,
    paginationMeta?: PaginationMetaDto,
    include: PickRelation<UserEntity> = {},
    sort: FindUserSortDto = {},
  ): Promise<EntityCount<UserEntity>> {
    const data = await this.txHost.tx.user.findMany({
      where: filter,
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      include: this.processInclude(include),
      orderBy: this.processSort(sort),
    });
    const count = await this.txHost.tx.user.count({ where: filter });
    return { data, count };
  }

  async update(id: string, data: Partial<UserEntity>): Promise<UserEntity> {
    try {
      return await this.txHost.tx.user.update({
        where: { id },
        data: UsersRepository.removeRelationships(data as UserEntity),
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2025':
            throw new EntityConflictException('User does not exist');
        }
      }
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      await this.txHost.tx.user.delete({ where: { id } });
    } catch (error) {
      throw new EntityNotFoundException(`User ${id} does not exist`);
    }
  }

  private processSort(
    sort: FindUserSortDto,
  ): Prisma.UserOrderByWithRelationInput[] {
    return objectEntries(sort).map(([k, v]) => {
      return {
        [k]: v,
      };
    });
  }

  private processInclude(include: PickRelation<UserEntity>) {
    return include;
  }
}
