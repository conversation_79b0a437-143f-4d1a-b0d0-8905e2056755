import { EmailSender, MailData } from '@/core/abstracts/email-sender';
import { Injectable } from '@nestjs/common';
import { MailService } from '@sendgrid/mail';
import { LoggerService } from '../logger/logger.service';

@Injectable()
export class SendGridMailerService implements EmailSender {
  private readonly mailService: MailService;
  private readonly logger: LoggerService = new LoggerService(
    'SendGridMailerService',
  );

  constructor(private readonly sendGridApiKey: string) {
    this.mailService = new MailService();
    this.mailService.setApiKey(sendGridApiKey);
  }
  async send(mailData: MailData): Promise<boolean> {
    let success = true;
    try {
      await this.mailService.send({
        from: { email: '<EMAIL>', name: 'Opus Remote' },
        replyTo: '<EMAIL>',
        to: mailData.to,
        subject: mailData.subject,
        html: mailData.html,
        text: mailData.text ?? '',
        attachments: mailData.attachments?.map((attachment) => ({
          filename: attachment.fileName,
          type: attachment.contentType,
          content: attachment.content.toString('base64'),
        })),
      });
    } catch (error) {
      this.logger.error(error);
      success = false;
    }
    return success;
  }
}
