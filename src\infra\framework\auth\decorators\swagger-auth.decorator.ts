import { DefaultErrorMessageDto } from '@/shared/dtos/message/default-error-message.dto';
import { applyDecorators } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

// for docs purposes only, will not test this decorator
export const SwaggerAuth = () => {
  return applyDecorators(
    ApiBearerAuth(),
    ApiUnauthorizedResponse({
      description: 'Invalid credentials',
      type: DefaultErrorMessageDto,
    }),
    ApiForbiddenResponse({
      description: 'Insufficient access permission',
      type: DefaultErrorMessageDto,
    }),
  );
};
