# Since .env is gitignored, you can use .env.example to build a new `.env` file when you clone the repo.
# Keep this file up-to-date when you add new variables to \`.env\`.
NODE_ENV=staging

SENTRY_DSN=""
GATEWAY_PORT=3000

# URL variables
FILES_BASE_URL="http://localhost:3000/files"
API_URL="http://localhost:3000"
WEB_URL="http://localhost:8000"

# User Activation Variables
USER_ACTIVATION_REDIRECT_URL="http://localhost:4000/activate"

# Files manager variables
FILES_MANAGER="local"
GOOGLE_BUCKET_NAME=

# Auth related secrets
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
JWT_SECRET=
PASSWORD_SALT_ROUNDS=
GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT="<EMAIL>"

# Sendgrid Secrets
SENDGRID_API_KEY=


DATABASE_HOSTNAME=localhost
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=dev
DATABASE_PORT=5432
DATABASE_URL=postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOSTNAME}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public