import { Mapper } from '@/core/base/mapper';
import { CreateAccountInputDto } from '@/shared/dtos/accounts/create-account-input.dto';
import { AccountEntity } from '../../entities/account.entity';

export class CreateAccountMapper extends Mapper<
  CreateAccountInputDto,
  AccountEntity
> {
  public map(data: CreateAccountInputDto): AccountEntity {
    const account = new AccountEntity();

    account.accountName = data.accountName;

    return account;
  }
}
