import { SetMetadata } from '@nestjs/common';
import { REQUESTER_MATCH_KEY } from '../../constants';

export enum RequesterMatchType {
  UserId,
  AccountId,
  DepartmentId,
}

export interface RequesterMatchInfo {
  matchType: RequesterMatchType;
}

export interface UserIdLocation<InputDto> {
  userIdLocation: 'query' | 'params';
  userIdParamName: keyof InputDto;
}

export interface RequesterMatchProps<InputDto>
  extends UserIdLocation<InputDto> {
  OR?: RequesterMatchInfo[];
  AND?: RequesterMatchInfo[];
}

export const RequesterMatch = <T>(userAttributeMatch: RequesterMatchProps<T>) =>
  SetMetadata(REQUESTER_MATCH_KEY, userAttributeMatch);

export const UserIdMatch = <T>({
  userIdLocation,
  userIdParamName,
}: UserIdLocation<T>) =>
  RequesterMatch({
    userIdLocation,
    userIdParamName,
    AND: [{ matchType: RequesterMatchType.UserId }],
  });

export const UserIdOrAccountIdMatch = <T>({
  userIdLocation,
  userIdParamName,
}: UserIdLocation<T>) =>
  RequesterMatch({
    userIdLocation,
    userIdParamName,
    OR: [
      { matchType: RequesterMatchType.UserId },
      { matchType: RequesterMatchType.AccountId },
    ],
  });

export const AccountIdMatch = <T>({
  userIdLocation,
  userIdParamName,
}: UserIdLocation<T>) =>
  RequesterMatch({
    userIdLocation,
    userIdParamName,
    AND: [{ matchType: RequesterMatchType.AccountId }],
  });
