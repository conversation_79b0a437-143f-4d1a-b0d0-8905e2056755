import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';

export const externalCalendarEvents = z
  .object({
    id: z.string().nullish(),
    summary: z.string().nullish(),
    description: z.string().nullish(),
    start: z.string().datetime().nullish(),
    end: z.string().datetime().nullish(),
    link: z.string().url().datetime().nullish(),
    attendees: z.string().email().nullish().array().nullish(),
    country: z.string().optional(),
  })
  .array();

export class ExternalCalendarEventsDto extends createZodDto(
  externalCalendarEvents,
) {}
