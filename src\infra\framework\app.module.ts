import { ClsPluginTransactional } from '@nestjs-cls/transactional';
import { TransactionalAdapterPrisma } from '@nestjs-cls/transactional-adapter-prisma';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { ClsModule } from 'nestjs-cls';
import { PrismaModule } from '../data/prisma/prisma.module';
import { PrismaService } from '../data/prisma/prisma.service';
import { AccountsModule } from './accounts/accounts.module';
import { AttendancesModule } from './attendances/attendances.module';
import { AuthModule } from './auth/auth.module';
import { CalendarsModule } from './calendars/calendars.module';
import { FilesModule } from './files/files.module';
import { HasherModule } from './hasher/hasher.module';
import { OpusExceptionsInterceptor } from './interceptors/opus-exceptions.interceptor';
import { LeaveApprovalHierarchiesModule } from './leave-approval-hierarchies/leave-approval-hierarchies.module';
import { LeaveCreditsModule } from './leave-credits/leave-credits.module';
import { LeaveTypesModule } from './leave-types/leave-types.module';
import { LeavesModule } from './leaves/leaves.module';
import { MailerModule } from './mailer/mailer.module';
import { ZodValidationPipe } from './pipes/zod-validation.pipe';
import { ProfileModule } from './profile/profile.module';
import { ResourcesModule } from './resources/resources.module';
import { SocketsModule } from './sockets/sockets.module';
import { UsersModule } from './users/users.module';
import { DepartmentsModule } from './departments/departments.module';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    ScheduleModule.forRoot(),
    UsersModule,
    AuthModule,
    ConfigModule,
    FilesModule,
    AttendancesModule,
    SocketsModule,
    LeaveTypesModule,
    LeavesModule,
    MailerModule,
    LeaveCreditsModule,
    CalendarsModule,
    AccountsModule,
    ResourcesModule,
    ProfileModule,
    HasherModule,
    ClsModule.forRoot({
      plugins: [
        new ClsPluginTransactional({
          imports: [
            // module in which the PrismaClient is provided
            PrismaModule,
          ],
          adapter: new TransactionalAdapterPrisma({
            // the injection token of the PrismaClient
            prismaInjectionToken: PrismaService,
          }),
        }),
      ],
    }),
    LeaveApprovalHierarchiesModule,
    DepartmentsModule,
    HealthModule,
  ],
  providers: [
    { provide: APP_PIPE, useClass: ZodValidationPipe },
    {
      provide: APP_INTERCEPTOR,
      useClass: OpusExceptionsInterceptor,
    },
  ],
})
export class AppModule {}
