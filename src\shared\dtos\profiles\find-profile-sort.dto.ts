import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdProfile } from './created-profile.dto';
import { sortQuery } from '../base/base.dto';

export const findProfileSort = createdProfile
  .pick({ lastName: true, firstName: true, createdAt: true })
  .extend({
    lastName: sortQuery,
    firstName: sortQuery,
    createdAt: sortQuery,
  })
  .partial();

export class FindProfileSortDto extends createZodDto(findProfileSort) {}
