/*
  Warnings:

  - Made the column `approver_id` on table `time_away_approval_history` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "time_away_approval_history" DROP CONSTRAINT "time_away_approval_history_approver_user_id_fkey";

-- DropForeignKey
ALTER TABLE "time_away_approval_history" DROP CONSTRAINT "time_away_approval_history_timeAwayId_fkey";

-- AlterTable
ALTER TABLE "time_away_approval_history" ALTER COLUMN "message" DROP NOT NULL,
ALTER COLUMN "approver_id" SET NOT NULL,
ALTER COLUMN "approver_user_id" DROP NOT NULL,
ALTER COLUMN "is_approved" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "time_away_approval_history" ADD CONSTRAINT "time_away_approval_history_approver_user_id_fkey" FOREIGN KEY ("approver_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history" ADD CONSTRAINT "time_away_approval_history_approver_id_fkey" FOREIGN KEY ("approver_id") REFERENCES "time_away_approvers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history" ADD CONSTRAINT "time_away_approval_history_timeAwayId_fkey" FOREIGN KEY ("timeAwayId") REFERENCES "time_aways"("id") ON DELETE CASCADE ON UPDATE CASCADE;
