import { FindProfileFilterDto } from '@/shared/dtos/profiles/find-profile-filter.dto';
import { FindProfileSortDto } from '@/shared/dtos/profiles/find-profile-sort.dto';
import { OmitRelation, Repository } from '../base/repository';
import { ProfileEntity } from '../domain/entities/profile.entity';

export abstract class ProfilesRepository extends Repository<
  ProfileEntity,
  FindProfileFilterDto,
  FindProfileSortDto
> {
  static removeRelationships(data: ProfileEntity): OmitRelation<ProfileEntity> {
    const { department, ...rest } = data;
    return rest;
  }
}
