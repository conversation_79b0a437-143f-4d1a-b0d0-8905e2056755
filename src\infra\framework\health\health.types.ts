import { ApiProperty } from '@nestjs/swagger';

/**
 * Health status enumeration representing the three possible states of a system component
 *
 * - **healthy**: Component is functioning normally with optimal performance
 * - **degraded**: Component is functional but experiencing performance issues
 * - **unhealthy**: Component is not functioning properly or is unavailable
 */
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy';

/**
 * ServiceHealth interface
 *
 * Represents the health status of an individual service component within the application.
 * This interface provides comprehensive information about a service's current state,
 * including performance metrics and diagnostic details.
 *
 * @interface ServiceHealth
 *
 * @example
 * ```typescript
 * const databaseHealth: ServiceHealth = {
 *   status: 'healthy',
 *   message: 'Database connection is healthy',
 *   responseTime: 25,
 *   details: {
 *     connectionPool: 'active',
 *     queryTime: '25ms'
 *   }
 * };
 * ```
 */
export interface ServiceHealth {
  /** Current health status of the service */
  status: HealthStatus;

  /** Human-readable description of the service status */
  message: string;

  /** Response time in milliseconds for the health check operation */
  responseTime: number;

  /** Error message if the service is unhealthy (optional) */
  error?: string;

  /** Additional diagnostic information specific to the service (optional) */
  details?: Record<string, any>;
}

/**
 * HealthCheckResponse interface
 *
 * Represents the complete health check response containing overall application status
 * and detailed information about all monitored services. This is the primary response
 * format for all health check endpoints.
 *
 * @interface HealthCheckResponse
 *
 * @example
 * ```typescript
 * const healthResponse: HealthCheckResponse = {
 *   status: 'healthy',
 *   timestamp: '2024-01-15T10:30:00.000Z',
 *   uptime: 3600000,
 *   version: '1.0.0',
 *   environment: 'production',
 *   services: {
 *     database: { status: 'healthy', message: 'OK', responseTime: 25 },
 *     memory: { status: 'healthy', message: 'Normal usage', responseTime: 2 }
 *   }
 * };
 * ```
 */
export interface HealthCheckResponse {
  /** Overall health status aggregated from all services */
  status: HealthStatus;

  /** ISO timestamp when the health check was performed */
  timestamp: string;

  /** Application uptime in milliseconds since startup */
  uptime: number;

  /** Application version from package.json */
  version: string;

  /** Current environment (development, staging, production) */
  environment: string;

  /** Health status of individual services indexed by service name */
  services: Record<string, ServiceHealth>;
}

/**
 * ServiceHealthDto class
 *
 * Swagger/OpenAPI DTO (Data Transfer Object) for documenting the ServiceHealth interface
 * in API documentation. This abstract class provides Swagger decorators for automatic
 * API schema generation and validation.
 *
 * Used by NestJS Swagger module to generate comprehensive API documentation
 * that helps developers understand the structure and types of health check responses.
 *
 * @abstract
 * @class ServiceHealthDto
 * @implements {ServiceHealth}
 *
 * @example
 * ```typescript
 * // This DTO generates the following OpenAPI schema:
 * {
 *   "type": "object",
 *   "properties": {
 *     "status": { "enum": ["healthy", "degraded", "unhealthy"] },
 *     "message": { "type": "string" },
 *     "responseTime": { "type": "number" },
 *     "error": { "type": "string" },
 *     "details": { "type": "object" }
 *   }
 * }
 * ```
 */
export abstract class ServiceHealthDto implements ServiceHealth {
  /**
   * Health status of the service
   * @type {HealthStatus}
   */
  @ApiProperty({
    enum: ['healthy', 'degraded', 'unhealthy'],
    description: 'Health status of the service',
  })
  abstract status: HealthStatus;

  /**
   * Human-readable status message
   * @type {string}
   */
  @ApiProperty({ description: 'Human-readable status message' })
  abstract message: string;

  /**
   * Response time in milliseconds
   * @type {number}
   */
  @ApiProperty({ description: 'Response time in milliseconds' })
  abstract responseTime: number;

  /**
   * Error message if unhealthy (optional)
   * @type {string}
   */
  @ApiProperty({ description: 'Error message if unhealthy', required: false })
  abstract error?: string;

  /**
   * Additional service details (optional)
   * @type {Record<string, any>}
   */
  @ApiProperty({ description: 'Additional service details', required: false })
  abstract details?: Record<string, any>;
}

/**
 * HealthCheckResponseDto class
 *
 * Swagger/OpenAPI DTO for documenting the complete health check response structure.
 * This abstract class provides comprehensive API documentation for all health endpoints
 * including /health, /health/ready, and /health/live.
 *
 * The DTO ensures consistent API documentation across all health check endpoints
 * and provides clear schema definitions for API consumers and monitoring tools.
 *
 * @abstract
 * @class HealthCheckResponseDto
 * @implements {HealthCheckResponse}
 *
 * @example
 * ```typescript
 * // This DTO generates comprehensive OpenAPI documentation:
 * {
 *   "type": "object",
 *   "properties": {
 *     "status": { "enum": ["healthy", "degraded", "unhealthy"] },
 *     "timestamp": { "type": "string", "format": "date-time" },
 *     "uptime": { "type": "number" },
 *     "version": { "type": "string" },
 *     "environment": { "type": "string" },
 *     "services": {
 *       "type": "object",
 *       "additionalProperties": { "$ref": "#/components/schemas/ServiceHealthDto" }
 *     }
 *   }
 * }
 * ```
 */
export abstract class HealthCheckResponseDto implements HealthCheckResponse {
  /**
   * Overall health status aggregated from all services
   * @type {HealthStatus}
   */
  @ApiProperty({
    enum: ['healthy', 'degraded', 'unhealthy'],
    description: 'Overall health status',
  })
  abstract status: HealthStatus;

  /**
   * ISO timestamp when the health check was performed
   * @type {string}
   */
  @ApiProperty({ description: 'Timestamp of the health check' })
  abstract timestamp: string;

  /**
   * Application uptime in milliseconds since startup
   * @type {number}
   */
  @ApiProperty({ description: 'Application uptime in milliseconds' })
  abstract uptime: number;

  /**
   * Application version from package.json
   * @type {string}
   */
  @ApiProperty({ description: 'Application version' })
  abstract version: string;

  /**
   * Current environment (development, staging, production)
   * @type {string}
   */
  @ApiProperty({
    description: 'Environment (development, staging, production)',
  })
  abstract environment: string;

  /**
   * Health status of individual services indexed by service name
   * @type {Record<string, ServiceHealth>}
   */
  @ApiProperty({
    description: 'Health status of individual services',
    type: 'object',
    additionalProperties: { $ref: '#/components/schemas/ServiceHealthDto' },
  })
  abstract services: Record<string, ServiceHealth>;
}
