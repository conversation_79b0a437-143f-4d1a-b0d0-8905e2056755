/*
  Warnings:

  - You are about to drop the `GoogleCalendarToken` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "GoogleCalendarToken" DROP CONSTRAINT "GoogleCalendarToken_accountId_fkey";

-- DropTable
DROP TABLE "GoogleCalendarToken";

-- CreateTable
CREATE TABLE "google_calendar_tokens" (
    "accountId" TEXT NOT NULL,
    "calendarId" TEXT NOT NULL,
    "refreshToken" TEXT NOT NULL,

    CONSTRAINT "google_calendar_tokens_pkey" PRIMARY KEY ("accountId","calendarId")
);

-- AddForeignKey
ALTER TABLE "google_calendar_tokens" ADD CONSTRAINT "google_calendar_tokens_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
