name: PR Validation

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - 'main'
      - 'staging'

env:
  NODE_VERSION: '20'

jobs:
  # 🚀 OPTIMIZED: Single job with parallel steps for faster feedback
  validation:
    name: PR Validation
    runs-on: ubuntu-latest
    strategy:
      matrix:
        task: [lint-and-type-check, unit-tests, build-check]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js with caching
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: ⚡ Install dependencies (cached)
        run: |
          # Use npm ci with optimizations for faster installs
          npm ci --prefer-offline --no-audit --no-fund

      - name: 🔧 Generate Prisma client (cached)
        run: npx prisma generate

      - name: 📋 Run validation task
        run: |
          case "${{ matrix.task }}" in
            "lint-and-type-check")
              echo "🔍 Running ESLint and TypeScript checks..."
              npm run lint
              npx tsc --noEmit
              echo "✅ Lint and type checks completed"
              ;;
            "unit-tests")
              echo "🧪 Running unit tests..."
              npm run test -- --ci --watchAll=false --maxWorkers=2
              echo "✅ Unit tests completed"
              ;;
            "build-check")
              echo "🏗️ Running build check..."
              npm run build
              echo "✅ Build check completed"
              ;;
          esac

  # 🚀 OPTIMIZED: Lightweight Docker build validation (optional)
  docker-validation:
    name: Docker Build Check
    runs-on: ubuntu-latest
    if: contains(github.event.pull_request.changed_files, 'Dockerfile') || contains(github.event.pull_request.changed_files, 'package.json')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🐳 Build Docker image (no push)
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.optimized
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # PR Status Notification
  notify:
    name: PR Status Notification
    runs-on: ubuntu-latest
    needs: [validation, docker-validation]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: AI Code Review Notice
        run: |
          echo "🤖 AI Code Review will be handled by CodeRabbit"
          echo "CodeRabbit will automatically analyze this PR and provide comments"
          echo "No GitHub Actions time wasted on simulation - real AI review in progress!"

      - name: Check for CodeRabbit comments
        id: coderabbit-check
        run: |
          # Check if CodeRabbit has already commented on this PR
          COMMENTS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.pull_request.number }}/comments" | \
            jq -r '.[] | select(.user.login == "coderabbitai[bot]") | .id' | wc -l)

          if [[ $COMMENTS -gt 0 ]]; then
            echo "coderabbit-status=commented" >> $GITHUB_OUTPUT
            echo "ai-review-message=CodeRabbit has already reviewed and commented on this PR" >> $GITHUB_OUTPUT
          else
            echo "coderabbit-status=pending" >> $GITHUB_OUTPUT
            echo "ai-review-message=CodeRabbit AI review is starting to analyze your code" >> $GITHUB_OUTPUT
          fi

      - name: Determine PR status
        id: status
        run: |
          VALIDATION_STATUS="${{ needs.validation.result }}"
          DOCKER_STATUS="${{ needs.docker-validation.result }}"

          # Get detailed job results
          if [[ "$VALIDATION_STATUS" == "success" ]]; then
            VALIDATION_MESSAGE="✅ All validation checks passed - Lint, TypeScript, tests, and build completed"
          else
            VALIDATION_MESSAGE="❌ Validation failed - Check lint, TypeScript, tests, or build issues"
          fi

          if [[ "$DOCKER_STATUS" == "success" || "$DOCKER_STATUS" == "skipped" ]]; then
            DOCKER_MESSAGE="✅ Docker build validation passed"
          else
            DOCKER_MESSAGE="❌ Docker build validation failed"
          fi

          if [[ "$VALIDATION_STATUS" == "success" && ("$DOCKER_STATUS" == "success" || "$DOCKER_STATUS" == "skipped") ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "overall-message=🚀 PR validation passed! All checks completed successfully." >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "overall-message=❌ PR validation failed. Please fix issues before proceeding." >> $GITHUB_OUTPUT
          fi

          echo "validation-message=$VALIDATION_MESSAGE" >> $GITHUB_OUTPUT
          echo "docker-message=$DOCKER_MESSAGE" >> $GITHUB_OUTPUT

      - name: Send PR status notification
        run: |
          STATUS="${{ steps.status.outputs.status }}"
          if [[ "$STATUS" == "success" ]]; then
            STATUS_EMOJI="✅"
            STATUS_COLOR="good"
          else
            STATUS_EMOJI="❌"
            STATUS_COLOR="danger"
          fi

          COMMIT_SHORT="${{ github.event.pull_request.head.sha }}"
          COMMIT_SHORT="${COMMIT_SHORT:0:7}"

          # Escape quotes in PR title and messages
          PR_TITLE=$(echo '${{ github.event.pull_request.title }}' | sed 's/"/\\"/g')
          OVERALL_MESSAGE=$(echo '${{ steps.status.outputs.overall-message }}' | sed 's/"/\\"/g')
          VALIDATION_MESSAGE=$(echo '${{ steps.status.outputs.validation-message }}' | sed 's/"/\\"/g')
          DOCKER_MESSAGE=$(echo '${{ steps.status.outputs.docker-message }}' | sed 's/"/\\"/g')
          AI_REVIEW_MESSAGE=$(echo '${{ steps.coderabbit-check.outputs.ai-review-message }}' | sed 's/"/\\"/g')

          PAYLOAD=$(cat <<EOF
          {
            "text": "${STATUS_EMOJI} PR Validation - ${{ github.head_ref }}",
            "attachments": [
              {
                "color": "${STATUS_COLOR}",
                "blocks": [
                  {
                    "type": "header",
                    "text": {
                      "type": "plain_text",
                      "text": "${STATUS_EMOJI} PR Validation Results",
                      "emoji": true
                    }
                  },
                  {
                    "type": "section",
                    "fields": [
                      {"type": "mrkdwn", "text": "*PR:* #${{ github.event.pull_request.number }}"},
                      {"type": "mrkdwn", "text": "*Branch:* ${{ github.head_ref }}"},
                      {"type": "mrkdwn", "text": "*Target:* ${{ github.base_ref }}"},
                      {"type": "mrkdwn", "text": "*Author:* ${{ github.event.pull_request.user.login }}"},
                      {"type": "mrkdwn", "text": "*Commit:* ${COMMIT_SHORT}"},
                      {"type": "mrkdwn", "text": "*Status:* ${STATUS_EMOJI} ${STATUS}"}
                    ]
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*PR Title:* ${PR_TITLE}\\n\\n*Overall:* ${OVERALL_MESSAGE}"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Validation:* ${{ needs.validation.result }}\\n${VALIDATION_MESSAGE}\\n\\n*Docker Build:* ${{ needs.docker-validation.result }}\\n${DOCKER_MESSAGE}"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "🤖 *AI Review:* ${{ steps.coderabbit-check.outputs.coderabbit-status }}\\n${AI_REVIEW_MESSAGE}"
                    }
                  },
                  {
                    "type": "actions",
                    "elements": [
                      {
                        "type": "button",
                        "text": {"type": "plain_text", "text": "View PR", "emoji": true},
                        "url": "${{ github.event.pull_request.html_url }}",
                        "style": "primary"
                      },
                      {
                        "type": "button",
                        "text": {"type": "plain_text", "text": "View Workflow", "emoji": true},
                        "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                      }
                    ]
                  },
                  {
                    "type": "context",
                    "elements": [
                      {
                        "type": "mrkdwn",
                        "text": "Environment: development | Validated at $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
                      }
                    ]
                  }
                ]
              }
            ]
          }
          EOF
          )

          curl -X POST -H 'Content-type: application/json' \
            --data "$PAYLOAD" \
            "${{ secrets.SLACK_WEBHOOK_URL }}" || echo "⚠️ Slack notification failed but continuing"
