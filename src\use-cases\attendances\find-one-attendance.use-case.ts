import { UseCase } from '@/core/base/use-case';
import { CreatedAttendanceMapper } from '@/core/domain/mappers/attendances/created-attendance.mapper';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { CreatedAttendanceDto } from '@/shared/dtos/attendances/created-attendance.dto';
import { FindAttendanceFilterDto } from '@/shared/dtos/attendances/find-attendance-filter.dto';
import { FindAttendanceSortDto } from '@/shared/dtos/attendances/find-attendance-sort.dto';

export class FindOneAttendancesUseCase
  implements UseCase<CreatedAttendanceDto>
{
  private readonly createdAttendanceMapper: CreatedAttendanceMapper;

  constructor(private readonly attendancesRepository: AttendancesRepository) {
    this.createdAttendanceMapper = new CreatedAttendanceMapper();
  }

  public async execute(
    filter: FindAttendanceFilterDto,
    sort?: FindAttendanceSortDto,
  ): Promise<CreatedAttendanceDto> {
    const attendance = await this.attendancesRepository.findOne(
      filter,
      {
        startPicture: true,
        endPicture: true,
      },
      sort,
    );
    if (!attendance) {
      throw new EntityNotFoundException();
    }
    return this.createdAttendanceMapper.map(attendance);
  }
}
