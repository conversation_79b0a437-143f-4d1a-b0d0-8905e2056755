import { Email<PERSON><PERSON> } from '@/core/abstracts/email-sender';
import { Module } from '@nestjs/common';
import { OpusConfig } from '../opus-config';
import { SendGridMailerService } from './send-grid-mailer.service';
import { MailBuilderService } from './mail-builder.service';

@Module({
  providers: [
    {
      provide: EmailSender,
      useValue: new SendGridMailerService(OpusConfig.SENDGRID_API_KEY),
    },
    MailBuilderService,
  ],
  exports: [EmailSender, MailBuilderService],
})
export class MailerModule {}
