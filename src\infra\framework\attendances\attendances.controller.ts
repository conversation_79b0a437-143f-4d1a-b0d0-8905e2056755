import { OpusRoles } from '@/core/enums/role.enum';
import { ExportAttendanceInputDto } from '@/shared/dtos/attendances/export-attendance.dto';
import { FindUserAttendanceFilterDto } from '@/shared/dtos/attendances/find-user-attendance-filter.dto';
import { PaginatedGroupedDayAttendanceDto } from '@/shared/dtos/attendances/grouped-day-attendance.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { GroupAttendancesByDayUseCase } from '@/use-cases/attendances/group-attendances-by-day.use-case';
import { Controller, Get, Post, Query, Res } from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { AttendancesService } from './attendances.service';

@Controller('attendances')
@ApiTags('attendances')
@ApiDefaultErrorMessage()
@SwaggerAuth()
@Roles([OpusRoles.Admin])
export class AttendancesController {
  constructor(
    private readonly groupAttendancesByDayUseCase: GroupAttendancesByDayUseCase,
    private readonly attendancesService: AttendancesService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Gets the attendance info of users',
  })
  @ApiOkResponse({ type: PaginatedGroupedDayAttendanceDto })
  async getAttendances(
    @CurrentUser() user: CreatedUserDetailsDto,
    @Query() paging: PaginationMetaDto,
    @Query() filter: FindUserAttendanceFilterDto,
  ) {
    return this.groupAttendancesByDayUseCase.execute(
      user.accountId!,
      user.timezone,
      filter,
      paging!,
      user,
    );
  }

  @Post('/export')
  @ApiOperation({
    summary: 'Export staff attendance as csv or pdf',
  })
  @ApiCreatedResponse({
    description: 'Attendance has been successfully exported',
  })
  async exportAttendance(
    @Query() filter: ExportAttendanceInputDto,
    @CurrentUser() user: CreatedUserDetailsDto,
    @Res() res: Response,
  ) {
    switch (filter.format) {
      case 'pdf':
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="attendance.pdf"`,
        );
        res.send(
          Buffer.from(
            await this.attendancesService.exportAttendancePdf(filter, user),
          ),
        );
        break;
      case 'csv':
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename="attendance.csv"`,
        );
        res.send(
          await this.attendancesService.exportAttendanceCsv(filter, user),
        );
        break;
    }
  }

  @Post('/remind')
  @ApiExcludeEndpoint(true)
  async sendReminders() {
    await this.attendancesService.sendAttendanceReminders();
  }
}
