import { UseCase } from '@/core/base/use-case';
import { CreatedAttendanceMapper } from '@/core/domain/mappers/attendances/created-attendance.mapper';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { FindUserAttendanceFilterDto } from '@/shared/dtos/attendances/find-user-attendance-filter.dto';
import {
  DayLog,
  GroupedUserDayAttendanceDto,
} from '@/shared/dtos/attendances/grouped-user-day-attendance.dto';
import {
  eachDayOfIntervalAtTimezone,
  startOfDayAtTimezone,
} from '@/shared/helpers/date';

// External Helper Dependency

export class GroupUserAttendancesByDayUseCase
  implements UseCase<GroupedUserDayAttendanceDto>
{
  private readonly createdAttendanceMapper: CreatedAttendanceMapper;

  constructor(private readonly attendancesRepository: AttendancesRepository) {
    this.createdAttendanceMapper = new CreatedAttendanceMapper();
  }

  public async execute(
    userId: string,
    timezone: string,
    filter: FindUserAttendanceFilterDto,
  ): Promise<GroupedUserDayAttendanceDto> {
    const { data } = await this.attendancesRepository.findAll(
      {
        userId,
        ...filter,
      },
      undefined,
      {},
      { inDate: 'asc' },
    );

    const intervalDays = eachDayOfIntervalAtTimezone(
      filter.startDate,
      filter.endDate,
      timezone,
    );

    const reduced = data.reduce(
      (prev, curr) => {
        const currDay = startOfDayAtTimezone(
          curr.startDatetime,
          timezone,
        ).toISOString();

        const currDayLogIdx = prev.dayLogs.findIndex(
          (dayLog) => dayLog.day == currDay,
        );

        const currDayLog: DayLog = prev.dayLogs[currDayLogIdx] ?? {
          day: currDay,
          entries: [],
          totalMillis: 0,
        };

        const { userId, day, ...mapped } =
          this.createdAttendanceMapper.map(curr);

        currDayLog.entries.push(mapped);

        currDayLog.totalMillis += curr.millis;
        prev.totalMillis += curr.millis;

        if (currDayLogIdx == -1) {
          prev.dayLogs.push(currDayLog);
        } else {
          prev.dayLogs[currDayLogIdx] = currDayLog;
        }
        return prev;
      },
      {
        totalMillis: 0,
        dayLogs: intervalDays.map<DayLog>((day) => {
          return { day: day.toISOString(), entries: [], totalMillis: 0 };
        }),
      },
    );

    return { userId, ...reduced };
  }
}
