import {
  createdDepartment,
  CreatedDepartmentDto,
} from '@/shared/dtos/department/created-department.dto';

export function departmentMapper(input: {
  id: string;
  headUser: {
    user: {
      id: string;
      firstName: string;
      lastName: string;
    };
  } | null;
  accountId: string;
  departmentName: string;
  headId: string | null;
}): CreatedDepartmentDto {
  const headUser = input.headUser?.user;
  return createdDepartment.parse({
    ...input,
    headUser: headUser ? headUser : null,
  } as CreatedDepartmentDto);
}
