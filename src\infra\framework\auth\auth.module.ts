import { EmailSender } from '@/core/abstracts/email-sender';
import { ExternalImageManager } from '@/core/abstracts/external-image-manager';
import { FilesManager } from '@/core/abstracts/files-manager';
import { OtpGenerator } from '@/core/abstracts/otp-generator';
import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { SocialOauth2 } from '@/core/abstracts/social-login';
import {
  TokenManager,
  TokenManagerOptionsType,
} from '@/core/abstracts/token-manager';
import { FilesRepository } from '@/core/repositories/files.repository';
import { UsersRepository } from '@/core/repositories/users.repository';
import { FilesManagerModule } from '@/infra/data/files-manager/files-manager.module';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { ChangePasswordUseCase } from '@/use-cases/auth/change-password.use-case';
import { GenerateOtpUseCase } from '@/use-cases/auth/generate-otp.use-case';
import { LoginEmailUseCase } from '@/use-cases/auth/login-email.use-case';
import { LoginGoogleUseCase } from '@/use-cases/auth/login-google.use-case';
import { RefreshTokenUseCase } from '@/use-cases/auth/refresh-token.use-case';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { OAuth2Client } from 'google-auth-library';
import { ISSUER } from '../constants';
import { ExternalImageService } from '../external-image/external-image.service';
import { HasherModule } from '../hasher/hasher.module';
import { MailerModule } from '../mailer/mailer.module';
import { OpusConfig } from '../opus-config';
import { AuthController } from './auth.controller';
import { JwtAuthGuard } from './guards/jwt.guard';
import { RequesterMatchGuard } from './guards/requester-match.guard';
import { RolesGuard } from './guards/roles.guard';
import { OtpGeneratorService } from './otp-generator/otp-generator.service';
import { GoogleLoginService } from './social/google-login.service';
import { TokenManagerService } from './token-manager.service';
import { AuthService } from './auth.service';
import { MailBuilderService } from '../mailer/mail-builder.service';

@Module({
  imports: [
    JwtModule.register({
      secret: OpusConfig.JWT_SECRET,
      signOptions: {
        issuer: ISSUER,
      },
      verifyOptions: {
        issuer: ISSUER,
      },
    }),
    HttpModule,
    FilesManagerModule,
    MailerModule,
    HasherModule,
    PrismaModule,
  ],
  providers: [
    GoogleLoginService,
    { provide: APP_GUARD, useClass: JwtAuthGuard },
    { provide: APP_GUARD, useClass: RolesGuard },
    { provide: APP_GUARD, useClass: RequesterMatchGuard },
    {
      provide: TokenManagerService.TOKEN_MANAGER_OPTIONS_KEY,
      useValue: {
        issuer: ISSUER,
        secret: OpusConfig.JWT_SECRET,
        accessExpiryOffset: 5,
        refreshExpiryOffset: 30,
      } as TokenManagerOptionsType,
    },
    {
      provide: GoogleLoginService.GOOGLE_LOGIN_OAUTH2CLIENT_KEY,
      useValue: new OAuth2Client(
        OpusConfig.GOOGLE_CLIENT_ID,
        OpusConfig.GOOGLE_CLIENT_SECRET,
        'postmessage',
      ),
    },
    {
      provide: SocialOauth2,
      useFactory: (
        tokenManager: TokenManager,
        googleOauth2Client: OAuth2Client,
      ) => new GoogleLoginService(tokenManager, googleOauth2Client),
      inject: [TokenManager, GoogleLoginService.GOOGLE_LOGIN_OAUTH2CLIENT_KEY],
    },
    {
      provide: TokenManager,
      useClass: TokenManagerService,
    },
    { provide: ExternalImageManager, useClass: ExternalImageService },
    {
      provide: LoginGoogleUseCase,
      useFactory: (
        googleLogin: SocialOauth2,
        usersRepository: UsersRepository,
        tokenManager: TokenManager,
        externalImage: ExternalImageManager,
        filesManager: FilesManager,
        filesRepository: FilesRepository,
      ) =>
        new LoginGoogleUseCase(
          googleLogin,
          usersRepository,
          tokenManager,
          externalImage,
          filesManager,
          filesRepository,
        ),
      inject: [
        SocialOauth2,
        UsersRepository,
        TokenManager,
        ExternalImageManager,
        FilesManager,
        FilesRepository,
      ],
    },
    {
      provide: LoginEmailUseCase,
      useFactory: (
        passwordHasher: PasswordHasher,
        usersRepository: UsersRepository,
        tokenManager: TokenManager,
      ) => new LoginEmailUseCase(passwordHasher, usersRepository, tokenManager),
      inject: [PasswordHasher, UsersRepository, TokenManager],
    },
    { provide: OtpGenerator, useClass: OtpGeneratorService },
    {
      provide: RefreshTokenUseCase,
      useFactory: (
        tokenManager: TokenManager,
        usersRepository: UsersRepository,
      ) => new RefreshTokenUseCase(tokenManager, usersRepository),
      inject: [TokenManager, UsersRepository],
    },
    {
      provide: GenerateOtpUseCase,
      useFactory: (
        otpGenerator: OtpGenerator,
        emailSender: EmailSender,
        mailBuilderService: MailBuilderService,
      ) =>
        new GenerateOtpUseCase(otpGenerator, emailSender, mailBuilderService),
      inject: [OtpGenerator, EmailSender, MailBuilderService],
    },
    {
      provide: ChangePasswordUseCase,
      useFactory: (
        usersRepository: UsersRepository,
        hasher: PasswordHasher,
        otpGenerator: OtpGenerator,
        emailSender: EmailSender,
        mailBuilderService: MailBuilderService,
      ) =>
        new ChangePasswordUseCase(
          usersRepository,
          hasher,
          otpGenerator,
          emailSender,
          mailBuilderService,
        ),
      inject: [
        UsersRepository,
        PasswordHasher,
        OtpGenerator,
        EmailSender,
        MailBuilderService,
      ],
    },
    AuthService,
  ],
  controllers: [AuthController],
  exports: [
    AuthService,
    TokenManager,
    TokenManagerService.TOKEN_MANAGER_OPTIONS_KEY,
  ],
})
export class AuthModule {}
