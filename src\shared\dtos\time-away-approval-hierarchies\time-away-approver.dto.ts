import { TimeAwayApproverType } from '@prisma/client';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

const departmentHeadApprover = z.object({
  approverType: z.literal(TimeAwayApproverType.DEPARTMENT_HEAD),
});
const userApprover = z.object({
  approverType: z.literal(TimeAwayApproverType.SPECIFIC_PERSON),
  userId: IdType,
  firstName: z.string(),
  lastName: z.string(),
});
const roleApprover = z.object({
  approverType: z.literal(TimeAwayApproverType.SPECIFIC_ROLE),
  roleId: IdType,
  roleName: z.string(),
});

export const timeAwayApprover = z.object({
  id: IdType,
  approver: z.union([departmentHeadApprover, userApprover, roleApprover]),
});
const checklistItem = z.object({
  id: IdType,
  question: z.string(),
});
export const timeAwayApprovalHierarchy = z.object({
  approvers: timeAwayApprover.array(),
  checklist: checklistItem.array(),
});
export class TimeAwayApprovalHierarchy extends createZodDto(
  timeAwayApprovalHierarchy,
) {}

const upsertTimeAwayApprover = timeAwayApprover
  .extend({
    approver: z.union([
      departmentHeadApprover,
      userApprover.omit({ firstName: true, lastName: true }),
      roleApprover.omit({ roleName: true }),
    ]),
  })
  .partial({ id: true });
export const updateTimeAwayApprovalHierarchy = z.object({
  hierarchy: upsertTimeAwayApprover.array(),
});

export class UpdateTimeAwayApprovalHierarchy extends createZodDto(
  updateTimeAwayApprovalHierarchy,
) {}
