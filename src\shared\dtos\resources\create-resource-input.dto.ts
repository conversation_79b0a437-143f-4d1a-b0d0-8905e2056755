import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdResource } from './created-resource.dto';
import { IdType } from '../base/base.dto';
import { z } from 'zod';

export const createResourceInput = createdResource
  .extend({ attachmentIds: IdType.array(), draft: z.boolean().optional() })
  .omit({
    id: true,
    uploader: true,
    accountId: true,
    attachments: true,
    createdAt: true,
    publishDate: true,
  });

export class CreateResourceInputDto extends createZodDto(createResourceInput) {}
