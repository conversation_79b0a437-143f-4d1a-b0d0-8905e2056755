import { OtpGenerator } from '@/core/abstracts/otp-generator';
import { OtpPurpose } from '@/core/enums/otp-purpose.enum';
import { Injectable } from '@nestjs/common';
import { Secret, TOTP } from 'otpauth';

@Injectable()
export class OtpGeneratorService implements OtpGenerator {
  async generate(key: string, purpose: OtpPurpose): Promise<string> {
    const totp = new TOTP({ secret: this.generateSecret(key, purpose) });
    return totp.generate();
  }

  async validate(
    otp: string,
    key: string,
    purpose: OtpPurpose,
  ): Promise<boolean> {
    const totp = new TOTP({ secret: this.generateSecret(key, purpose) });
    const delta = totp.validate({ token: otp, window: 2 });
    return typeof delta == 'number';
  }

  private generateSecret(key: string, purpose: string) {
    return Secret.fromUTF8(`${key}${purpose}`);
  }
}
