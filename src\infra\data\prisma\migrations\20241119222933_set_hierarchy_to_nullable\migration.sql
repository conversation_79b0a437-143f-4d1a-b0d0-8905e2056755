-- DropFore<PERSON><PERSON>ey
ALTER TABLE "time_away_approvers" DROP CONSTRAINT "time_away_approvers_time_away_approval_hierarchy_id_fkey";

-- DropForeignKey
ALTER TABLE "time_away_check_list_item" DROP CONSTRAINT "time_away_check_list_item_time_away_approval_hierarchy_id_fkey";

-- AlterTable
ALTER TABLE "time_away_approvers" ALTER COLUMN "time_away_approval_hierarchy_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "time_away_check_list_item" ALTER COLUMN "time_away_approval_hierarchy_id" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "time_away_approvers" ADD CONSTRAINT "time_away_approvers_time_away_approval_hierarchy_id_fkey" FOREIGN KEY ("time_away_approval_hierarchy_id") REFERENCES "time_away_approval_hierarchies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_check_list_item" ADD CONSTRAINT "time_away_check_list_item_time_away_approval_hierarchy_id_fkey" FOREIGN KEY ("time_away_approval_hierarchy_id") REFERENCES "time_away_approval_hierarchies"("id") ON DELETE SET NULL ON UPDATE CASCADE;
