import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { fileMeta } from '../files/file-meta.dto';

export const individualAttendance = z
  .object({
    lat: z.number(),
    long: z.number(),
    datetime: z.string().datetime(),
    picture: fileMeta,
  })
  .openapi('IndividualAttendance');
export const createdAttendance = z.object({
  id: IdType,
  userId: IdType,
  day: z.string().datetime(),
  millis: z.number(),
  in: individualAttendance,
  out: individualAttendance.nullish(),
});
export class CreatedAttendanceDto extends createZodDto(createdAttendance) {}
