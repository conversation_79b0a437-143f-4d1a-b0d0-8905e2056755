import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { TimeAwayApproverType } from '@prisma/client';

export const checkListItem = z.object({
  id: z.string(),
  question: z.string(),
});
export const checkListAnswer = z.object({
  id: IdType,
  checkListItemId: IdType,
  answer: z.boolean().nullable(),
});

export const approverPerson = z.object({
  id: IdType,
  firstName: z.string(),
  lastName: z.string(),
});
export const personApprover = z.object({
  approverType: z.enum([
    TimeAwayApproverType.DEPARTMENT_HEAD,
    TimeAwayApproverType.SPECIFIC_PERSON,
  ]),
  user: approverPerson.nullable(),
});
export const roleApprover = z.object({
  approverType: z.literal(TimeAwayApproverType.SPECIFIC_ROLE),
  role: z
    .object({
      id: IdType,
      title: z.string(),
    })
    .nullable(),
});

export const approverDetails = z.union([personApprover, roleApprover]);
export type ApproverDetails = z.infer<typeof approverDetails>;

export const approvalHistoryItem = z.object({
  id: IdType,
  message: z.string().nullable(),
  isApproved: z.boolean().nullable(),
  approverDetails,
  approverUser: approverPerson.nullable(),
  answers: checkListAnswer.array(),
});
export const createdTimeAway = z.object({
  id: IdType,
  userId: IdType,
  hours: z.coerce.number().array(),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  timeAwayTypeId: IdType,
  isApproved: z.boolean().nullable(),
  reason: z.string().nullable(),
  createdAt: z.string().datetime(),
  approvalMessage: z.string().nullable(),
  isUserRequest: z.boolean(),
  isSubtract: z.boolean().default(true),
  approvalHistory: approvalHistoryItem.array(),
  checklist: checkListItem.array(),
  currentApproverIndex: z.number(),
  finalApproverUser: z
    .object({
      id: IdType,
      firstName: z.string(),
      lastName: z.string(),
    })
    .nullable(),
});

export class CreatedTimeAwayDto extends createZodDto(createdTimeAway) {}
