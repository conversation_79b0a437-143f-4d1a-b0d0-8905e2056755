import { ExternalImageManager } from '@/core/abstracts/external-image-manager';
import { FilesManager } from '@/core/abstracts/files-manager';
import { SocialOauth2 } from '@/core/abstracts/social-login';
import { TokenManager } from '@/core/abstracts/token-manager';
import { UseCase } from '@/core/base/use-case';
import { CreateFileMapper } from '@/core/domain/mappers/files/create-file.mapper';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import { FilePermission } from '@/core/enums/file.enum';
import {
  InsufficientPermissionException,
  InvalidCredentialException,
} from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { UsersRepository } from '@/core/repositories/users.repository';
import { GoogleLoginInputDto } from '@/shared/dtos/auth/login-input.dto';
import { AuthTokensDto } from '@/shared/dtos/auth/token.dto';

export class LoginGoogleUseCase implements UseCase<AuthTokensDto> {
  private readonly createdUserMapper: CreatedUserMapper;
  private readonly createFileMapper: CreateFileMapper;

  constructor(
    private readonly googleLogin: SocialOauth2,
    private readonly usersRepository: UsersRepository,
    private readonly tokenManager: TokenManager,
    private readonly externalImage: ExternalImageManager,
    private readonly filesManager: FilesManager,
    private readonly filesRepository: FilesRepository,
  ) {
    this.createdUserMapper = new CreatedUserMapper();
    this.createFileMapper = new CreateFileMapper();
  }

  public async execute(
    input: GoogleLoginInputDto,
    timezone?: string,
  ): Promise<AuthTokensDto> {
    const { code } = input;
    const { email, pictureUrl, refreshToken } =
      await this.googleLogin.verifyLogin(code);

    const user = await this.usersRepository.findOne({ email });

    if (!user) {
      throw new InvalidCredentialException('User is not registered');
    }

    if (!user.activatedAt) {
      throw new InsufficientPermissionException('User is not activated');
    }

    let profilePictureId: string | undefined = undefined;

    // try to update the profile picture if user has no profile picture yet
    if (user.profilePictureId === null && pictureUrl) {
      const file = await this.externalImage.download(pictureUrl);
      const originalFileName = `${user.lastName}-${user.lastName}.jpg`;

      const modifiedFileName = await this.filesManager.upload(
        originalFileName,
        file,
      );

      const createFileData = this.createFileMapper.map({
        content: file,
        contentType: 'image/jpg',
        fileName: modifiedFileName,
        originalFileName: originalFileName,
        permission: FilePermission.Public,
        fileSize: file.byteLength,
        uploaderId: user.id,
      });

      const createdFileMeta = await this.filesRepository.create(createFileData);
      profilePictureId = createdFileMeta.id;
    }

    if (timezone || profilePictureId || refreshToken) {
      await this.usersRepository.update(user.id, {
        profilePictureId,
        timezone,
        googleRefreshToken: refreshToken,
      });
    }

    const tokens = await this.tokenManager.generateTokens(
      this.createdUserMapper.map(user),
    );
    return tokens;
  }
}
