import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { Module } from '@nestjs/common';
import { BcryptHasherService } from './bcrypt-hasher.service';
import { OpusConfig } from '../opus-config';

@Module({
  providers: [
    {
      provide: PasswordHasher,
      useFactory: () =>
        new BcryptHasherService({
          saltRounds: OpusConfig.PASSWORD_SALT_ROUNDS,
        }),
    },
  ],
  exports: [PasswordHasher],
})
export class HasherModule {}
