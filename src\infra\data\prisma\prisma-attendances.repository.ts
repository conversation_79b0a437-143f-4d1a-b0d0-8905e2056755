import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { AttendanceEntity } from '@/core/domain/entities/attendance.entity';
import {
  EntityConflictException,
  EntityNotFoundException,
} from '@/core/exceptions/opus-exceptions';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { ExtendedTransactionalAdapterPrisma } from '@/infra/data/prisma/prisma.service';
import { FindAttendanceFilterDto } from '@/shared/dtos/attendances/find-attendance-filter.dto';
import { FindAttendanceSortDto } from '@/shared/dtos/attendances/find-attendance-sort.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { objectEntries } from '@/shared/helpers/entries';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaAttendancesRepository implements AttendancesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: AttendanceEntity): Promise<AttendanceEntity> {
    try {
      const attendance = await this.txHost.tx.attendance.create({
        data: {
          ...AttendancesRepository.removeRelationships(data),
        },
        include: this.processInclude({}),
      });
      return attendance;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Picture id is already used');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }
      throw error;
    }
  }

  async findOne(
    filter: FindAttendanceFilterDto,
    include: PickRelation<AttendanceEntity> = {},
    sort: FindAttendanceSortDto = {},
  ): Promise<AttendanceEntity | null> {
    const processedFilter = this.processFilter(filter);
    const processedSort = this.processSort(sort);

    return this.txHost.tx.attendance.findFirst({
      where: processedFilter,
      include: this.processInclude(include),
      orderBy: processedSort,
    });
  }

  @Transactional()
  async findAll(
    filter: FindAttendanceFilterDto,
    paginationMeta?: PaginationMetaDto,
    include: PickRelation<AttendanceEntity> = {},
    sort: FindAttendanceSortDto = {},
  ): Promise<EntityCount<AttendanceEntity>> {
    const processedFilter = this.processFilter(filter);
    const processedSort = this.processSort(sort);

    const data = await this.txHost.tx.attendance.findMany({
      where: processedFilter,
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      include: this.processInclude(include),
      orderBy: processedSort,
    });
    const count = await this.txHost.tx.attendance.count({
      where: processedFilter,
    });
    return { data, count };
  }

  async update(
    id: string,
    data: Partial<AttendanceEntity>,
  ): Promise<AttendanceEntity> {
    try {
      return await this.txHost.tx.attendance.update({
        where: { id },
        data: AttendancesRepository.removeRelationships(
          data as AttendanceEntity,
        ),
        include: this.processInclude({}),
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Picture id is already used');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2025':
            throw new EntityConflictException('Attendance does not exist');
        }
      }
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      await this.txHost.tx.attendance.delete({ where: { id } });
    } catch (error) {
      throw new EntityNotFoundException(`Attendance ${id} does not exist`);
    }
  }

  private processInclude(
    include: PickRelation<AttendanceEntity>,
  ): Prisma.AttendanceInclude {
    return {
      ...include,
      user: { select: { timezone: true } },
      startPicture: true,
      endPicture: true,
    };
  }

  private processFilter(
    filter: FindAttendanceFilterDto,
  ): Prisma.AttendanceWhereInput {
    const { startDate, endDate, ...rest } = filter;
    return {
      ...rest,
      startDatetime: { gte: startDate, lte: endDate },
    };
  }

  private processSort(
    sort: FindAttendanceSortDto,
  ): Prisma.AttendanceOrderByWithRelationInput[] {
    return objectEntries(sort).map(([k, v]) => {
      const item = {} as Prisma.AttendanceOrderByWithRelationInput;
      switch (k) {
        case 'inDate':
          item['startDatetime'] = v;
          break;
        case 'endDate':
          item['endDatetime'] = v;
          break;
      }
      return item;
    });
  }
}
