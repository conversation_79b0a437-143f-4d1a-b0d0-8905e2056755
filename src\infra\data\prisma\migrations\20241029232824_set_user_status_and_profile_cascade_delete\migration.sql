-- DropForeign<PERSON>ey
ALTER TABLE "profiles" DROP CONSTRAINT "profiles_id_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "user_status" DROP CONSTRAINT "user_status_id_fkey";

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_status" ADD CONSTRAINT "user_status_id_fkey" FOREIGN KEY ("id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
