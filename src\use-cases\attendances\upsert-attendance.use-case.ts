import { UseCase } from '@/core/base/use-case';
import { AttendanceEntity } from '@/core/domain/entities/attendance.entity';
import { CreatedAttendanceMapper } from '@/core/domain/mappers/attendances/created-attendance.mapper';
import { UpsertAttendanceMapper } from '@/core/domain/mappers/attendances/upsert-attendance.mapper';
import { InvalidPayloadException } from '@/core/exceptions/opus-exceptions';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { AttendancesService } from '@/infra/framework/attendances/attendances.service';
import { CreatedAttendanceDto } from '@/shared/dtos/attendances/created-attendance.dto';
import { UpsertAttendanceDto } from '@/shared/dtos/attendances/upsert-attendance.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class UpsertAttendanceUseCase implements UseCase<CreatedAttendanceDto> {
  private readonly createdAttendanceMapper: CreatedAttendanceMapper;
  private readonly upsertAttendanceMapper: UpsertAttendanceMapper;

  constructor(
    private readonly attendancesRepository: AttendancesRepository,
    private readonly attendancesService: AttendancesService,
  ) {
    this.createdAttendanceMapper = new CreatedAttendanceMapper();
    this.upsertAttendanceMapper = new UpsertAttendanceMapper();
  }

  public async execute(
    data: UpsertAttendanceDto,
    user: CreatedUserDetailsDto,
  ): Promise<CreatedAttendanceDto> {
    if (!data.id && !data.in) {
      throw new InvalidPayloadException(
        'Time in data must be provided when id is not provided',
      );
    }

    let attendance: AttendanceEntity;
    if (!data.id) {
      // Create attendance when id is NOT provided
      const createData = this.upsertAttendanceMapper.map(data);
      attendance = await this.attendancesRepository.create(createData);
    } else {
      // Update attendance when id IS PROVIDED
      const existing = await this.attendancesRepository.findOne({
        id: data.id,
      });

      if (existing && existing.startDatetime) {
        if (
          data.out &&
          new Date(data.out?.datetime) < new Date(existing.startDatetime)
        ) {
          throw new InvalidPayloadException(
            'End time log cannot be before the start time log',
          );
        }
      }
      const updateData = this.upsertAttendanceMapper.map(data);
      attendance = await this.attendancesRepository.update(data.id, updateData);
    }

    try {
      void this.attendancesService.sendSlackNotif(user, data.id ? 'out' : 'in');
    } catch (error) {
      // do nothing on error
    }

    return this.createdAttendanceMapper.map(attendance);
  }
}
