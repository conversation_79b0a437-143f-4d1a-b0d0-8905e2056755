import { FindFileFilterDto } from '@/shared/dtos/files/find-file-filter.dto';
import { Repository } from '../base/repository';
import { FileEntity } from '../domain/entities/file.entity';

export abstract class FilesRepository extends Repository<
  FileEntity,
  FindFileFilterDto,
  unknown
> {
  static removeRelationships(data: FileEntity) {
    const { uploader, ...rest } = data;
    return rest;
  }
}
