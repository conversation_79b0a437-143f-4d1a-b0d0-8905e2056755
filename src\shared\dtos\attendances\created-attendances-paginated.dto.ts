import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { paginateSchema } from '../pagination/paginated-base';
import { createdAttendance } from './created-attendance.dto';

export const paginatedCreatedAttendances = paginateSchema(
  createdAttendance,
).extend({
  totalMillis: z.number(),
});
export class PaginatedCreatedAttendancesDto extends createZodDto(
  paginatedCreatedAttendances,
) {}
