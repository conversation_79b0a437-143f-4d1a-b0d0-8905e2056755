import { Entity } from '@/core/base/entity';
import { GenderType } from '@/core/enums/gender.enum';
import { MaritalStatusType } from '@/core/enums/marital-status.enum';
import { DepartmentEntity } from './department.entity';
import { UserEntity } from './user.entity';
import { FileEntity } from './file.entity';

export class ProfileEntity extends Entity {
  /**
   * This id equal to the id of the related User Entity
   */
  id!: string;
  birthDate!: Date | null;
  maritalStatus!: MaritalStatusType | null;
  gender!: GenderType | null;

  // Address Properties
  addressStreet1!: string | null;
  addressStreet2!: string | null;
  city!: string | null;
  province!: string | null;
  postalCode!: string | null;
  country!: string | null;

  // Contact Properties
  workPhone!: string | null;
  mobilePhone!: string | null;
  homePhone!: string | null;
  personalEmail!: string | null;

  // Emergency Contact Properties
  emergencyName!: string | null;
  emergencyRelationship!: string | null;
  emergencyMobilePhone!: string | null;

  // Work Properties
  jobTitle!: string | null;
  employmentStatus!: string | null;
  departmentId!: string | null;
  contractId!: string | null;
  joinDate!: Date;

  department!: DepartmentEntity | null;
  contract!: FileEntity | null;
  user!: Pick<
    UserEntity,
    'firstName' | 'lastName' | 'email' | 'profilePicture' | 'activatedAt'
  >;
}
