import { Logger } from '@/core/abstracts/logger';
import { Injectable, Logger as NestLogger } from '@nestjs/common';

@Injectable()
export class LoggerService implements Logger {
  private readonly logger: NestLogger;

  constructor(private readonly context: string) {
    this.logger = new NestLogger(context);
  }

  verbose(message: any): void {
    this.logger.verbose(message);
  }
  debug(message: any): void {
    this.logger.debug(message);
  }
  log(message: any): void {
    this.logger.log(message);
  }
  warn(message: any): void {
    this.logger.warn(message);
  }
  error(message: any): void {
    this.logger.error(message);
  }
}
