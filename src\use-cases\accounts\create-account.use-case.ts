import { UseCase } from '@/core/base/use-case';
import { CreateAccountMapper } from '@/core/domain/mappers/accounts/create-account.mapper';
import { CreatedAccountMapper } from '@/core/domain/mappers/accounts/created-account.mapper';
import { AccountsRepository } from '@/core/repositories/accounts.repository';
import { CreateAccountInputDto } from '@/shared/dtos/accounts/create-account-input.dto';
import { CreatedAccountDto } from '@/shared/dtos/accounts/created-account.dto';

export class CreateAccountUseCase implements UseCase<CreatedAccountDto> {
  private createAccountMapper: CreateAccountMapper;
  private createdAccountMapper: CreatedAccountMapper;

  constructor(private readonly repository: AccountsRepository) {
    this.createAccountMapper = new CreateAccountMapper();
    this.createdAccountMapper = new CreatedAccountMapper();
  }

  public async execute(
    account: CreateAccountInputDto,
  ): Promise<CreatedAccountDto> {
    const entity = this.createAccountMapper.map(account);
    const createdAccount = await this.repository.create(entity);
    return this.createdAccountMapper.map(createdAccount);
  }
}
