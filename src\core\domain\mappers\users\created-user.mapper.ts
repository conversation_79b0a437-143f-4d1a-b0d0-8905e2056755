import { Mapper } from '@/core/base/mapper';
import { UserEntity } from '@/core/domain/entities/user.entity';
import {
  createdUserRelationship,
  CreatedUserRelationshipDto,
} from '@/shared/dtos/users/created-user.dto';
import { CreatedUserStatusMapper } from '../user-status/created-user-status.mapper';

export class CreatedUserMapper
  implements Mapper<UserEntity, CreatedUserRelationshipDto>
{
  private readonly userStatusMapper = new CreatedUserStatusMapper();

  public map(data: UserEntity): CreatedUserRelationshipDto {
    const { userStatus, activatedAt, ...rest } = data;

    let toParse: any = {
      ...rest,
      activatedAt: activatedAt ? activatedAt.toISOString() : null,
    };

    if (userStatus) {
      toParse = {
        ...toParse,
        userStatus: this.userStatusMapper.map(userStatus),
      };
    }
    return createdUserRelationship.parse(toParse);
  }
}
