import { FilesManager } from '@/core/abstracts/files-manager';
import { Bucket } from '@google-cloud/storage';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GoogleFilesManagerService extends FilesManager {
  constructor(private readonly bucket: Bucket) {
    super();
  }

  async upload(originalFileName: string, file: Buffer): Promise<string> {
    const randomizedFileName = this.randomizeFileName(originalFileName);
    const bucketFile = this.bucket.file(randomizedFileName);
    await bucketFile.save(file);
    return randomizedFileName;
  }

  async download(fileName: string): Promise<Buffer> {
    const bucketFile = this.bucket.file(fileName);
    const contents = await bucketFile.download();
    return contents[0];
  }
}
