import { SetUserAccrualPolicyInput } from '@/shared/dtos/time-away-credits/set-user-accrual-policy-input';
import { PaginatedTotalUserTimeAwayCreditsDto } from '@/shared/dtos/time-away-credits/total-user-time-away-credits-paginated.dto';
import { CreatedTimeAwayTypeListDto } from '@/shared/dtos/time-away-types/created-time-away-type-list.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { FindAllTimeAwayTypesUseCase } from '@/use-cases/time-away-types/find-all-time-away-types.use-case';
import { Body, Controller, Get, Post } from '@nestjs/common';
import {
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { LeaveTypesService } from './leave-types.service';

@Controller('leave-types')
@ApiTags('leave-types')
@ApiDefaultErrorMessage()
@SwaggerAuth()
export class LeaveTypesController {
  constructor(
    private readonly findAllTimeAwayTypesUseCase: FindAllTimeAwayTypesUseCase,
    private readonly leaveCreditsService: LeaveTypesService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get the list of all leave types',
  })
  @ApiOkResponse({ type: CreatedTimeAwayTypeListDto })
  async findAll() {
    return this.findAllTimeAwayTypesUseCase.execute();
  }

  @Post('/accrual/process')
  @ApiExcludeEndpoint(true)
  @ApiOperation({
    summary: 'Set the accrual policy of users',
  })
  @ApiOkResponse({
    type: PaginatedTotalUserTimeAwayCreditsDto,
  })
  async processAccruals() {
    return this.leaveCreditsService.processAccruals();
  }

  @Post('/accrual/users')
  @ApiOperation({
    summary: 'Set the accrual policy of users',
  })
  @ApiOkResponse({
    type: PaginatedTotalUserTimeAwayCreditsDto,
  })
  async setUserAccrualPolicy(
    @Body() input: SetUserAccrualPolicyInput,
    @CurrentUser() requester: CreatedUserDetailsDto,
  ) {
    return this.leaveCreditsService.setUserAccrualPolicy(input, requester);
  }
}
