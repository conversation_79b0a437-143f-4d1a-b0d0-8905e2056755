import { OpusRoles } from '@/core/enums/role.enum';
import { TimeAwayTypes } from '@/core/enums/time-away-types.enum';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedRoles() {
  for (const key in OpusRoles) {
    const title = OpusRoles[key as keyof typeof OpusRoles];
    console.log(`Upserting role '${title}'`);
    const role = await prisma.role.upsert({
      where: { title },
      update: {},
      create: { title },
    });
    console.log(`'${title}' inserted at ${role.createdAt}\n`);
  }
}

async function seedTimeAwayTypes() {
  for (const key in TimeAwayTypes) {
    const description = TimeAwayTypes[key as keyof typeof TimeAwayTypes];
    console.log(`Upserting time away type '${description}'`);
    const role = await prisma.timeAwayType.upsert({
      where: { description },
      update: {},
      create: { description },
    });
    console.log(`'${description}' inserted at ${role.createdAt}\n`);
  }
}

async function main() {
  await seedRoles();
  await seedTimeAwayTypes();
}
main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
