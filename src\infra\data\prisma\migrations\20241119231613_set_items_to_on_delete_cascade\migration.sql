-- DropFore<PERSON><PERSON>ey
ALTER TABLE "time_away_approval_history_item" DROP CONSTRAINT "time_away_approval_history_item_approver_user_id_fkey";

-- DropForeignKey
ALTER TABLE "time_away_check_list_item_answers" DROP CONSTRAINT "time_away_check_list_item_answers_check_list_id_fkey";

-- DropForeignKey
ALTER TABLE "time_away_check_list_item_answers" DROP CONSTRAINT "time_away_check_list_item_answers_history_item_id_fkey";

-- AddForeignKey
ALTER TABLE "time_away_approval_history_item" ADD CONSTRAINT "time_away_approval_history_item_approver_user_id_fkey" FOREIGN KEY ("approver_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_check_list_item_answers" ADD CONSTRAINT "time_away_check_list_item_answers_check_list_id_fkey" FOREIGN KEY ("check_list_id") REFERENCES "time_away_check_list_item"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_check_list_item_answers" ADD CONSTRAINT "time_away_check_list_item_answers_history_item_id_fkey" FOREIGN KEY ("history_item_id") REFERENCES "time_away_approval_history_item"("id") ON DELETE CASCADE ON UPDATE CASCADE;
