# Multi-stage optimized Dockerfile for faster builds
# Uses BuildKit features for better caching and parallel builds

# ------ Base image with Node.js ------
FROM node:20.15.0-alpine AS base
WORKDIR /opus

# Install system dependencies in a single layer
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates

# ------ Dependencies stage ------
FROM base AS deps
WORKDIR /opus

# Copy package files for dependency installation
COPY package.json package-lock.json ./

# Install dependencies with optimizations
# --mount=type=cache mounts npm cache for faster subsequent builds
RUN --mount=type=cache,target=/root/.npm \
    npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# ------ Build stage ------
FROM base AS builder
WORKDIR /opus

# Copy package files
COPY package.json package-lock.json ./

# Install all dependencies (including dev dependencies)
RUN --mount=type=cache,target=/root/.npm \
    npm ci --no-audit --no-fund

# Copy source code
COPY . .

# Generate Prisma client and build
# These steps are cached unless source code changes
RUN npx prisma generate
RUN npm run build

# ------ Production stage ------
FROM node:20.15.0-alpine AS production
WORKDIR /opus

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S opus -u 1001

# Copy production dependencies from deps stage
COPY --from=deps --chown=opus:nodejs /opus/node_modules ./node_modules

# Copy built application from builder stage
COPY --from=builder --chown=opus:nodejs /opus/dist ./dist
COPY --from=builder --chown=opus:nodejs /opus/package.json ./

# Copy Prisma schema and generated client
COPY --from=builder --chown=opus:nodejs /opus/src/infra/data/prisma ./src/infra/data/prisma

# Copy start script
COPY --chown=opus:nodejs ./scripts/start.sh ./scripts/start.sh
RUN chmod +x ./scripts/start.sh

# Switch to non-root user
USER opus

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
ENV NPM_CONFIG_CACHE=/tmp/.npm

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["./scripts/start.sh"]