/*
  Warnings:

  - A unique constraint covering the columns `[contract_id]` on the table `profiles` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "profiles" ADD COLUMN     "contract_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "profiles_contract_id_key" ON "profiles"("contract_id");

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE CASCADE;
