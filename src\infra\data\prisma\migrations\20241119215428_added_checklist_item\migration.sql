/*
  Warnings:

  - You are about to drop the column `previous_approver_id` on the `time_away_approvers` table. All the data in the column will be lost.
  - You are about to drop the `time_away_approval_history` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "time_away_approval_history" DROP CONSTRAINT "time_away_approval_history_approver_id_fkey";

-- DropForeignKey
ALTER TABLE "time_away_approval_history" DROP CONSTRAINT "time_away_approval_history_approver_user_id_fkey";

-- DropForeignKey
ALTER TABLE "time_away_approval_history" DROP CONSTRAINT "time_away_approval_history_timeAwayId_fkey";

-- DropForeignKey
ALTER TABLE "time_away_approvers" DROP CONSTRAINT "time_away_approvers_previous_approver_id_fkey";

-- DropIndex
DROP INDEX "time_away_approvers_previous_approver_id_key";

-- AlterTable
ALTER TABLE "time_away_approvers" DROP COLUMN "previous_approver_id",
ADD COLUMN     "order" INTEGER NOT NULL DEFAULT 0;

-- DropTable
DROP TABLE "time_away_approval_history";

-- CreateTable
CREATE TABLE "time_away_check_list_item" (
    "id" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    "time_away_approval_hierarchy_id" TEXT NOT NULL,

    CONSTRAINT "time_away_check_list_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "time_away_approval_history_item" (
    "id" TEXT NOT NULL,
    "message" TEXT,
    "is_approved" BOOLEAN,
    "approver_user_id" TEXT,
    "approver_id" TEXT NOT NULL,
    "timeAwayId" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "time_away_approval_history_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "time_away_check_list_item_answers" (
    "id" TEXT NOT NULL,
    "check_list_id" TEXT NOT NULL,
    "answer" BOOLEAN NOT NULL DEFAULT false,
    "history_item_id" TEXT NOT NULL,

    CONSTRAINT "time_away_check_list_item_answers_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "time_away_check_list_item" ADD CONSTRAINT "time_away_check_list_item_time_away_approval_hierarchy_id_fkey" FOREIGN KEY ("time_away_approval_hierarchy_id") REFERENCES "time_away_approval_hierarchies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history_item" ADD CONSTRAINT "time_away_approval_history_item_approver_user_id_fkey" FOREIGN KEY ("approver_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history_item" ADD CONSTRAINT "time_away_approval_history_item_approver_id_fkey" FOREIGN KEY ("approver_id") REFERENCES "time_away_approvers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_approval_history_item" ADD CONSTRAINT "time_away_approval_history_item_timeAwayId_fkey" FOREIGN KEY ("timeAwayId") REFERENCES "time_aways"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_check_list_item_answers" ADD CONSTRAINT "time_away_check_list_item_answers_check_list_id_fkey" FOREIGN KEY ("check_list_id") REFERENCES "time_away_check_list_item"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_check_list_item_answers" ADD CONSTRAINT "time_away_check_list_item_answers_history_item_id_fkey" FOREIGN KEY ("history_item_id") REFERENCES "time_away_approval_history_item"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
