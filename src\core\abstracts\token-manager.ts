import { AuthTokensDto, TokenPayloadDto } from '@/shared/dtos/auth/token.dto';
import { CreatedUserDto } from '@/shared/dtos/users/created-user.dto';
import { InvalidJwtException } from '../exceptions/opus-exceptions';

export type TokenManagerOptionsType = {
  issuer: string;
  secret: string;
  accessExpiryOffset: number;
  refreshExpiryOffset: number;
};

export abstract class TokenManager {
  protected options: TokenManagerOptionsType;

  constructor(options: TokenManagerOptionsType) {
    this.options = options;
  }

  /**
   * Verifies and decodes a JWT
   *
   * @param token The JWT to be verified and parsed
   * @returns The decoded JWT payload
   *
   * @throws A {@link InvalidJwtException} when the JWT is invalid
   */
  abstract verify<T extends object = any>(token: string): Promise<T>;

  /**
   * Decodes a JWT without verification
   *
   * @param token The JWT to be decoded
   * @returns The decoded JWT payload
   *
   * @throws A {@link InvalidJwtException} when the JWT is invalid
   */
  abstract decode<T extends object = any>(token: string): Promise<T>;

  /**
   * Encodes the payload into a JWT using the options
   *
   * @param payload The payload
   * @returns The signed JWT
   */
  abstract sign(payload: TokenPayloadDto): Promise<string>;

  /**
   * Generates access and refresh tokens from the user details.
   * Only generates the access token if overrideRefreshToken is provided.
   *
   * @param user The user details
   * @param overrideRefreshToken The refresh token used for re
   *
   * @returns The object containing the access and refresh tokens
   */
  abstract generateTokens(
    user: CreatedUserDto,
    overrideRefreshToken?: string,
  ): Promise<AuthTokensDto>;
}
