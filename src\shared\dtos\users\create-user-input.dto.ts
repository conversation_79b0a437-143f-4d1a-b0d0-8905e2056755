import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType, PasswordType } from '../base/base.dto';

export const createUserInput = z
  .object({
    email: z.string().email().describe('The email address of the user'),
    firstName: z.string().describe('The first name of the user'),
    lastName: z.string().describe('The last name of the user'),
    password: PasswordType.optional().describe('The password of the user'),
    accountId: IdType.describe('The reference id to the account').optional(),
    roleId: IdType.describe('The reference id to the role').optional(),
  })
  .describe('The input data used for creating a user entity');

export class CreateUserInputDto extends createZodDto(createUserInput) {}
