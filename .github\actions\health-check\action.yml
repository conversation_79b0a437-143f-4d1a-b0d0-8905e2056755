name: 'Health Check'
description: 'Performs health checks on deployed Cloud Run services'
inputs:
  service-url:
    description: 'URL of the service to check'
    required: true
  timeout:
    description: 'Timeout in seconds for health checks'
    required: false
    default: '30'
  retry-attempts:
    description: 'Number of retry attempts'
    required: false
    default: '3'
  retry-delay:
    description: 'Delay between retries in seconds'
    required: false
    default: '10'
  expected-status:
    description: 'Expected HTTP status code'
    required: false
    default: '200'
  health-endpoint:
    description: 'Health check endpoint path'
    required: false
    default: '/health'
  readiness-endpoint:
    description: 'Readiness check endpoint path'
    required: false
    default: '/health/ready'
  liveness-endpoint:
    description: 'Liveness check endpoint path'
    required: false
    default: '/health/live'

outputs:
  health-status:
    description: 'Overall health status (healthy, degraded, unhealthy)'
    value: ${{ steps.health-check.outputs.status }}
  response-time:
    description: 'Average response time in milliseconds'
    value: ${{ steps.health-check.outputs.response-time }}
  health-details:
    description: 'Detailed health check results'
    value: ${{ steps.health-check.outputs.details }}
  readiness-status:
    description: 'Readiness check status'
    value: ${{ steps.readiness-check.outputs.status }}
  liveness-status:
    description: 'Liveness check status'
    value: ${{ steps.liveness-check.outputs.status }}

runs:
  using: 'composite'
  steps:
    - name: Install dependencies
      shell: bash
      run: |
        if ! command -v curl &> /dev/null; then
          echo "Installing curl..."
          sudo apt-get update && sudo apt-get install -y curl
        fi

        if ! command -v jq &> /dev/null; then
          echo "Installing jq..."
          sudo apt-get install -y jq
        fi

    - name: Perform liveness check
      id: liveness-check
      shell: bash
      run: |
        echo "🔍 Performing liveness check..."

        LIVENESS_URL="${{ inputs.service-url }}${{ inputs.liveness-endpoint }}"
        TIMEOUT=${{ inputs.timeout }}
        MAX_ATTEMPTS=${{ inputs.retry-attempts }}
        RETRY_DELAY=${{ inputs.retry-delay }}

        attempt=1
        success=false
        total_time=0

        while [ $attempt -le $MAX_ATTEMPTS ] && [ "$success" = false ]; do
          echo "Attempt $attempt/$MAX_ATTEMPTS: Checking liveness at $LIVENESS_URL"
          
          start_time=$(date +%s%3N)
          
          if response=$(curl -s -w "%{http_code}|%{time_total}" --max-time $TIMEOUT "$LIVENESS_URL" 2>/dev/null); then
            http_code=$(echo "$response" | tail -1 | cut -d'|' -f1)
            response_time=$(echo "$response" | tail -1 | cut -d'|' -f2)
            response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d'.' -f1)
            
            if [ "$http_code" = "${{ inputs.expected-status }}" ]; then
              echo "✅ Liveness check passed (HTTP $http_code, ${response_time_ms}ms)"
              echo "status=healthy" >> $GITHUB_OUTPUT
              echo "response-time=$response_time_ms" >> $GITHUB_OUTPUT
              success=true
            else
              echo "❌ Liveness check failed with HTTP $http_code"
            fi
          else
            echo "❌ Liveness check failed - no response"
          fi
          
          if [ "$success" = false ] && [ $attempt -lt $MAX_ATTEMPTS ]; then
            echo "Waiting ${RETRY_DELAY}s before retry..."
            sleep $RETRY_DELAY
          fi
          
          attempt=$((attempt + 1))
        done

        if [ "$success" = false ]; then
          echo "status=unhealthy" >> $GITHUB_OUTPUT
          echo "response-time=0" >> $GITHUB_OUTPUT
          echo "❌ Liveness check failed after $MAX_ATTEMPTS attempts"
          exit 1
        fi

    - name: Perform readiness check
      id: readiness-check
      shell: bash
      run: |
        echo "🔍 Performing readiness check..."

        READINESS_URL="${{ inputs.service-url }}${{ inputs.readiness-endpoint }}"
        TIMEOUT=${{ inputs.timeout }}
        MAX_ATTEMPTS=${{ inputs.retry-attempts }}
        RETRY_DELAY=${{ inputs.retry-delay }}

        attempt=1
        success=false

        while [ $attempt -le $MAX_ATTEMPTS ] && [ "$success" = false ]; do
          echo "Attempt $attempt/$MAX_ATTEMPTS: Checking readiness at $READINESS_URL"
          
          if response=$(curl -s -w "%{http_code}|%{time_total}" --max-time $TIMEOUT "$READINESS_URL" 2>/dev/null); then
            http_code=$(echo "$response" | tail -1 | cut -d'|' -f1)
            response_time=$(echo "$response" | tail -1 | cut -d'|' -f2)
            response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d'.' -f1)
            
            if [ "$http_code" = "${{ inputs.expected-status }}" ]; then
              echo "✅ Readiness check passed (HTTP $http_code, ${response_time_ms}ms)"
              echo "status=healthy" >> $GITHUB_OUTPUT
              echo "response-time=$response_time_ms" >> $GITHUB_OUTPUT
              success=true
            else
              echo "❌ Readiness check failed with HTTP $http_code"
            fi
          else
            echo "❌ Readiness check failed - no response"
          fi
          
          if [ "$success" = false ] && [ $attempt -lt $MAX_ATTEMPTS ]; then
            echo "Waiting ${RETRY_DELAY}s before retry..."
            sleep $RETRY_DELAY
          fi
          
          attempt=$((attempt + 1))
        done

        if [ "$success" = false ]; then
          echo "status=unhealthy" >> $GITHUB_OUTPUT
          echo "response-time=0" >> $GITHUB_OUTPUT
          echo "❌ Readiness check failed after $MAX_ATTEMPTS attempts"
          exit 1
        fi

    - name: Perform comprehensive health check
      id: health-check
      shell: bash
      run: |
        echo "🔍 Performing comprehensive health check..."

        HEALTH_URL="${{ inputs.service-url }}${{ inputs.health-endpoint }}"
        TIMEOUT=${{ inputs.timeout }}
        MAX_ATTEMPTS=${{ inputs.retry-attempts }}
        RETRY_DELAY=${{ inputs.retry-delay }}

        attempt=1
        success=false
        total_response_time=0

        while [ $attempt -le $MAX_ATTEMPTS ] && [ "$success" = false ]; do
          echo "Attempt $attempt/$MAX_ATTEMPTS: Checking health at $HEALTH_URL"
          
          if response=$(curl -s -w "%{http_code}|%{time_total}" --max-time $TIMEOUT "$HEALTH_URL" 2>/dev/null); then
            http_code=$(echo "$response" | tail -1 | cut -d'|' -f1)
            response_time=$(echo "$response" | tail -1 | cut -d'|' -f2)
            response_time_ms=$(echo "$response_time * 1000" | bc -l | cut -d'.' -f1)
            response_body=$(echo "$response" | head -n -1)
            
            total_response_time=$((total_response_time + response_time_ms))
            
            if [ "$http_code" = "${{ inputs.expected-status }}" ]; then
              echo "✅ Health check passed (HTTP $http_code, ${response_time_ms}ms)"
              
              # Parse health status from response
              if echo "$response_body" | jq -e . >/dev/null 2>&1; then
                health_status=$(echo "$response_body" | jq -r '.status // "unknown"')
                uptime=$(echo "$response_body" | jq -r '.uptime // 0')
                version=$(echo "$response_body" | jq -r '.version // "unknown"')
                environment=$(echo "$response_body" | jq -r '.environment // "unknown"')
                
                echo "Health Status: $health_status"
                echo "Uptime: ${uptime}ms"
                echo "Version: $version"
                echo "Environment: $environment"
                
                # Create detailed health summary
                health_details=$(cat << EOF
        {
          "status": "$health_status",
          "uptime": $uptime,
          "version": "$version",
          "environment": "$environment",
          "responseTime": $response_time_ms,
          "httpCode": $http_code,
          "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
        }
        EOF
                )
                
                echo "status=$health_status" >> $GITHUB_OUTPUT
                echo "response-time=$response_time_ms" >> $GITHUB_OUTPUT
                echo "details<<EOF" >> $GITHUB_OUTPUT
                echo "$health_details" >> $GITHUB_OUTPUT
                echo "EOF" >> $GITHUB_OUTPUT
                
                success=true
              else
                echo "⚠️ Health check returned non-JSON response"
                echo "status=degraded" >> $GITHUB_OUTPUT
                echo "response-time=$response_time_ms" >> $GITHUB_OUTPUT
                success=true
              fi
            else
              echo "❌ Health check failed with HTTP $http_code"
              if [ "$http_code" = "503" ]; then
                echo "Service is temporarily unavailable"
              fi
            fi
          else
            echo "❌ Health check failed - no response"
          fi
          
          if [ "$success" = false ] && [ $attempt -lt $MAX_ATTEMPTS ]; then
            echo "Waiting ${RETRY_DELAY}s before retry..."
            sleep $RETRY_DELAY
          fi
          
          attempt=$((attempt + 1))
        done

        if [ "$success" = false ]; then
          avg_response_time=$((total_response_time / MAX_ATTEMPTS))
          echo "status=unhealthy" >> $GITHUB_OUTPUT
          echo "response-time=$avg_response_time" >> $GITHUB_OUTPUT
          echo "details={\"status\":\"unhealthy\",\"error\":\"Health check failed after $MAX_ATTEMPTS attempts\"}" >> $GITHUB_OUTPUT
          echo "❌ Health check failed after $MAX_ATTEMPTS attempts"
          exit 1
        fi

    - name: Generate health check summary
      shell: bash
      run: |
        echo "## 🏥 Health Check Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Service URL:** ${{ inputs.service-url }}" >> $GITHUB_STEP_SUMMARY
        echo "**Overall Status:** ${{ steps.health-check.outputs.status }}" >> $GITHUB_STEP_SUMMARY
        echo "**Response Time:** ${{ steps.health-check.outputs.response-time }}ms" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Check Results" >> $GITHUB_STEP_SUMMARY
        echo "- **Liveness:** ${{ steps.liveness-check.outputs.status }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Readiness:** ${{ steps.readiness-check.outputs.status }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Health:** ${{ steps.health-check.outputs.status }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if [ "${{ steps.health-check.outputs.details }}" != "" ]; then
          echo "### Health Details" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo '${{ steps.health-check.outputs.details }}' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
        fi
