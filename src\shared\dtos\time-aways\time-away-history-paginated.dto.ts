import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdTimeAway } from './created-time-away.dto';
import { z } from 'zod';
import { paginateSchema } from '../pagination/paginated-base';
import { TimeAwayTypes } from '@/core/enums/time-away-types.enum';

export const timeAwayHistory = createdTimeAway
  .omit({ isSubtract: true, timeAwayTypeId: true })
  .extend({
    hours: z.coerce.number(),
    timeAwayType: z.nativeEnum(TimeAwayTypes),
  });

export class TimeAwayHistoryDto extends createZodDto(timeAwayHistory) {}

export class PaginatedTimeAwayHistoryDto extends createZodDto(
  paginateSchema(timeAwayHistory),
) {}
