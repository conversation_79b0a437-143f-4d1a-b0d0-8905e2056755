import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { paginationMeta } from '../pagination/page-meta.dto';
import { findAttendanceFilter } from './find-attendance-filter.dto';
import { findAttendanceSort } from './find-attendance-sort.dto';

export const findAttendanceQuery = z
  .object({
    filter: findAttendanceFilter,
    sort: z.object({ sort: findAttendanceSort }).partial(),
    paging: paginationMeta,
  })
  .partial();

export class FindAttendanceQueryDto extends createZodDto(findAttendanceQuery) {}
