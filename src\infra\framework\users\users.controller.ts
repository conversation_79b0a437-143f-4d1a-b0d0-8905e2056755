import { CreateUserInputDto } from '@/shared/dtos/users/create-user-input.dto';
import { PaginatedCreatedUsersDto } from '@/shared/dtos/users/created-user-paginated.dto';
import { CreatedUserDto } from '@/shared/dtos/users/created-user.dto';
import { FindUserQueryDto } from '@/shared/dtos/users/find-user-query.dto';
import { parseNestedQuery } from '@/shared/helpers/parse-nested-query';
import { CreateUserUseCase } from '@/use-cases/users/create-user.use-case';
import { FindUsersUseCase } from '@/use-cases/users/find-users.use-case';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import {
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Public } from '../auth/decorators/public.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';

@ApiTags('users')
@Controller('users')
@SwaggerAuth()
@ApiDefaultErrorMessage()
export class UsersController {
  constructor(
    private createUserUseCase: CreateUserUseCase,
    private findUsersUseCase: FindUsersUseCase,
  ) {}

  @Post()
  @Public()
  @ApiOperation({
    summary: 'Create a user',
  })
  @ApiCreatedResponse({ type: CreatedUserDto })
  @ApiConflictResponse({ description: 'The user already exists' })
  async create(@Body() data: CreateUserInputDto) {
    return this.createUserUseCase.execute(data);
  }

  @Get()
  @ApiQuery({
    type: FindUserQueryDto,
  })
  @ApiOperation({
    summary: 'Get a paginated list of users',
  })
  @ApiOkResponse({ type: PaginatedCreatedUsersDto })
  async findMany(@Query() query: unknown) {
    const { filter, paging, include, sort } = await parseNestedQuery(
      FindUserQueryDto.zodSchema,
      query,
    );
    return this.findUsersUseCase.execute(
      { ...filter },
      paging!,
      include?.include,
      sort?.sort,
    );
  }
}
