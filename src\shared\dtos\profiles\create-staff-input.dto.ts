import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { IdType } from '../base/base.dto';
import { createdProfile } from './created-profile.dto';
import { z } from 'zod';
import { createTimeAwayAdjustment } from '../time-aways/create-time-away-adjustment.dto';

export const createStaffInput = z.object({
  profile: createdProfile
    .omit({
      id: true,
      department: true,
      profilePicture: true,
      contract: true,
      activatedAt: true,
    })
    .extend({
      departmentId: IdType,
      profilePictureId: IdType,
      contractId: IdType,
    })
    .partial()
    .required({
      firstName: true,
      lastName: true,
      jobTitle: true,
      employmentStatus: true,
      workEmail: true,
      createdAt: true,
    }),
  leaveCredits: createTimeAwayAdjustment
    .pick({
      hours: true,
      timeAwayTypeId: true,
    })
    .array(),
});

export class CreateStaffInputDto extends createZodDto(createStaffInput) {}
