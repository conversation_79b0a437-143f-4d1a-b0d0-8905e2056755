import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { paginationMeta } from '../pagination/page-meta.dto';
import { findUserTimeAwayFilter } from './find-user-time-away-filter.dto';
import { findUserTimeAwaySort } from './find-user-time-away-sort.dto';

export const findUserTimeAwayQuery = z
  .object({
    filter: findUserTimeAwayFilter,
    sort: z.object({ sort: findUserTimeAwaySort }).partial(),
    paging: paginationMeta,
  })
  .partial();

export class FindUserTimeAwayQueryDto extends createZodDto(
  findUserTimeAwayQuery,
) {}
