import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';

export const countryHolidayIds = {
  'en.af#<EMAIL>': 'Afghanistan',
  'en.al#<EMAIL>': 'Albania',
  'en.dz#<EMAIL>': 'Algeria',
  'en.as#<EMAIL>': 'American Samoa',
  'en.ad#<EMAIL>': 'Andorra',
  'en.ao#<EMAIL>': 'Angola',
  'en.ai#<EMAIL>': 'Anguilla',
  'en.ag#<EMAIL>': 'Antigua and Barbuda',
  'en.ar#<EMAIL>': 'Argentina',
  'en.am#<EMAIL>': 'Armenia',
  'en.aw#<EMAIL>': 'Aruba',
  'en.australian#<EMAIL>': 'Australia',
  'en.austrian#<EMAIL>': 'Austria',
  'en.az#<EMAIL>': 'Azerbaijan',
  'en.bs#<EMAIL>': 'Bahamas',
  'en.bh#<EMAIL>': 'Bahrain',
  'en.bd#<EMAIL>': 'Bangladesh',
  'en.bb#<EMAIL>': 'Barbados',
  'en.by#<EMAIL>': 'Belarus',
  'en.be#<EMAIL>': 'Belgium',
  'en.bz#<EMAIL>': 'Belize',
  'en.bj#<EMAIL>': 'Benin',
  'en.bm#<EMAIL>': 'Bermuda',
  'en.bt#<EMAIL>': 'Bhutan',
  'en.bo#<EMAIL>': 'Bolivia',
  'en.ba#<EMAIL>': 'Bosnia and Herzegovina',
  'en.bw#<EMAIL>': 'Botswana',
  'en.brazilian#<EMAIL>': 'Brazil',
  'en.vg#<EMAIL>': 'British Virgin Islands',
  'en.bn#<EMAIL>': 'Brunei Darussalam',
  'en.bulgarian#<EMAIL>': 'Bulgaria',
  'en.bf#<EMAIL>': 'Burkina Faso',
  'en.bi#<EMAIL>': 'Burundi',
  'en.kh#<EMAIL>': 'Cambodia',
  'en.cm#<EMAIL>': 'Cameroon',
  'en.canadian#<EMAIL>': 'Canada',
  'en.cv#<EMAIL>': 'Cape Verde',
  'en.ky#<EMAIL>': 'Cayman Islands',
  'en.cf#<EMAIL>': 'Central African Republic',
  'en.td#<EMAIL>': 'Chad',
  'en.cl#<EMAIL>': 'Chile',
  'en.china#<EMAIL>': 'China',
  'en.co#<EMAIL>': 'Colombia',
  'en.km#<EMAIL>': 'Comoros',
  'en.cg#<EMAIL>': 'Congo',
  'en.ck#<EMAIL>': 'Cook Islands',
  'en.cr#<EMAIL>': 'Costa Rica',
  'en.ci#<EMAIL>': "Côte d'Ivoire",
  'en.croatian#<EMAIL>': 'Croatia',
  'en.cu#<EMAIL>': 'Cuba',
  'en.cw#<EMAIL>': 'Curaçao',
  'en.cy#<EMAIL>': 'Cyprus',
  'en.czech#<EMAIL>': 'Czechia',
  'en.kp#<EMAIL>':
    "Democratic People's Republic of Korea",
  'en.danish#<EMAIL>': 'Denmark',
  'en.dj#<EMAIL>': 'Djibouti',
  'en.dm#<EMAIL>': 'Dominica',
  'en.do#<EMAIL>': 'Dominican Republic',
  'en.ec#<EMAIL>': 'Ecuador',
  'en.eg#<EMAIL>': 'Egypt',
  'en.sv#<EMAIL>': 'El Salvador',
  'en.gq#<EMAIL>': 'Equatorial Guinea',
  'en.er#<EMAIL>': 'Eritrea',
  'en.ee#<EMAIL>': 'Estonia',
  'en.et#<EMAIL>': 'Ethiopia',
  'en.fk#<EMAIL>': 'Falkland Islands (Malvinas)',
  'en.fo#<EMAIL>': 'Faroe Islands',
  'en.fm#<EMAIL>': 'Federated States of Micronesia',
  'en.fj#<EMAIL>': 'Fiji',
  'en.finnish#<EMAIL>': 'Finland',
  'en.french#<EMAIL>': 'France',
  'en.gf#<EMAIL>': 'French Guiana',
  'en.pf#<EMAIL>': 'French Polynesia',
  'en.ga#<EMAIL>': 'Gabon',
  'en.gm#<EMAIL>': 'Gambia',
  'en.ge#<EMAIL>': 'Georgia',
  'en.german#<EMAIL>': 'Germany',
  'en.gh#<EMAIL>': 'Ghana',
  'en.gi#<EMAIL>': 'Gibraltar',
  'en.greek#<EMAIL>': 'Greece',
  'en.gl#<EMAIL>': 'Greenland',
  'en.gd#<EMAIL>': 'Grenada',
  'en.gu#<EMAIL>': 'Guam',
  'en.gt#<EMAIL>': 'Guatemala',
  'en.gg#<EMAIL>': 'Guernsey',
  'en.gn#<EMAIL>': 'Guinea',
  'en.gw#<EMAIL>': 'Guinea-Bissau',
  'en.gy#<EMAIL>': 'Guyana',
  'en.ht#<EMAIL>': 'Haiti',
  'en.va#<EMAIL>': 'Holy See (Vatican City State)',
  'en.hn#<EMAIL>': 'Honduras',
  'en.hong_kong#<EMAIL>': 'Hong Kong',
  'en.hungarian#<EMAIL>': 'Hungary',
  'en.is#<EMAIL>': 'Iceland',
  'en.indian#<EMAIL>': 'India',
  'en.indonesian#<EMAIL>': 'Indonesia',
  'en.iq#<EMAIL>': 'Iraq',
  'en.irish#<EMAIL>': 'Ireland',
  'en.ir#<EMAIL>': 'Islamic Republic of Iran',
  'en.im#<EMAIL>': 'Isle of Man',
  'en.jewish#<EMAIL>': 'Israel',
  'en.italian#<EMAIL>': 'Italy',
  'en.jm#<EMAIL>': 'Jamaica',
  'en.japanese#<EMAIL>': 'Japan',
  'en.je#<EMAIL>': 'Jersey',
  'en.jo#<EMAIL>': 'Jordan',
  'en.kz#<EMAIL>': 'Kazakhstan',
  'en.ke#<EMAIL>': 'Kenya',
  'en.ki#<EMAIL>': 'Kiribati',
  'en.kw#<EMAIL>': 'Kuwait',
  'en.kg#<EMAIL>': 'Kyrgyzstan',
  'en.la#<EMAIL>':
    "Lao People's Democratic Republic",
  'en.latvian#<EMAIL>': 'Latvia',
  'en.lb#<EMAIL>': 'Lebanon',
  'en.ls#<EMAIL>': 'Lesotho',
  'en.lr#<EMAIL>': 'Liberia',
  'en.ly#<EMAIL>': 'Libya',
  'en.li#<EMAIL>': 'Liechtenstein',
  'en.lithuanian#<EMAIL>': 'Lithuania',
  'en.lu#<EMAIL>': 'Luxembourg',
  'en.mo#<EMAIL>': 'Macao',
  'en.mg#<EMAIL>': 'Madagascar',
  'en.mw#<EMAIL>': 'Malawi',
  'en.malaysia#<EMAIL>': 'Malaysia',
  'en.mv#<EMAIL>': 'Maldives',
  'en.ml#<EMAIL>': 'Mali',
  'en.mt#<EMAIL>': 'Malta',
  'en.mh#<EMAIL>': 'Marshall Islands',
  'en.mq#<EMAIL>': 'Martinique',
  'en.mr#<EMAIL>': 'Mauritania',
  'en.mu#<EMAIL>': 'Mauritius',
  'en.yt#<EMAIL>': 'Mayotte',
  'en.mexican#<EMAIL>': 'Mexico',
  'en.md#<EMAIL>': 'Moldova',
  'en.mc#<EMAIL>': 'Monaco',
  'en.mn#<EMAIL>': 'Mongolia',
  'en.me#<EMAIL>': 'Montenegro',
  'en.ms#<EMAIL>': 'Montserrat',
  'en.ma#<EMAIL>': 'Morocco',
  'en.mz#<EMAIL>': 'Mozambique',
  'en.mm#<EMAIL>': 'Myanmar',
  'en.na#<EMAIL>': 'Namibia',
  'en.nr#<EMAIL>': 'Nauru',
  'en.np#<EMAIL>': 'Nepal',
  'en.dutch#<EMAIL>': 'Netherlands',
  'en.nc#<EMAIL>': 'New Caledonia',
  'en.new_zealand#<EMAIL>': 'New Zealand',
  'en.ni#<EMAIL>': 'Nicaragua',
  'en.ne#<EMAIL>': 'Niger',
  'en.ng#<EMAIL>': 'Nigeria',
  'en.mp#<EMAIL>': 'Northern Mariana Islands',
  'en.norwegian#<EMAIL>': 'Norway',
  'en.om#<EMAIL>': 'Oman',
  'en.pk#<EMAIL>': 'Pakistan',
  'en.pw#<EMAIL>': 'Palau',
  'en.pa#<EMAIL>': 'Panama',
  'en.pg#<EMAIL>': 'Papua New Guinea',
  'en.py#<EMAIL>': 'Paraguay',
  'en.pe#<EMAIL>': 'Peru',
  'en.philippines#<EMAIL>': 'Philippines',
  'en.polish#<EMAIL>': 'Poland',
  'en.portuguese#<EMAIL>': 'Portugal',
  'en.pr#<EMAIL>': 'Puerto Rico',
  'en.qa#<EMAIL>': 'Qatar',
  'en.south_korea#<EMAIL>': 'Republic of Korea',
  'en.re#<EMAIL>': 'Réunion',
  'en.romanian#<EMAIL>': 'Romania',
  'en.russian#<EMAIL>': 'Russian Federation',
  'en.rw#<EMAIL>': 'Rwanda',
  'en.bl#<EMAIL>': 'Saint Barthélemy',
  'en.sh#<EMAIL>': 'Saint Helena',
  'en.kn#<EMAIL>': 'Saint Kitts and Nevis',
  'en.lc#<EMAIL>': 'Saint Lucia',
  'en.mf#<EMAIL>': 'Saint Martin (French part)',
  'en.pm#<EMAIL>': 'Saint Pierre and Miquelon',
  'en.vc#<EMAIL>':
    'Saint Vincent and the Grenadines',
  'en.ws#<EMAIL>': 'Samoa',
  'en.sm#<EMAIL>': 'San Marino',
  'en.st#<EMAIL>': 'Sao Tome and Principe',
  'en.saudiarabian#<EMAIL>': 'Saudi Arabia',
  'en.sn#<EMAIL>': 'Senegal',
  'en.rs#<EMAIL>': 'Serbia',
  'en.sc#<EMAIL>': 'Seychelles',
  'en.sl#<EMAIL>': 'Sierra Leone',
  'en.singapore#<EMAIL>': 'Singapore',
  'en.sx#<EMAIL>': 'Sint Maarten (Dutch part)',
  'en.slovak#<EMAIL>': 'Slovakia',
  'en.slovenian#<EMAIL>': 'Slovenia',
  'en.sb#<EMAIL>': 'Solomon Islands',
  'en.so#<EMAIL>': 'Somalia',
  'en.sa#<EMAIL>': 'South Africa',
  'en.ss#<EMAIL>': 'South Sudan',
  'en.spain#<EMAIL>': 'Spain',
  'en.lk#<EMAIL>': 'Sri Lanka',
  'en.sd#<EMAIL>': 'Sudan',
  'en.sr#<EMAIL>': 'Suriname',
  'en.sz#<EMAIL>': 'Swaziland',
  'en.swedish#<EMAIL>': 'Sweden',
  'en.ch#<EMAIL>': 'Switzerland',
  'en.sy#<EMAIL>': 'Syrian Arab Republic',
  'en.taiwan#<EMAIL>': 'Taiwan',
  'en.tj#<EMAIL>': 'Tajikistan',
  'en.th#<EMAIL>': 'Thailand',
  'en.cd#<EMAIL>':
    'The Democratic Republic of the Congo',
  'en.mk#<EMAIL>':
    'The Former Yugoslav Republic of Macedonia',
  'en.tl#<EMAIL>': 'Timor-Leste',
  'en.tg#<EMAIL>': 'Togo',
  'en.to#<EMAIL>': 'Tonga',
  'en.tt#<EMAIL>': 'Trinidad and Tobago',
  'en.tn#<EMAIL>': 'Tunisia',
  'en.turkish#<EMAIL>': 'Turkey',
  'en.tm#<EMAIL>': 'Turkmenistan',
  'en.tc#<EMAIL>': 'Turks and Caicos Islands',
  'en.tv#<EMAIL>': 'Tuvalu',
  'en.vi#<EMAIL>': 'U.S. Virgin Islands',
  'en.ug#<EMAIL>': 'Uganda',
  'en.ukrainian#<EMAIL>': 'Ukraine',
  'en.ae#<EMAIL>': 'United Arab Emirates',
  'en.uk#<EMAIL>': 'United Kingdom',
  'en.tz#<EMAIL>': 'United Republic of Tanzania',
  'en.usa#<EMAIL>': 'United States',
  'en.uy#<EMAIL>': 'Uruguay',
  'en.uz#<EMAIL>': 'Uzbekistan',
  'en.vu#<EMAIL>': 'Vanuatu',
  'en.ve#<EMAIL>': 'Venezuela',
  'en.vietnamese#<EMAIL>': 'Vietnam',
  'en.wf#<EMAIL>': 'Wallis and Futuna',
  'en.ye#<EMAIL>': 'Yemen',
  'en.zm#<EMAIL>': 'Zambia',
  'en.zw#<EMAIL>': 'Zimbabwe',
};

export const calendarType = z.enum([
  'google',
  'google-holiday',
  'leaves',
  'birthdays',
  'anniversaries',
]);
export type CalendarType = z.infer<typeof calendarType>;

export const calendarTypeFilter = z
  .union([calendarType.array(), calendarType, z.literal('all')])
  .optional();
export type CalendarTypeFilter = z.infer<typeof calendarTypeFilter>;

export const listCalendarDateFilter = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
});
export type ListCalendarDateFilter = z.infer<typeof listCalendarDateFilter>;

export const listCalendarEventsInput = listCalendarDateFilter.extend({
  calendarTypes: calendarTypeFilter,
});

export class ListCalendarEventsInputDto extends createZodDto(
  listCalendarEventsInput,
) {}
