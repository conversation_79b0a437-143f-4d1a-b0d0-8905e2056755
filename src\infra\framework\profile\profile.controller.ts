import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreateStaffInputDto } from '@/shared/dtos/profiles/create-staff-input.dto';
import { PaginatedCreatedProfileDto } from '@/shared/dtos/profiles/created-profile-paginated.dto';
import { CreatedProfileDto } from '@/shared/dtos/profiles/created-profile.dto';
import { FindProfileQueryDto } from '@/shared/dtos/profiles/find-profile-query.dto';
import { UpdateProfileInputDto } from '@/shared/dtos/profiles/update-profile-input.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { parseNestedQuery } from '@/shared/helpers/parse-nested-query';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { ProfileService } from './profile.service';

@Controller('profile')
@ApiTags('profile')
@SwaggerAuth()
@ApiDefaultErrorMessage()
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get()
  @ApiOperation({
    summary: 'Get a paginated list of profiles',
  })
  @ApiQuery({
    type: FindProfileQueryDto,
  })
  @ApiOkResponse({ type: PaginatedCreatedProfileDto })
  async findAll(
    @CurrentUser() requester: CreatedUserDetailsDto,
    @Query() query: unknown,
  ) {
    const { filter, paging, sort } = await parseNestedQuery(
      FindProfileQueryDto.zodSchema,
      query,
    );
    return this.profileService.findProfiles(
      requester,
      { ...filter },
      paging as PaginationMetaDto,
      sort?.sort,
    );
  }

  @Post()
  @ApiOperation({
    summary: 'Create a staff profile',
    description: 'Allows an HR to create a staff profile',
  })
  @ApiCreatedResponse({ type: CreatedProfileDto })
  async createStaffProfile(
    @CurrentUser() creator: CreatedUserDetailsDto,
    @Body() input: CreateStaffInputDto,
  ) {
    return this.profileService.createStaff(creator, input);
  }

  @Get('/:profileId')
  @ApiOperation({
    summary: 'Get a profile by id',
  })
  @ApiOkResponse({ type: CreatedProfileDto })
  async findById(
    @CurrentUser() requester: CreatedUserDetailsDto,
    @Param('profileId') profileId: string,
  ) {
    return this.profileService.findOneProfile(requester, { id: profileId });
  }

  @Post('/:profileId/resend-invite')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Resend email invitation by id',
  })
  @ApiOkResponse({ type: CreatedProfileDto })
  async resendInvite(
    @CurrentUser() requester: CreatedUserDetailsDto,
    @Param('profileId') profileId: string,
  ) {
    return this.profileService.resendInvite(requester, profileId);
  }

  @Put('/:profileId')
  @ApiOperation({
    summary: 'Update a profile by id',
  })
  @ApiOkResponse({ type: CreatedProfileDto })
  async updateById(
    @Param('profileId') profileId: string,
    @Body() input: UpdateProfileInputDto,
    @CurrentUser() updater: CreatedUserDetailsDto,
  ) {
    return this.profileService.updateProfile(profileId, input, updater);
  }
}
