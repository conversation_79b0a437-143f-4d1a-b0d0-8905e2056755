{"name": "opus-remote-backend", "version": "1.0.0", "author": "<PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npx prisma generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/infra/framework/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install", "commit": "git-cz"}, "dependencies": {"@anatine/zod-openapi": "2.2.6", "@google-cloud/scheduler": "4.3.0", "@google-cloud/storage": "7.12.0", "@nestjs-cls/transactional": "2.4.2", "@nestjs-cls/transactional-adapter-prisma": "1.2.4", "@nestjs/axios": "3.0.2", "@nestjs/common": "10.3.9", "@nestjs/config": "3.2.2", "@nestjs/core": "10.3.9", "@nestjs/jwt": "10.2.0", "@nestjs/passport": "10.0.3", "@nestjs/platform-express": "10.3.9", "@nestjs/platform-socket.io": "10.3.10", "@nestjs/schedule": "4.1.0", "@nestjs/swagger": "7.3.1", "@nestjs/websockets": "10.3.10", "@prisma/client": "5.16.1", "@sendgrid/mail": "8.1.3", "@sentry/nestjs": "8.15.0", "@sentry/profiling-node": "8.15.0", "@slack/webhook": "7.0.3", "@wahyubucil/nestjs-zod-openapi": "0.1.2", "axios": "1.7.2", "bcrypt": "5.1.1", "cron": "3.1.7", "date-fns": "3.6.0", "date-fns-tz": "3.1.3", "gaxios": "6.7.0", "google-auth-library": "9.11.0", "googleapis": "140.0.1", "jspdf": "2.5.2", "jspdf-autotable": "3.8.3", "nestjs-cls": "4.4.1", "otpauth": "9.3.2", "papaparse": "5.4.1", "passport": "0.7.0", "passport-jwt": "4.0.1", "reflect-metadata": "0.2.2", "rimraf": "5.0.7", "rxjs": "7.8.1", "socket.io": "4.7.5", "zod": "3.23.8"}, "devDependencies": {"@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@nestjs/cli": "10.4.9", "@nestjs/schematics": "10.1.1", "@nestjs/testing": "10.3.9", "@types/bcrypt": "5.0.2", "@types/express": "4.17.21", "@types/jest": "29.5.12", "@types/multer": "1.4.11", "@types/node": "20.14.9", "@types/papaparse": "5.3.14", "@types/passport-jwt": "4.0.1", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "7.14.1", "@typescript-eslint/parser": "7.14.1", "commitizen": "4.3.0", "cz-conventional-changelog": "3.3.0", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-jsdoc": "48.7.0", "eslint-plugin-prettier": "5.1.3", "husky": "9.0.11", "jest": "29.7.0", "jest-junit": "^16.0.0", "lint-staged": "15.2.7", "prettier": "3.3.2", "pretty-quick": "4.0.0", "prisma": "5.16.1", "source-map-support": "0.5.21", "supertest": "7.0.0", "ts-jest": "29.1.5", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.5.3", "babel-jest": "^29.7.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.ts$": ["ts-jest", {"isolatedModules": true}], "^.+\\.js$": "babel-jest"}, "transformIgnorePatterns": ["node_modules/(?!(.*\\.mjs$))"], "reporters": ["default", "jest-junit"], "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.module.(t|j)s", "!**/*.e2e-spec.(t|j)s", "!infra/data/**/*.(t|j)s", "!**/main.(t|j)s"], "moduleNameMapper": {"^@/core(.*)$": "<rootDir>/core$1", "^@/infra(.*)$": "<rootDir>/infra$1", "^@/shared(.*)$": "<rootDir>/shared$1", "^@/use-cases(.*)$": "<rootDir>/use-cases$1"}, "coverageDirectory": "../coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/../jest.setup.js"], "coverageReporters": ["text", "lcov", "html", "json-summary"], "coverageThreshold": {"global": {"lines": 10, "functions": 5, "branches": 5, "statements": 10}}, "testTimeout": 10000, "maxWorkers": "50%", "forceExit": false, "verbose": false, "detectOpenHandles": true, "preset": "ts-jest"}, "prisma": {"schema": "./src/infra/data/prisma/schema.prisma", "seed": "ts-node ./src/infra/data/prisma/seed.ts"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "volta": {"node": "20.15.0", "npm": "10.8.1"}}