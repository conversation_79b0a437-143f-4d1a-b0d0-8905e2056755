import { UseCase } from '@/core/base/use-case';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class DeleteResourceUseCase implements UseCase<void> {
  constructor(private readonly resourcesRepository: ResourcesRepository) {}

  public async execute(
    id: string,
    deleter: CreatedUserDetailsDto,
  ): Promise<void> {
    const resourceEntity = await this.resourcesRepository.findOne({ id });

    if (!resourceEntity || resourceEntity.accountId !== deleter.accountId) {
      throw new EntityNotFoundException('Resource not found');
    }

    await this.resourcesRepository.remove(id);

    return;
  }
}
