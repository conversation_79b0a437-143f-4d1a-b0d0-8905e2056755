import { UserEntity } from '@/core/domain/entities/user.entity';
import { InsufficientPermissionException } from '@/core/exceptions/opus-exceptions';
import { UsersRepository } from '@/core/repositories/users.repository';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { REQUESTER_MATCH_KEY } from '../../constants';
import {
  RequesterMatchProps,
  RequesterMatchType,
} from '../decorators/requester-match.decorator';

@Injectable()
export class RequesterMatchGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private usersRepository: UsersRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const requesterMatchProps = this.reflector.get<RequesterMatchProps<any>>(
      REQUESTER_MATCH_KEY,
      context.getHandler(),
    );

    if (!requesterMatchProps) {
      return true;
    }

    const { userIdLocation, userIdParamName, AND, OR } = requesterMatchProps;

    if (!AND && !OR) {
      return true;
    }

    const userId = request[userIdLocation][userIdParamName];
    const requester: CreatedUserDetailsDto = request.user;

    let _user: UserEntity | null = null;
    const getUser = async () => {
      if (_user) return _user;
      _user = await this.usersRepository.findOne({ id: userId });
      return _user;
    };

    const error = new InsufficientPermissionException(
      'Insufficient Permission',
    );

    if (AND) {
      for (const { matchType } of AND) {
        if (
          matchType === RequesterMatchType.UserId &&
          requester.id !== userId
        ) {
          throw error;
        } else if (
          matchType === RequesterMatchType.AccountId &&
          requester.accountId !== (await getUser())?.accountId
        ) {
          throw error;
        }
      }
    }

    let hasError = false;
    if (OR) {
      for (const { matchType } of OR) {
        if (matchType === RequesterMatchType.UserId) {
          if (requester.id === userId) return true;

          hasError = true;
        } else if (matchType === RequesterMatchType.AccountId) {
          if (requester.accountId === (await getUser())?.accountId) return true;

          hasError = true;
        }
      }
    }

    if (hasError) {
      throw error;
    }

    return true;
  }
}
