import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdTimeAway } from './created-time-away.dto';
import { z } from 'zod';

export const createTimeAway = createdTimeAway
  .pick({
    hours: true,
    startDate: true,
    endDate: true,
    timeAwayTypeId: true,
    reason: true,
    userId: true,
  })
  .extend({
    hours: z.coerce.number().min(0).array(),
  })
  .partial({ reason: true });

export class CreateTimeAwayDto extends createZodDto(createTimeAway) {}
