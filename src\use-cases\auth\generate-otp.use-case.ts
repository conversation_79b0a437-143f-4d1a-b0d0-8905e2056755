import { Email<PERSON><PERSON> } from '@/core/abstracts/email-sender';
import { OtpGenerator } from '@/core/abstracts/otp-generator';
import { UseCase } from '@/core/base/use-case';
import { OtpPurpose } from '@/core/enums/otp-purpose.enum';
import { MailBuilderService } from '@/infra/framework/mailer/mail-builder.service';
import { GeneratedOtpDto } from '@/shared/dtos/auth/generated-otp.dto';
import { CreatedUserFullDto } from '@/shared/dtos/users/created-user-full.dto';

export class GenerateOtpUseCase implements UseCase<GeneratedOtpDto> {
  constructor(
    private readonly otpGenerator: OtpGenerator,
    private readonly emailSender: EmailSender,
    private readonly mailBuilderService: MailBuilderService,
  ) {}

  public async execute(
    requester: CreatedUserFullDto,
    purpose: OtpPurpose,
  ): Promise<GeneratedOtpDto> {
    const otp = await this.otpGenerator.generate(requester.id, purpose);
    const mailData = await this.mailBuilderService.buildOTPEmail(
      requester.email,
      otp,
      purpose,
    );

    await this.emailSender.send(mailData);

    return { otp };
  }
}
