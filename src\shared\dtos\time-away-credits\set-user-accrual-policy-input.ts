import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';

export const setUserAccrualPolicyInput = z.object({
  timeAwayAccrualPolicyId: IdType,
  users: z.union([
    z.object({
      all: z.boolean(),
    }),
    z.object({
      ids: IdType.array(),
    }),
  ]),
});
export class SetUserAccrualPolicyInput extends createZodDto(
  setUserAccrualPolicyInput,
) {}
