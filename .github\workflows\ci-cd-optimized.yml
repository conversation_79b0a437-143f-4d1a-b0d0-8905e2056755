name: 'Optimized CI/CD Pipeline - Fast Deployment'

on:
  push:
    branches: [staging, develop, 'hotfix/*']
  pull_request:
    branches: [staging]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
          - development

concurrency:
  group: '${{ github.workflow }}-${{ github.head_ref || github.ref }}-${{ inputs.environment || github.ref_name }}'
  cancel-in-progress: true

permissions:
  contents: 'read'
  id-token: 'write'
  deployments: 'write'

defaults:
  run:
    shell: 'bash'

env:
  IMAGE_NAME: opus-remote-backend
  # Enable Docker BuildKit for faster builds
  DOCKER_BUILDKIT: 1
  BUILDKIT_PROGRESS: plain

jobs:
  # Fast setup job - runs in parallel with build preparation
  setup:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.config.outputs.environment }}
      deploy: ${{ steps.config.outputs.deploy }}
      service_name: ${{ steps.config.outputs.service_name }}
      docker_tags: ${{ steps.config.outputs.docker_tags }}
      cpu_limit: ${{ steps.config.outputs.cpu_limit }}
      memory_limit: ${{ steps.config.outputs.memory_limit }}
      max_instances: ${{ steps.config.outputs.max_instances }}
      project_id: ${{ steps.config.outputs.project_id }}
      region: ${{ steps.config.outputs.region }}
      registry_url: ${{ steps.config.outputs.registry_url }}
    steps:
      - name: '🎯 Configure deployment settings'
        id: config
        run: |
          # Determine environment
          case "${{ github.event_name }}-${{ github.ref_name }}" in
            "workflow_dispatch-"*)
              ENV="${{ inputs.environment }}"
              ;;
            "push-staging"|"pull_request-staging")
              ENV="staging"
              ;;
            "push-main")
              ENV="production"
              ;;
            "push-develop")
              ENV="development"
              ;;
            "push-hotfix/"*)
              ENV="staging"  # Deploy hotfixes to staging first
              ;;
            *)
              ENV="none"
              ;;
          esac

          # Set outputs based on environment
          case "$ENV" in
            "staging")
              echo "environment=staging" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-staging" >> $GITHUB_OUTPUT
              echo "docker_tags=staging,latest" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=10" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.STAGING_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.STAGING_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.STAGING_GCP_REGION }}-docker.pkg.dev/${{ vars.STAGING_GCP_PROJECT_ID }}/${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "production")
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-production" >> $GITHUB_OUTPUT
              echo "docker_tags=production,v$(date +%Y%m%d)-${{ github.run_number }}" >> $GITHUB_OUTPUT
              echo "cpu_limit=2000m" >> $GITHUB_OUTPUT
              echo "memory_limit=1Gi" >> $GITHUB_OUTPUT
              echo "max_instances=50" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.PRODUCTION_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.PRODUCTION_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.PRODUCTION_GCP_REGION }}-docker.pkg.dev/${{ vars.PRODUCTION_GCP_PROJECT_ID }}/${{ vars.PRODUCTION_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "development")
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-dev" >> $GITHUB_OUTPUT
              echo "docker_tags=develop" >> $GITHUB_OUTPUT
              echo "cpu_limit=500m" >> $GITHUB_OUTPUT
              echo "memory_limit=256Mi" >> $GITHUB_OUTPUT
              echo "max_instances=3" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.STAGING_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.STAGING_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.STAGING_GCP_REGION }}-docker.pkg.dev/${{ vars.STAGING_GCP_PROJECT_ID }}/${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "environment=none" >> $GITHUB_OUTPUT
              echo "deploy=false" >> $GITHUB_OUTPUT
              ;;
          esac

  # Optimized build and deploy job
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.deploy == 'true'
    environment:
      name: ${{ needs.setup.outputs.environment }}
      url: ${{ steps.deploy.outputs.url }}
    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      # Parallel authentication and setup
      - name: '🔐 Authenticate to Google Cloud'
        uses: 'google-github-actions/auth@v2'
        with:
          workload_identity_provider: ${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GCP_WIF_PROVIDER || secrets.STAGING_GCP_WIF_PROVIDER }}
          service_account: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL }}

      - name: '☁️ Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          project_id: ${{ needs.setup.outputs.project_id }}

      # Setup Docker with caching
      - name: '🐳 Set up Docker Buildx'
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:buildx-stable-1
            network=host

      - name: '🔑 Configure Docker for Artifact Registry'
        run: |
          gcloud auth configure-docker ${{ needs.setup.outputs.region }}-docker.pkg.dev

      # Cache optimization for Node.js dependencies
      - name: '📦 Setup Node.js with caching'
        uses: 'actions/setup-node@v4'
        with:
          node-version-file: 'package.json'
          cache: 'npm'
          cache-dependency-path: 'package-lock.json'

      # Pre-install dependencies for faster Docker build
      - name: '⚡ Pre-install dependencies'
        run: |
          npm ci --prefer-offline --no-audit --no-fund
          npx prisma generate

      # Optimized Docker build with multi-stage caching
      - name: '🚀 Build and push Docker image with cache'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.optimized
          push: true
          platforms: linux/amd64
          tags: |
            ${{ needs.setup.outputs.registry_url }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
            ${{ needs.setup.outputs.registry_url }}/${{ env.IMAGE_NAME }}:latest
          cache-from: |
            type=gha,scope=${{ github.ref_name }}
            type=registry,ref=${{ needs.setup.outputs.registry_url }}/${{ env.IMAGE_NAME }}:cache
          cache-to: |
            type=gha,scope=${{ github.ref_name }},mode=max
            type=registry,ref=${{ needs.setup.outputs.registry_url }}/${{ env.IMAGE_NAME }}:cache,mode=max
          build-args: |
            BUILDKIT_INLINE_CACHE=1
            NODE_ENV=production

      # Fast deployment without unnecessary verification steps
      - name: '🚀 Deploy to Cloud Run'
        id: deploy
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: ${{ needs.setup.outputs.service_name }}
          region: ${{ needs.setup.outputs.region }}
          image: ${{ needs.setup.outputs.registry_url }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          env_vars: |
            NODE_ENV=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_NODE_ENV || vars.STAGING_NODE_ENV }}
            DATABASE_URL=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_URL || secrets.STAGING_DATABASE_URL }}
            JWT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_JWT_SECRET || secrets.STAGING_JWT_SECRET }}
            SENTRY_DSN=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENTRY_DSN || secrets.STAGING_SENTRY_DSN }}
            GATEWAY_PORT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GATEWAY_PORT || vars.STAGING_GATEWAY_PORT }}
            GOOGLE_PROJECT_ID=${{ needs.setup.outputs.project_id }}
            GOOGLE_CLIENT_ID=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_ID || secrets.STAGING_GOOGLE_CLIENT_ID }}
            GOOGLE_CLIENT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_SECRET || secrets.STAGING_GOOGLE_CLIENT_SECRET }}
            GOOGLE_BUCKET_NAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GOOGLE_BUCKET_NAME || vars.STAGING_GOOGLE_BUCKET_NAME }}
            SENDGRID_API_KEY=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENDGRID_API_KEY || secrets.STAGING_SENDGRID_API_KEY }}
            API_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_API_URL || vars.STAGING_API_URL }}
            WEB_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_WEB_URL || vars.STAGING_WEB_URL }}
            VERSION=${{ github.sha }}
            BRANCH=${{ github.ref_name }}
            ENVIRONMENT=${{ needs.setup.outputs.environment }}
            BUILD_NUMBER=${{ github.run_number }}
          flags: >
            --cpu=${{ needs.setup.outputs.cpu_limit }}
            --memory=${{ needs.setup.outputs.memory_limit }}
            --concurrency=80
            --max-instances=${{ needs.setup.outputs.max_instances }}
            --allow-unauthenticated
            --port=8080
            --timeout=300
            --execution-environment=gen2

  # Optional: Slack notification (runs in parallel, doesn't block deployment)
  notify-completion:
    runs-on: ubuntu-latest
    needs: [setup, build-and-deploy]
    if: always() && needs.setup.outputs.deploy == 'true'
    steps:
      - name: '📢 Notify deployment result'
        uses: './.github/actions/unified-slack-notify'
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: ${{ needs.build-and-deploy.result == 'success' && 'deployment-success' || 'deployment-failure' }}
          data: |
            {
              "branch": "${{ github.ref_name }}",
              "commit": "${{ github.sha }}",
              "environment": "${{ needs.setup.outputs.environment }}",
              "status": "${{ needs.build-and-deploy.result }}",
              "url": "${{ needs.build-and-deploy.outputs.url || 'N/A' }}",
              "duration": "${{ github.event.head_commit.timestamp }}"
            }