import {
  TokenManager,
  TokenManagerOptionsType,
} from '@/core/abstracts/token-manager';
import { InvalidJwtException } from '@/core/exceptions/opus-exceptions';
import { AuthTokensDto, TokenPayloadDto } from '@/shared/dtos/auth/token.dto';
import { CreatedUserDto } from '@/shared/dtos/users/created-user.dto';
import { Inject, Injectable } from '@nestjs/common';
import { JsonWebTokenError, JwtService } from '@nestjs/jwt';
import { addDays, addMinutes, getUnixTime } from 'date-fns';

@Injectable()
export class TokenManagerService extends TokenManager {
  static TOKEN_MANAGER_OPTIONS_KEY = 'token-manager-options-key';

  constructor(
    private readonly jwtService: JwtService,
    @Inject(TokenManagerService.TOKEN_MANAGER_OPTIONS_KEY)
    protected readonly options: TokenManagerOptionsType,
  ) {
    super(options);
  }

  async verify<T extends object = any>(token: string): Promise<T> {
    try {
      const parsed = await this.jwtService.verifyAsync<T>(token, this.options);
      return parsed;
    } catch (error) {
      if (error instanceof JsonWebTokenError) {
        throw new InvalidJwtException(error.message);
      } else {
        throw error;
      }
    }
  }
  async decode<T extends object = any>(token: string): Promise<T> {
    try {
      const parsed = this.jwtService.decode<T>(token);
      return parsed;
    } catch (error) {
      if (error instanceof JsonWebTokenError) {
        throw new InvalidJwtException(error.message);
      } else {
        throw error;
      }
    }
  }
  async sign(payload: TokenPayloadDto): Promise<string> {
    return this.jwtService.signAsync(payload);
  }
  async generateTokens(
    user: CreatedUserDto,
    overrideRefreshToken?: string,
  ): Promise<AuthTokensDto> {
    const now = new Date();

    const accessExpires = addMinutes(now, this.options.accessExpiryOffset);
    const refreshExpires = addDays(now, this.options.refreshExpiryOffset);

    const accessPayload = {
      sub: user.id,
      iat: getUnixTime(now),
      exp: getUnixTime(accessExpires),
    };

    const refreshPayload = {
      sub: user.id,
      iat: getUnixTime(now),
      exp: getUnixTime(refreshExpires),
    };

    const accessToken = await this.jwtService.signAsync(accessPayload);

    // Only generate the refreshToken if overrideRefreshToken is not provided
    const refreshToken =
      overrideRefreshToken ?? (await this.jwtService.signAsync(refreshPayload));

    return {
      accessToken,
      refreshToken,
    };
  }
}
