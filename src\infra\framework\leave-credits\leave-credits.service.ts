import { OpusRoles } from '@/core/enums/role.enum';
import { TimeAwayTypes } from '@/core/enums/time-away-types.enum';
import { InsufficientPermissionException } from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { PaginatedTotalUserTimeAwayCreditsDto } from '@/shared/dtos/time-away-credits/total-user-time-away-credits-paginated.dto';
import { TotalUserTimeAwayCreditsDto } from '@/shared/dtos/time-away-credits/total-user-time-away-credits.dto';
import { CreateTimeAwayAdjustmentInputDto } from '@/shared/dtos/time-aways/create-time-away-adjustment-input.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class LeaveCreditsService {
  constructor(private readonly prisma: PrismaService) {}
  async getLeaveCredits(userId: string): Promise<TotalUserTimeAwayCreditsDto> {
    const timeAwayTypes = await this.prisma.timeAwayType.findMany({
      where: { account: { users: { some: { id: userId } } } },
    });

    const data = await this.prisma.timeAway.findMany({
      where: { userId, isApproved: true },
      select: {
        userId: true,
        hours: true,
        isSubtract: true,
        timeAwayType: true,
      },
    });

    const grouped = timeAwayTypes.reduce<TotalUserTimeAwayCreditsDto['totals']>(
      (prev, curr) => {
        // Get the total per time away type
        const total = data.reduce((total, timeAway) => {
          if (timeAway.timeAwayType.description === curr.description) {
            const sum = timeAway.hours.reduce(
              (sum, currHour) =>
                sum + currHour * (timeAway.isSubtract ? -1 : 1),
              0,
            );
            // Remove floating point errors
            total = Math.round((total + sum) * 100000) / 100000;
          }
          return total;
        }, 0);
        prev.push({
          timeAwayType: curr.description as TimeAwayTypes,
          timeAwayTypeId: curr.id,
          total,
        });
        return prev;
      },
      [],
    );

    return {
      userId,
      totals: grouped,
    };
  }

  async getManyLeaveCredits(
    pagination: PaginationMetaDto,
    requester: CreatedUserDetailsDto,
  ): Promise<PaginatedTotalUserTimeAwayCreditsDto> {
    if (requester.role.title !== OpusRoles.Admin) {
      throw new InsufficientPermissionException('Insufficient permissions');
    }

    const { users, count } = await this.prisma.$transaction(async (tx) => {
      const where: Prisma.UserWhereInput = {
        accountId: requester.accountId,
        activatedAt: { not: null },
      };
      const users = await tx.user.findMany({
        where,
        select: { id: true },
      });
      const count = await tx.user.count({ where });

      return { users, count };
    });

    const userCredits: TotalUserTimeAwayCreditsDto[] = [];

    for (const user of users) {
      const credits = await this.getLeaveCredits(user.id);
      userCredits.push(credits);
    }

    const paging = getPaginationDetails(userCredits, pagination, count);

    return {
      data: userCredits,
      paging,
    };
  }

  async addCredits(
    input: CreateTimeAwayAdjustmentInputDto,
    requester: CreatedUserDetailsDto,
  ) {
    if (requester.role.title !== OpusRoles.Admin) {
      throw new InsufficientPermissionException();
    }

    const user = await this.prisma.user.findUnique({
      where: { id: input.userId },
      select: { accountId: true },
    });

    if (requester.accountId !== user?.accountId) {
      throw new InsufficientPermissionException();
    }

    await this.prisma.timeAway.create({
      data: {
        hours: [Math.abs(input.hours)],
        isSubtract: input.hours < 0,
        startDate: new Date(),
        endDate: new Date(),
        userId: input.userId,
        timeAwayTypeId: input.timeAwayTypeId,
        isApproved: true,
        isUserRequest: false,
        approvalMessage: input.reason,
        reason: input.reason,
      },
    });
  }
}
