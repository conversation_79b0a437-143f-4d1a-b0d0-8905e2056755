import { EmailSender } from '@/core/abstracts/email-sender';
import { FilesManager } from '@/core/abstracts/files-manager';
import { FilesRepository } from '@/core/repositories/files.repository';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { FilesManagerModule } from '@/infra/data/files-manager/files-manager.module';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { CreateResourceUseCase } from '@/use-cases/resources/create-resource.use-case';
import { DeleteResourceUseCase } from '@/use-cases/resources/delete-resource.use-case';
import { FindResourcesUseCase } from '@/use-cases/resources/find-resources.use-case';
import { UpdateResourceUseCase } from '@/use-cases/resources/update-resource.use-case';
import { Module } from '@nestjs/common';
import { MailBuilderService } from '../mailer/mail-builder.service';
import { MailerModule } from '../mailer/mailer.module';
import { ResourcesController } from './resources.controller';
import { ResourcesService } from './resources.service';

@Module({
  imports: [PrismaModule, FilesManagerModule, MailerModule],
  controllers: [ResourcesController],
  providers: [
    {
      provide: CreateResourceUseCase,
      useFactory: (
        resourcesRepository: ResourcesRepository,
        filesRepository: FilesRepository,
        filesManager: FilesManager,
        mailBuilder: MailBuilderService,
        emailSender: EmailSender,
        prisma: PrismaService,
      ) =>
        new CreateResourceUseCase(
          resourcesRepository,
          filesRepository,
          filesManager,
          mailBuilder,
          emailSender,
          prisma,
        ),

      inject: [
        ResourcesRepository,
        FilesRepository,
        FilesManager,
        MailBuilderService,
        EmailSender,
        PrismaService,
      ],
    },
    {
      provide: FindResourcesUseCase,
      useFactory: (resourcesRepository: ResourcesRepository) =>
        new FindResourcesUseCase(resourcesRepository),
      inject: [ResourcesRepository],
    },
    {
      provide: DeleteResourceUseCase,
      useFactory: (resourcesRepository: ResourcesRepository) =>
        new DeleteResourceUseCase(resourcesRepository),
      inject: [ResourcesRepository],
    },
    {
      provide: UpdateResourceUseCase,
      useFactory: (
        resourcesRepository: ResourcesRepository,
        filesRepository: FilesRepository,
        filesManager: FilesManager,
        mailBuilder: MailBuilderService,
        emailSender: EmailSender,
        prisma: PrismaService,
      ) =>
        new UpdateResourceUseCase(
          resourcesRepository,
          filesRepository,
          filesManager,
          mailBuilder,
          emailSender,
          prisma,
        ),
      inject: [
        ResourcesRepository,
        FilesRepository,
        FilesManager,
        MailBuilderService,
        EmailSender,
        PrismaService,
      ],
    },
    ResourcesService,
  ],
})
export class ResourcesModule {}
