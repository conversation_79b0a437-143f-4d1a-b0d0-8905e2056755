import { UseCase } from '@/core/base/use-case';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import { UsersRepository } from '@/core/repositories/users.repository';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { PaginatedCreatedUsersDto } from '@/shared/dtos/users/created-user-paginated.dto';
import { FindUserFilterDto } from '@/shared/dtos/users/find-user-filter.dto';
import { FindUserIncludeDto } from '@/shared/dtos/users/find-user-include.dto';
import { FindUserSortDto } from '@/shared/dtos/users/find-user-sort.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';

export class FindUsersUseCase implements UseCase<PaginatedCreatedUsersDto> {
  private createdUserMapper: CreatedUserMapper;

  constructor(private readonly repository: UsersRepository) {
    this.createdUserMapper = new CreatedUserMapper();
  }

  public async execute(
    filter: FindUserFilterDto,
    paginationMeta: PaginationMetaDto,
    include?: FindUserIncludeDto,
    sort?: FindUserSortDto,
  ): Promise<PaginatedCreatedUsersDto> {
    const { data, count } = await this.repository.findAll(
      filter,
      paginationMeta,
      include,
      sort,
    );
    return {
      data: data.map((user) => this.createdUserMapper.map(user)),
      paging: getPaginationDetails(data, paginationMeta, count),
    };
  }
}
