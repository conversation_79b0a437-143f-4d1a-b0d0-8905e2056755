import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';

export const refreshTokenInput = z
  .object({
    refreshToken: z
      .string()
      .describe('The token used to obtain a new access token'),
  })
  .describe(
    'The payload containing tokens used to authenticate requests to protected routes',
  );
export class RefreshTokenInputDto extends createZodDto(refreshTokenInput) {}

export const authTokens = refreshTokenInput
  .extend({
    accessToken: z
      .string()
      .describe('The token used to authenticate requests to protected routes'),
  })
  .describe(
    'The payload containing tokens used to authenticate requests to protected routes',
  );
export class AuthTokensDto extends createZodDto(authTokens) {}

export const tokenPayload = z.object({
  sub: z.string().describe('The user id'),
  iat: z.number().describe('The issue time of token in UNIX time'),
  exp: z.number().describe('The expiration of the token in UNIX time'),
  iss: z.string().describe('The issuer').optional(),
  aud: z.enum(['activate']).describe('The intended recipient').optional(),
});
export class TokenPayloadDto extends createZodDto(tokenPayload) {}

export const activateUserInput = z.object({
  token: z.string(),
});
export class ActivateUserInput extends createZodDto(activateUserInput) {}
