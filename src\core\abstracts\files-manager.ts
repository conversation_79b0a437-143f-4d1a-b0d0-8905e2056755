import { parse } from 'path';
import { uid } from 'uid';

export abstract class FilesManager {
  /**
   * Creates a file in the server then return the file name of that file in the server
   *
   * @param originalFilename The original filename of the file
   * @param file The file to be uploaded
   * @returns The file name of the created file in the server
   */
  abstract upload(originalFileName: string, file: Buffer): Promise<string>;

  /**
   * Reads the file the server by its file name
   *
   * @param filename The file name to be read
   * @returns The instance of the file in the server
   */
  abstract download(fileName: string): Promise<Buffer>;

  randomizeFileName(fileName: string): string {
    const parsed = parse(fileName);
    const name = parsed.name.replace(/[\s/]/g, '_').toLowerCase();
    return `${name}-${uid(21)}${parsed.ext}`;
  }
}
