{"compilerOptions": {"module": "commonjs", "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/core/*": ["./src/core/*"], "@/infra/*": ["./src/infra/*"], "@/shared/*": ["./src/shared/*"], "@/use-cases/*": ["./src/use-cases/*"]}, "incremental": true, "skipLibCheck": true, "strict": true, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "ts-node": {"require": ["tsconfig-paths/register"]}}