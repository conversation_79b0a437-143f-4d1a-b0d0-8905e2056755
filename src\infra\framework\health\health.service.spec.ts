import { Test, TestingModule } from '@nestjs/testing';
import { HealthService } from './health.service';
import { PrismaService } from '@/infra/data/prisma/prisma.service';

describe('HealthService', () => {
  let service: HealthService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const mockPrismaService = {
      $queryRaw: jest.fn().mockResolvedValue([{ '?column?': 1 }]),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('checkHealth', () => {
    it('should return healthy status when all checks pass', async () => {
      const result = await service.checkHealth();

      // The overall status can be 'healthy' or 'degraded' depending on system performance
      // but should not be 'unhealthy' when all services are accessible
      expect(['healthy', 'degraded']).toContain(result.status);
      expect(result.services.database.status).toBe('healthy');
      expect(result.services.memory.status).toBeDefined();
      expect(result.services.disk.status).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.uptime).toBeGreaterThan(0);
      expect(result.version).toBeDefined();
      expect(result.environment).toBeDefined();
    });

    it('should return unhealthy status when database check fails', async () => {
      jest
        .spyOn(prismaService, '$queryRaw')
        .mockRejectedValue(new Error('Database connection failed'));

      const result = await service.checkHealth();

      expect(result.status).toBe('unhealthy');
      expect(result.services.database.status).toBe('unhealthy');
      expect(result.services.database.error).toBe('Database connection failed');
    });
  });

  describe('checkReadiness', () => {
    it('should return healthy status when database is ready', async () => {
      const result = await service.checkReadiness();

      expect(result.status).toBe('healthy');
      expect(result.services.database.status).toBe('healthy');
      expect(prismaService.$queryRaw).toHaveBeenCalled();
    });

    it('should return unhealthy status when database is not ready', async () => {
      jest
        .spyOn(prismaService, '$queryRaw')
        .mockRejectedValue(new Error('Database not ready'));

      const result = await service.checkReadiness();

      expect(result.status).toBe('unhealthy');
      expect(result.services.database.status).toBe('unhealthy');
    });
  });

  describe('checkLiveness', () => {
    it('should always return healthy status', async () => {
      const result = await service.checkLiveness();

      expect(result.status).toBe('healthy');
      expect(result.timestamp).toBeDefined();
      expect(result.uptime).toBeGreaterThanOrEqual(0);
    });
  });
});
