import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';

export const paginationMeta = z
  .object({
    page: z.coerce
      .number()
      .int()
      .min(1)
      .default(1)
      .describe('The page to be queried'),
    limit: z.coerce
      .number()
      .int()
      .min(1)
      .default(50)
      .describe('The number of entities to be returned'),
  })
  .default({ page: 1, limit: 50 });

export class PaginationMetaDto extends createZodDto(paginationMeta) {}
