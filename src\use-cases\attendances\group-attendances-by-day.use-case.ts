import { UseCase } from '@/core/base/use-case';
import { TimeAwaysRepository } from '@/core/repositories/time-aways.repository';
import { ProfileService } from '@/infra/framework/profile/profile.service';
import { FindUserAttendanceFilterDto } from '@/shared/dtos/attendances/find-user-attendance-filter.dto';
import {
  DayAttendanceDetails,
  DaylogStatus,
  DayLogWithStatus,
  GroupedUserDayAttendanceWithStatus,
  PaginatedGroupedDayAttendanceDto,
} from '@/shared/dtos/attendances/grouped-day-attendance.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';
import { GroupUserAttendancesByDayUseCase } from './group-user-attendances-by-day.use-case';

export class GroupAttendancesByDayUseCase
  implements UseCase<PaginatedGroupedDayAttendanceDto>
{
  constructor(
    private readonly timeAwaysRepository: TimeAwaysRepository,
    private readonly groupUserAttendancesByDayUseCase: GroupUserAttendancesByDayUseCase,

    private readonly profilesService: ProfileService,
  ) {}

  public async execute(
    accountId: string,
    timezone: string,
    filter: FindUserAttendanceFilterDto,
    paging: PaginationMetaDto,
    requester: CreatedUserDetailsDto,
  ): Promise<PaginatedGroupedDayAttendanceDto> {
    const {
      data: profiles,
      paging: { count: totalEmployees },
    } = await this.profilesService.findProfiles(
      requester,
      {
        accountId,
        isActivated: true,
      },
      paging,
    );

    const { data: timeAways } = await this.timeAwaysRepository.findAll({
      accountId,
      startDate: filter.startDate,
      endDate: filter.endDate,
      isApproved: true,
      isUserRequest: true,
    });

    const logs: GroupedUserDayAttendanceWithStatus[] = [];
    for (const profile of profiles) {
      const {
        dayLogs: _dayLogs,
        totalMillis,
        userId,
      } = await this.groupUserAttendancesByDayUseCase.execute(
        profile.id,
        timezone,
        filter,
      );

      const dayLogs = _dayLogs.map<DayLogWithStatus>((currLog) => {
        const currentJsDate = new Date(currLog.day);
        const hasLeave = timeAways.find(
          (timeAway) =>
            timeAway.userId == userId &&
            timeAway.startDate <= currentJsDate &&
            timeAway.endDate >= currentJsDate,
        );

        const status = hasLeave
          ? DaylogStatus.onLeave
          : !currLog.entries.length
            ? DaylogStatus.pending
            : DaylogStatus.present;

        return { ...currLog, status };
      });

      logs.push({ userId, dayLogs, totalMillis });
    }

    const attendanceCountPerDay = logs.reduce((prev, curr) => {
      curr.dayLogs.map((currLog) => {
        let dailyCount = prev.find(
          (attendanceCount) => attendanceCount.day == currLog.day,
        );

        if (!dailyCount) {
          dailyCount = {
            day: currLog.day,
            present: 0,
            onLeave: 0,
            pending: 0,
            totalEmployees,
          };
          prev.push(dailyCount);
        }

        switch (currLog.status) {
          case DaylogStatus.present:
            dailyCount.present += 1;
            break;
          case DaylogStatus.onLeave:
            dailyCount.onLeave += 1;
            break;
          case DaylogStatus.pending:
            dailyCount.pending += 1;
            break;
        }
      });
      return prev;
    }, [] as DayAttendanceDetails[]);

    return {
      profiles: profiles.map((profile) => ({
        id: profile.id,
        firstName: profile.firstName,
        lastName: profile.lastName,
        department: profile.department,
        profilePicture: profile.profilePicture,
      })),
      data: logs,
      days: attendanceCountPerDay,
      paging: getPaginationDetails(profiles, paging, totalEmployees),
    };
  }
}
