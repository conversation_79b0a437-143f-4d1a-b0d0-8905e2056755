/*
  Warnings:

  - You are about to drop the column `approverId` on the `time_away_approval_history` table. All the data in the column will be lost.
  - You are about to drop the column `isApproved` on the `time_away_approval_history` table. All the data in the column will be lost.
  - Added the required column `approver_user_id` to the `time_away_approval_history` table without a default value. This is not possible if the table is not empty.
  - Added the required column `is_approved` to the `time_away_approval_history` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "time_away_approval_history" DROP CONSTRAINT "time_away_approval_history_approverId_fkey";

-- AlterTable
ALTER TABLE "time_away_approval_history" DROP COLUMN "approverId",
DROP COLUMN "isApproved",
ADD COLUMN     "approver_id" TEXT,
ADD COLUMN     "approver_user_id" TEXT NOT NULL,
ADD COLUMN     "is_approved" BOOLEAN NOT NULL;

-- AddF<PERSON><PERSON><PERSON>ey
ALTER TABLE "time_away_approval_history" ADD CONSTRAINT "time_away_approval_history_approver_user_id_fkey" FOREIGN KEY ("approver_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
