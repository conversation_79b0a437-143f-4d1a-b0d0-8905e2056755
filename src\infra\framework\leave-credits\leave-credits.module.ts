import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { Module } from '@nestjs/common';
import { LeaveCreditsController } from './leave-credits.controller';
import { LeaveCreditsService } from './leave-credits.service';

@Module({
  imports: [PrismaModule],
  controllers: [LeaveCreditsController],
  providers: [LeaveCreditsService],
  exports: [LeaveCreditsService],
})
export class LeaveCreditsModule {}
