import { TokenManager } from '@/core/abstracts/token-manager';
import { UseCase } from '@/core/base/use-case';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import {
  InvalidCredentialException,
  InvalidJwtException,
} from '@/core/exceptions/opus-exceptions';
import { UsersRepository } from '@/core/repositories/users.repository';
import {
  AuthTokensDto,
  RefreshTokenInputDto,
  TokenPayloadDto,
} from '@/shared/dtos/auth/token.dto';

export class RefreshTokenUseCase implements UseCase<AuthTokensDto> {
  private readonly createdUserMapper: CreatedUserMapper;
  constructor(
    private readonly tokenManager: TokenManager,
    private readonly usersRepository: UsersRepository,
  ) {
    this.createdUserMapper = new CreatedUserMapper();
  }

  public async execute(input: RefreshTokenInputDto): Promise<AuthTokensDto> {
    const payload = await this.tokenManager.verify<TokenPayloadDto>(
      input.refreshToken,
    );
    const user = await this.usersRepository.findOne({ id: payload.sub });

    if (!user) {
      throw new InvalidCredentialException('User not registered');
    }

    if (
      user?.lastPasswordChange &&
      user.lastPasswordChange.valueOf() > payload.iat * 1000
    ) {
      throw new InvalidJwtException('User password was changed');
    }

    return this.tokenManager.generateTokens(
      this.createdUserMapper.map(user),
      input.refreshToken,
    );
  }
}
