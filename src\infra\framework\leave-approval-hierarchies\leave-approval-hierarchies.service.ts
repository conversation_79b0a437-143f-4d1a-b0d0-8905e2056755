import { OpusRoles } from '@/core/enums/role.enum';
import { InsufficientPermissionException } from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import {
  TimeAwayApprovalHierarchy,
  UpdateTimeAwayApprovalHierarchy,
} from '@/shared/dtos/time-away-approval-hierarchies/time-away-approver.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { Injectable } from '@nestjs/common';
import { TimeAwayApproverType } from '@prisma/client';

@Injectable()
export class LeaveApprovalHierarchiesService {
  constructor(private readonly prisma: PrismaService) {}

  async setApprovalHierarchy(
    input: UpdateTimeAwayApprovalHierarchy,
    user: CreatedUserDetailsDto,
  ) {
    if (user.role.title !== OpusRoles.Admin || !user.accountId) {
      throw new InsufficientPermissionException();
    }

    let approvalHierarchy =
      await this.prisma.timeAwayApprovalHierarchy.findUnique({
        where: { accountId: user.accountId },
        include: { checkList: { select: { id: true } } },
      });

    if (!approvalHierarchy) {
      approvalHierarchy = await this.prisma.timeAwayApprovalHierarchy.create({
        data: { accountId: user.accountId },
        include: { checkList: { select: { id: true } } },
      });
    }

    await this.prisma.$transaction(async (tx) => {
      const presentIds = input.hierarchy.reduce((presentIds, curr) => {
        if (curr.id !== undefined) {
          presentIds.push(curr.id);
        }
        return presentIds;
      }, [] as string[]);
      await tx.timeAwayApprover.updateMany({
        where: {
          approvalHierarchyId: approvalHierarchy.id,
          id: { notIn: presentIds },
        },
        data: { approvalHierarchyId: null },
      });

      const approverIds = [];
      for (let i = 0; i < input.hierarchy.length; i++) {
        const currentApprover = input.hierarchy[i];
        const data: {
          approverType: TimeAwayApproverType;
          approvalHierarchyId: string;
          approverRoleId: string | null;
          approverUserId: string | null;
          order: number;
        } = {
          approverType: currentApprover.approver.approverType,
          approvalHierarchyId: approvalHierarchy.id,
          approverRoleId: null,
          approverUserId: null,
          order: i,
        };

        switch (currentApprover.approver.approverType) {
          case 'DEPARTMENT_HEAD':
            data.approverRoleId = null;
            data.approverUserId = null;
            break;
          case 'SPECIFIC_PERSON':
            data.approverRoleId = null;
            data.approverUserId = currentApprover.approver.userId;
            break;
          case 'SPECIFIC_ROLE':
            data.approverRoleId = currentApprover.approver.roleId;
            data.approverUserId = null;
            break;
        }

        const existingApprover = await tx.timeAwayApprover.findFirst({
          where: {
            id: currentApprover.id,
            approvalHierarchyId: approvalHierarchy.id,
          },
        });

        const approver =
          currentApprover.id && existingApprover
            ? await tx.timeAwayApprover.update({
                where: {
                  id: existingApprover.id,
                },
                data,
              })
            : await tx.timeAwayApprover.create({
                data,
              });

        approverIds.push(approver.id);
      }

      const unapprovedTimeAways = await tx.timeAway.findMany({
        where: { user: { accountId: user.accountId }, isApproved: null },
      });

      // Attach new time away histories and questions for unapproved leaves
      for (const timeAway of unapprovedTimeAways) {
        await tx.timeAwayCheckListItemAnswer.deleteMany({
          where: { historyItem: { timeAwayId: timeAway.id } },
        });
        await tx.timeAwayApprovalHistoryItem.deleteMany({
          where: {
            timeAwayId: timeAway.id,
          },
        });
        await tx.timeAway.update({
          where: { id: timeAway.id },
          data: {
            approvalHistory: {
              createMany: {
                data: approverIds.map((approverId) => {
                  return { approverId: approverId };
                }),
              },
            },
          },
        });

        const history = await tx.timeAwayApprovalHistoryItem.findMany({
          where: { timeAwayId: timeAway.id },
        });

        for (const item of history) {
          await tx.timeAwayApprovalHistoryItem.update({
            where: { id: item.id },
            data: {
              checkListAnswers: {
                createMany: {
                  data: approvalHierarchy.checkList.map((checkListItem) => {
                    return { checkListItemId: checkListItem.id };
                  }),
                },
              },
            },
          });
        }
      }
    });
  }

  async getApprovalHierarchy(
    user: CreatedUserDetailsDto,
  ): Promise<TimeAwayApprovalHierarchy> {
    const hierarchy = (await this.prisma.timeAwayApprovalHierarchy.findUnique({
      where: { accountId: user.account?.id },
      include: {
        approvers: {
          orderBy: { order: 'asc' },
          include: {
            approverUser: { select: { firstName: true, lastName: true } },
            approverRole: true,
          },
        },
        checkList: { orderBy: { order: 'asc' } },
      },
    })) ?? { approvers: [], checkList: [] };

    return {
      approvers: hierarchy.approvers.map((currentApprover) => {
        let approver: TimeAwayApprovalHierarchy['approvers'][0] | undefined =
          undefined;
        const ids = {
          id: currentApprover.id,
        };
        switch (currentApprover.approverType) {
          case 'DEPARTMENT_HEAD':
            approver = {
              ...ids,
              approver: { approverType: 'DEPARTMENT_HEAD' },
            };
            break;
          case 'SPECIFIC_PERSON':
            approver = {
              ...ids,
              approver: {
                approverType: 'SPECIFIC_PERSON',
                userId: currentApprover.approverUserId!,
                firstName: currentApprover?.approverUser?.firstName ?? '',
                lastName: currentApprover?.approverUser?.lastName ?? '',
              },
            };
            break;
          case 'SPECIFIC_ROLE':
            approver = {
              ...ids,
              approver: {
                approverType: 'SPECIFIC_ROLE',
                roleId: currentApprover.approverRoleId!,
                roleName: currentApprover?.approverRole?.title ?? '',
              },
            };
            break;
        }
        return approver;
      }),
      checklist: hierarchy.checkList.map((item) => {
        return {
          id: item.id,
          question: item.question,
        };
      }),
    };
  }
}
