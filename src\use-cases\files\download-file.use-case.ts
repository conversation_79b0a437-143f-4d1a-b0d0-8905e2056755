import { FilesManager } from '@/core/abstracts/files-manager';
import { UseCase } from '@/core/base/use-case';
import { CreatedFileMapper } from '@/core/domain/mappers/files/created-file.mapper';
import { FilePermission } from '@/core/enums/file.enum';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { DownloadFileInputDto } from '@/shared/dtos/files/download-file-input.dto';
import { FileDto } from '@/shared/dtos/files/file.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class DownloadFileUseCase implements UseCase<FileDto> {
  private readonly createdFileMapper: CreatedFileMapper;

  constructor(
    private readonly filesRepository: FilesRepository,
    private readonly filesManager: FilesManager,
  ) {
    this.createdFileMapper = new CreatedFileMapper();
  }

  public async execute(
    data: DownloadFileInputDto,
    downloader?: CreatedUserDetailsDto,
  ): Promise<FileDto> {
    const createdFile = await this.filesRepository.findOne(data, {
      uploader: true,
    });

    if (!createdFile) {
      throw new EntityNotFoundException();
    }

    switch (createdFile.permission) {
      case FilePermission.Public:
        break;
      case FilePermission.Account:
        if (!downloader) {
          throw new EntityNotFoundException();
        }
        if (downloader.accountId != createdFile.uploader?.accountId) {
          throw new EntityNotFoundException();
        }
    }
    const mappedFile = this.createdFileMapper.map(createdFile);
    const content = await this.filesManager.download(createdFile.fileName);

    return { content, ...mappedFile };
  }
}
