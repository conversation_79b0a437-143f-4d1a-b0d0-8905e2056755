import { UseCase } from '@/core/base/use-case';
import { CreatedAccountMapper } from '@/core/domain/mappers/accounts/created-account.mapper';
import { AccountsRepository } from '@/core/repositories/accounts.repository';
import { PaginatedCreatedAccountsDto } from '@/shared/dtos/accounts/created-account-paginated.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';

export class FindAccountsUseCase
  implements UseCase<PaginatedCreatedAccountsDto>
{
  private createdAccountMapper: CreatedAccountMapper;

  constructor(private readonly repository: AccountsRepository) {
    this.createdAccountMapper = new CreatedAccountMapper();
  }

  public async execute(
    paginationMeta: PaginationMetaDto,
  ): Promise<PaginatedCreatedAccountsDto> {
    const { data, count } = await this.repository.findAll({}, paginationMeta);
    return {
      data: data.map((user) => this.createdAccountMapper.map(user)),
      paging: getPaginationDetails(data, paginationMeta, count),
    };
  }
}
