import { EmailSender } from '@/core/abstracts/email-sender';
import { FilesManager } from '@/core/abstracts/files-manager';
import { UseCase } from '@/core/base/use-case';
import { FileEntity } from '@/core/domain/entities/file.entity';
import { CreatedFileMapper } from '@/core/domain/mappers/files/created-file.mapper';
import { CreateResourceMapper } from '@/core/domain/mappers/resources/create-resource.mapper';
import { CreatedResourceMapper } from '@/core/domain/mappers/resources/created-resource.mapper';
import { EntityConflictException } from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { MailBuilderService } from '@/infra/framework/mailer/mail-builder.service';
import { FileDto } from '@/shared/dtos/files/file.dto';
import { CreateResourceInputDto } from '@/shared/dtos/resources/create-resource-input.dto';
import { CreatedResourceDto } from '@/shared/dtos/resources/created-resource.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class CreateResourceUseCase implements UseCase<CreatedResourceDto> {
  private createResourceMapper: CreateResourceMapper;
  private createdResourceMapper: CreatedResourceMapper;
  private createdFileMapper: CreatedFileMapper;

  constructor(
    private readonly resourcesRepository: ResourcesRepository,
    private readonly filesRepository: FilesRepository,

    private readonly filesManager: FilesManager,
    private readonly mailBuilder: MailBuilderService,
    private readonly emailSender: EmailSender,
    private readonly prisma: PrismaService,
  ) {
    this.createResourceMapper = new CreateResourceMapper();
    this.createdResourceMapper = new CreatedResourceMapper();
    this.createdFileMapper = new CreatedFileMapper();
  }

  public async execute(
    data: CreateResourceInputDto,
    uploader: CreatedUserDetailsDto,
  ): Promise<CreatedResourceDto> {
    if (!uploader.accountId) {
      throw new EntityConflictException('User has no corresponding account');
    }

    const resourceEntity = await this.resourcesRepository.create(
      this.createResourceMapper.map({
        ...data,
        uploaderId: uploader.id,
        accountId: uploader.accountId,
      }),
    );

    const attachments: FileEntity[] = [];
    const mailAttachments: FileDto[] = [];

    for (const attachmentId of data.attachmentIds) {
      const updatedFile = await this.filesRepository.update(attachmentId, {
        resourceId: resourceEntity.id,
      });
      attachments.push(updatedFile);

      if (!data.draft) {
        const content = await this.filesManager.download(updatedFile.fileName);
        mailAttachments.push({
          ...this.createdFileMapper.map(updatedFile),
          content,
        });
      }
    }

    if (!data.draft) {
      const account = await this.prisma.account.findUnique({
        where: { id: uploader.accountId },
        include: {
          users: {
            select: { firstName: true, email: true },
            where: { activatedAt: { not: null } },
          },
        },
      });
      if (account?.users) {
        for (const user of account.users) {
          const mailData = await this.mailBuilder.buildAnnouncementEmail(
            resourceEntity.title,
            resourceEntity.content,
            uploader.firstName,
            user.firstName,
            user.email,
            resourceEntity.resourceType,
            mailAttachments,
          );
          void this.emailSender.send(mailData);
        }
      }
    }

    return this.createdResourceMapper.map({
      ...resourceEntity,
      attachments: attachments,
    });
  }
}
