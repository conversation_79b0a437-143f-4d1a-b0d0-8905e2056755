import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';

export const createdRole = z.object({
  id: IdType.describe('The unique identifier of the role'),
  title: z.string().describe('The title of the role'),
  description: z.string().describe('The description of the role'),
});

export class CreatedRoleDto extends createZodDto(createdRole) {}
