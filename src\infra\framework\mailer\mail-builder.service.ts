import { MailData } from '@/core/abstracts/email-sender';
import { OtpPurpose } from '@/core/enums/otp-purpose.enum';
import { Injectable } from '@nestjs/common';
import { OpusConfig } from '../opus-config';
import { FileDto } from '@/shared/dtos/files/file.dto';
import { ResourceTypeType } from '@/core/enums/resource-type.enum';
import { format, toZonedTime } from 'date-fns-tz';

@Injectable()
export class MailBuilderService {
  async buildPasswordChangeEmail(email: string): Promise<MailData> {
    return {
      to: email,
      text: `Your password has been updated.`,
      html: `Your password has been updated.`,
      subject: 'Password Changed',
    };
  }

  async buildOTPEmail(
    email: string,
    otp: string,
    purpose: OtpPurpose,
  ): Promise<MailData> {
    const text = `Your OTP is
${otp}
`;
    const html = `
Your OTP is<br>
<h2>${otp}</h2>
`;

    return {
      subject: `OTP - ${purpose}`,
      to: email,
      text,
      html,
    };
  }

  async buildUserActivationEmail(
    email: string,
    firstName: string,
    lastName: string,
    adminFirstName: string,
    adminLastName: string,
    adminEmail: string,
    accountName: string,
    activationToken: string,
  ): Promise<MailData> {
    const text = `
Dear ${firstName},

Welcome to ${accountName}! We are excited to have you join our team.

To help you get started, we have set up your employee profile on our HR platform Opus Remote. Please click the link below to access your profile and complete the necessary onboarding information:

${OpusConfig.USER_ACTIVATION_REDIRECT_URL}?token=${activationToken}

If you have any questions or need assistance, feel free to reach out to me at ${adminEmail}.

We look forward to working with you!

Best regards,
${adminFirstName} ${adminLastName}
HR Manager
${accountName}`;

    const html = `<p>Dear <strong>${firstName}</strong>,</p>
    <p>Welcome to ${accountName}! We are excited to have you join our team.</p>
    <p>To help you get started, we have set up your employee profile on our HR platform Opus Remote. Please click the link below to access your profile and complete the necessary onboarding information:</p>
    <p><a href="${OpusConfig.USER_ACTIVATION_REDIRECT_URL}?token=${activationToken}">Activation Link</a></p>
    <p>If you have any questions or need assistance, feel free to reach out to me at <a href="mailto:${adminEmail}">${adminEmail}</a>.We look forward to working with you!</p>
    <p>Best regards,</p>
    <p><strong>${adminFirstName} ${adminLastName}<br /></strong>HR Manager<br />${accountName}</p>`;

    return {
      subject: `Welcome to ${accountName}! Access your Opus Remote Employee Profile`,
      to: email,
      html,
      text,
    };
  }

  async buildRequestLeaveEmail(
    email: string,
    companyName: string,
    firstName: string,
    requestTypeName: string,
  ): Promise<MailData> {
    const emailBody = `
Hello ${firstName},

Your ${requestTypeName} request has been successfully submitted. You will receive a notification once the request has been reviewed and a decision has been made.

Thank you.

Best regards,
${companyName}
        `;
    return {
      to: email,
      subject: 'Leave Request Submitted',
      text: emailBody,
    };
  }

  async buildRequestLeaveAdminNotifEmail(
    adminEmail: string,
    adminName: string,
    requesterName: string,
    requestTypeName: string,
  ): Promise<MailData> {
    const emailBody = `
Hello ${adminName},

A ${requestTypeName} request has been filed by ${requesterName}. Manage this request at: ${OpusConfig.WEB_URL}/leave-management

This is an autogenerated email from Opus Remote.
`;

    const html = `
<p>Hello ${adminName},</p>
<p>A ${requestTypeName} request has been filed by ${requesterName}. Manage this request at: <a href="${OpusConfig.WEB_URL}/leave-management" target="_blank">${OpusConfig.WEB_URL}/leave-management</a></p>
<p>This is an autogenerated email from Opus Remote.</p>
`;
    return {
      to: adminEmail,
      subject: 'Leave Request Submitted',
      text: emailBody,
      html,
    };
  }

  async buildLeaveProcessedNotifEmail(
    approverName: string,
    nextApproverEmail: string,
    nextApproverName: string,
    requesterName: string,
    requestTypeName: string,
    approveString: 'APPROVED' | 'REJECTED',
  ): Promise<MailData> {
    const emailBody = `
Hello ${nextApproverName},

The ${requestTypeName} request by ${requesterName} has been ${approveString} by ${approverName}. Manage this request at: ${OpusConfig.WEB_URL}/leave-management

This is an autogenerated email from Opus Remote.
`;

    const html = `
<p>Hello ${nextApproverName},</p>
<p>The ${requestTypeName} request by ${requesterName} has been ${approveString} by ${approverName}. Manage this request at: <a href="${OpusConfig.WEB_URL}/leave-management" target="_blank">${OpusConfig.WEB_URL}/leave-management</a></p>
<p>This is an autogenerated email from Opus Remote.</p>
`;
    return {
      to: nextApproverEmail,
      subject: 'Leave Request Approval',
      text: emailBody,
      html,
    };
  }

  async buildFinalizedLeaveEmail(
    email: string,
    companyName: string,
    firstName: string,
    requestTypeName: string,
    approveString: 'APPROVED' | 'REJECTED',
  ) {
    const emailBody = `
Hello ${firstName},

Your ${requestTypeName} request has been ${approveString}.

Thank you.

Best regards,
${companyName}
    `;
    return {
      to: email,
      subject: `Leave Request ${approveString}`,
      text: emailBody,
    };
  }

  async buildAnnouncementEmail(
    resourceTitle: string,
    resourceContent: string,
    adminName: string,
    employeeName: string,
    employeeEmail: string,
    resourceType: ResourceTypeType,
    attachments: FileDto[],
  ): Promise<MailData> {
    const emailBody = `
Hello ${employeeName},

A new ${resourceType.toLocaleLowerCase()} has been published by ${adminName}.

${resourceContent}
`;

    const html = `
<p>Hello <strong>${employeeName}</strong>,</p>
<p>A new ${resourceType.toLocaleLowerCase()} has been published by <strong>${adminName}</strong>:</p>
<i>${await this.__textToHtml(resourceContent)}</i>
`;
    return {
      to: employeeEmail,
      subject: `New ${resourceType} - ${resourceTitle}`,
      text: emailBody,
      html,
      attachments,
    };
  }

  async buildClockOutReminderEmail(
    email: string,
    firstName: string,
    lastClockIn: Date,
    timezone: string,
  ): Promise<MailData> {
    const formattedTime = format(
      toZonedTime(lastClockIn, timezone),
      'yyyy-MM-dd hh:mm a',
    );

    const emailBody = `
Hello ${firstName},

This is a reminder that you still haven't clocked out yet for today.
Your last clock-in time was: ${formattedTime}.

To clock out, please visit: ${OpusConfig.WEB_URL}
`;

    const html = `
<p>Hello <strong>${firstName}</strong>,</p>
<p>This is a reminder that you still haven't clocked out yet for today. Your last clock-in time was: <strong>${formattedTime}</strong>.</p>
<p>To clock out, please visit: <a href="${OpusConfig.WEB_URL}" target="_blank">${OpusConfig.WEB_URL}</a></p>
`;
    return {
      to: email,
      subject: `Reminder: Clock Out`,
      text: emailBody,
      html,
    };
  }

  private async __textToHtml(text: string) {
    // Escape special HTML characters
    const escapedText = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    // Split text into lines
    const lines = escapedText.split('\n');

    // Wrap each line in <p> tags
    const result = lines.map((line) => `<p>${line}</p>`).join('');

    return result;
  }
}
