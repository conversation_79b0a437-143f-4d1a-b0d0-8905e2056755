# Implementation Plan

- [ ] 1. Set up new modular project structure and core infrastructure

  - Create the new directory structure under src/modules, src/shared, src/core, and src/common
  - Move and refactor the main application module to follow new structure
  - Update TypeScript path mappings in tsconfig.json to support new module structure
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [ ] 2. Create shared infrastructure and utilities

  - [ ] 2.1 Create base repository abstract class and interfaces

    - Implement BaseRepository abstract class with common CRUD operations
    - Create repository interfaces for type safety and dependency injection
    - Write unit tests for base repository functionality
    - _Requirements: 4.3, 2.3_

  - [ ] 2.2 Create shared DTOs and validation utilities

    - Move common DTOs to src/shared/dto directory
    - Implement base DTO classes with common validation patterns
    - Create validation decorators and pipes for consistent validation
    - _Requirements: 4.4, 2.5_

  - [ ] 2.3 Set up core database and configuration modules
    - Refactor Prisma configuration into src/core/database module
    - Create configuration module in src/core/config for environment management
    - Implement database connection and transaction utilities
    - _Requirements: 3.5, 2.3_

- [ ] 3. Create authentication module following new architecture

  - [ ] 3.1 Implement auth module structure

    - Create src/modules/auth directory with controllers, services, dto, and guards subdirectories
    - Move existing auth logic into new modular structure
    - Implement AuthService with proper dependency injection
    - _Requirements: 1.1, 1.2, 4.1, 4.2_

  - [ ] 3.2 Create auth controllers and DTOs

    - Implement AuthController with login, register, and token refresh endpoints
    - Create auth-specific DTOs (LoginDto, RegisterDto, TokenResponseDto)
    - Add proper validation and error handling
    - _Requirements: 2.3, 4.4, 6.1_

  - [ ] 3.3 Implement auth guards and decorators
    - Create JwtAuthGuard and RolesGuard following NestJS patterns
    - Implement custom decorators for authentication and authorization
    - Write unit tests for guards and decorators
    - _Requirements: 2.3, 2.4, 7.4_

- [ ] 4. Refactor users module to new architecture

  - [ ] 4.1 Create users module structure

    - Create src/modules/users directory with proper subdirectories
    - Implement UsersService with business logic separated from controllers
    - Create UsersRepository extending BaseRepository
    - _Requirements: 1.1, 1.2, 4.1, 4.2, 4.3_

  - [ ] 4.2 Implement users controllers and DTOs

    - Refactor existing users controllers to follow new patterns
    - Create user-specific DTOs (CreateUserDto, UpdateUserDto, UserResponseDto)
    - Implement proper validation and error handling
    - _Requirements: 2.3, 4.4, 6.1_

  - [ ] 4.3 Create user entities and relationships
    - Define User entity class with proper typing
    - Implement relationships with Profile, Department, and other entities
    - Create entity validation and transformation logic
    - _Requirements: 4.5, 2.3_

- [ ] 5. Refactor attendance module to new architecture

  - [ ] 5.1 Create attendance module structure

    - Create src/modules/attendance directory following module pattern
    - Implement AttendanceService with business logic
    - Create AttendanceRepository with proper data access methods
    - _Requirements: 1.1, 1.2, 4.1, 4.2, 4.3_

  - [ ] 5.2 Implement attendance controllers and DTOs

    - Refactor attendance controllers to new structure
    - Create attendance DTOs (CreateAttendanceDto, AttendanceResponseDto)
    - Implement file upload handling for attendance pictures
    - _Requirements: 2.3, 4.4, 6.1_

  - [ ] 5.3 Handle attendance-specific business logic
    - Implement attendance validation rules
    - Create attendance reporting and analytics services
    - Add proper error handling for attendance operations
    - _Requirements: 4.1, 5.2, 6.1_

- [ ] 6. Refactor leave management module to new architecture

  - [ ] 6.1 Create leaves module structure

    - Create src/modules/leaves directory with proper organization
    - Implement LeavesService with leave approval workflow logic
    - Create LeavesRepository with complex query methods
    - _Requirements: 1.1, 1.2, 4.1, 4.2, 4.3_

  - [ ] 6.2 Implement leave controllers and DTOs

    - Refactor leave-related controllers to new structure
    - Create leave DTOs (CreateLeaveDto, LeaveApprovalDto, LeaveResponseDto)
    - Implement leave approval workflow endpoints
    - _Requirements: 2.3, 4.4, 6.1_

  - [ ] 6.3 Handle leave approval hierarchy and policies
    - Implement leave approval hierarchy logic in service layer
    - Create leave accrual policy management
    - Add leave balance calculation and validation
    - _Requirements: 4.1, 5.2, 6.1_

- [ ] 7. Refactor departments module to new architecture

  - [ ] 7.1 Create departments module structure

    - Create src/modules/departments directory following patterns
    - Implement DepartmentsService with department management logic
    - Create DepartmentsRepository with hierarchy queries
    - _Requirements: 1.1, 1.2, 4.1, 4.2, 4.3_

  - [ ] 7.2 Implement departments controllers and DTOs
    - Create DepartmentsController with CRUD operations
    - Implement department DTOs (CreateDepartmentDto, DepartmentResponseDto)
    - Add department hierarchy and user assignment endpoints
    - _Requirements: 2.3, 4.4, 6.1_

- [ ] 8. Refactor resources module to new architecture

  - [ ] 8.1 Create resources module structure

    - Create src/modules/resources directory with proper organization
    - Implement ResourcesService with content management logic
    - Create ResourcesRepository with file attachment handling
    - _Requirements: 1.1, 1.2, 4.1, 4.2, 4.3_

  - [ ] 8.2 Implement resources controllers and DTOs
    - Create ResourcesController with content management endpoints
    - Implement resource DTOs (CreateResourceDto, ResourceResponseDto)
    - Add file upload and attachment management
    - _Requirements: 2.3, 4.4, 6.1_

- [ ] 9. Refactor accounts module to new architecture

  - [ ] 9.1 Create accounts module structure

    - Create src/modules/accounts directory following module patterns
    - Implement AccountsService with multi-tenant logic
    - Create AccountsRepository with account-scoped queries
    - _Requirements: 1.1, 1.2, 4.1, 4.2, 4.3_

  - [ ] 9.2 Implement accounts controllers and DTOs
    - Create AccountsController with account management endpoints
    - Implement account DTOs (CreateAccountDto, AccountResponseDto)
    - Add account configuration and settings management
    - _Requirements: 2.3, 4.4, 6.1_

- [ ] 10. Create shared services and cross-cutting concerns

  - [ ] 10.1 Implement shared services module

    - Create MailerService, FileService, and NotificationService in shared module
    - Implement proper service interfaces and dependency injection
    - Add configuration management for external services
    - _Requirements: 7.2, 7.3, 2.3_

  - [ ] 10.2 Create global interceptors and filters
    - Implement global exception filter with proper error formatting
    - Create logging interceptor for request/response tracking
    - Add validation pipe with custom error messages
    - _Requirements: 2.6, 7.4, 6.1_

- [ ] 11. Update module imports and dependencies

  - [ ] 11.1 Configure module imports and exports

    - Update all modules to properly import and export required services
    - Configure forRoot and forFeature patterns where appropriate
    - Ensure proper dependency injection throughout the application
    - _Requirements: 1.4, 7.1, 7.2_

  - [ ] 11.2 Update main application module
    - Refactor AppModule to import all feature modules
    - Configure global providers, interceptors, and filters
    - Set up proper module initialization order
    - _Requirements: 1.4, 7.1, 2.1_

- [ ] 12. Update tests to match new architecture

  - [ ] 12.1 Create unit tests for all services and repositories

    - Write comprehensive unit tests for each service class
    - Create repository tests with proper mocking
    - Implement test utilities and factories for consistent testing
    - _Requirements: 5.1, 5.2, 5.3, 5.4_

  - [ ] 12.2 Create integration tests for modules
    - Write integration tests for each feature module
    - Test module interactions and dependency injection
    - Create end-to-end tests for critical user flows
    - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 13. Clean up old architecture files

  - [ ] 13.1 Remove old clean architecture directories

    - Delete src/domains, src/use-cases, and src/infrastructure directories
    - Remove old abstract classes and interfaces that are no longer needed
    - Update any remaining imports to point to new module locations
    - _Requirements: 5.1, 5.2, 5.3_

  - [ ] 13.2 Update configuration and build files
    - Update Jest configuration to match new directory structure
    - Modify TypeScript path mappings to remove old paths
    - Update any build scripts or deployment configurations
    - _Requirements: 5.1, 5.2, 5.3_

- [ ] 14. Performance optimization and final testing

  - [ ] 14.1 Optimize imports and dependency injection

    - Review and optimize module imports to prevent circular dependencies
    - Ensure efficient dependency injection patterns
    - Add lazy loading where appropriate for better performance
    - _Requirements: 6.2, 6.3, 6.4_

  - [ ] 14.2 Comprehensive testing and validation
    - Run full test suite to ensure all functionality works
    - Perform load testing to validate performance improvements
    - Verify all API endpoints work correctly with new architecture
    - _Requirements: 5.1, 5.2, 5.3, 5.4, 6.2_
