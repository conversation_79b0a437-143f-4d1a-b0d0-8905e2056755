import { FilesManager } from '@/core/abstracts/files-manager';
import { OpusConfig } from '@/infra/framework/opus-config';
import { Storage } from '@google-cloud/storage';
import { Module } from '@nestjs/common';
import { GoogleFilesManagerService } from './google-files-manager.service';
import { LocalFilesManagerService } from './local-files-manager.service';

@Module({
  providers: [
    {
      provide: FilesManager,
      useFactory: () => {
        switch (OpusConfig.FILES_MANAGER) {
          case 'google':
            const storage = new Storage();
            return new GoogleFilesManagerService(
              storage.bucket(OpusConfig.GOOGLE_BUCKET_NAME),
            );
          case 'local':
          default:
            return new LocalFilesManagerService();
        }
      },
    },
  ],
  exports: [FilesManager],
})
export class FilesManagerModule {}
