import { Gender } from '@/core/enums/gender.enum';
import { MaritalStatus } from '@/core/enums/marital-status.enum';
import { z } from 'zod';
import { createdDepartment } from '../department/created-department.dto';
import { IdType } from '../base/base.dto';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { fileMeta } from '../files/file-meta.dto';

export const createdProfile = z.object({
  id: IdType,
  firstName: z.string(),
  lastName: z.string(),
  birthDate: z.string().date().nullable(),
  maritalStatus: z.nativeEnum(MaritalStatus).nullable(),
  gender: z.nativeEnum(Gender).nullable(),

  addressStreet1: z.string().nullable(),
  addressStreet2: z.string().nullable(),
  city: z.string().nullable(),
  province: z.string().nullable(),
  postalCode: z.string().nullable(),
  country: z.string().nullable(),

  // Contact Properties
  workPhone: z.string().nullable(),
  mobilePhone: z.string().nullable(),
  homePhone: z.string().nullable(),
  workEmail: z.string().email(),
  personalEmail: z.string().email().nullable(),

  // Emergency Contact Properties
  emergencyName: z.string().nullable(),
  emergencyRelationship: z.string().nullable(),
  emergencyMobilePhone: z.string().nullable(),

  // Work Properties
  jobTitle: z.string().nullable(),
  employmentStatus: z.string().nullable(),

  // Profile Picure
  profilePicture: fileMeta.nullable(),
  contract: fileMeta.nullish(),

  activatedAt: z.string().datetime().nullable(),

  createdAt: z.string().date(),
  department: createdDepartment.nullable(),
});

export class CreatedProfileDto extends createZodDto(createdProfile) {}
