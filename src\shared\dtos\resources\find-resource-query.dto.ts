import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { paginationMeta } from '../pagination/page-meta.dto';
import { findResourceFilter } from './find-resource-filter.dto';

export const findResourceQuery = z
  .object({
    filter: findResourceFilter,
    paging: paginationMeta,
  })
  .partial();

export class FindResourceQueryDto extends createZodDto(findResourceQuery) {}
