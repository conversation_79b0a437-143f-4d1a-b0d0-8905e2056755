import { SocialDataDto } from '@/shared/dtos/auth/social-data.dto';
import { InvalidCredentialException } from '../exceptions/opus-exceptions';

export abstract class SocialOauth2 {
  /**
   * Verifies the token provided by the social provider and gets the email of the user
   *
   * @param token The token from the social provider
   * @returns The user data from the social provider
   *
   * @throws A {@link InvalidCredentialException} when the credentials cannot be verified
   */
  abstract verifyLogin(token: string): Promise<SocialDataDto>;
}
