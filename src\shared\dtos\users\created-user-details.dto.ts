import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { createdAccount } from '../accounts/created-account.dto';
import { IdType } from '../base/base.dto';
import { fileMeta } from '../files/file-meta.dto';
import { createdRole } from '../roles/created-role.dto';
import { createdUserStatus } from '../user-statuses/created-user-status.dto';
import { createdUser } from './created-user.dto';

export const createdUserDetails = createdUser
  .omit({
    roleId: true,
    profilePictureId: true,
  })
  .extend({
    profilePicture: fileMeta.omit({ uploaderId: true }).nullable(),
    role: createdRole,
    account: createdAccount.nullable(),
    profile: z.object({
      employmentStatus: z.string().nullable(),
      jobTitle: z.string().nullable(),
      departmentId: IdType.nullable(),
      managedDepartmentId: IdType.nullable(),
    }),
    userStatus: createdUserStatus.nullable(),
    hasPassword: z.boolean(),
    isLeaveApprover: z.boolean(),
  });
export class CreatedUserDetailsDto extends createZodDto(createdUserDetails) {}
