import '@wahyubucil/nestjs-zod-openapi/boot';

import { Logger } from '@nestjs/common';
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { SwaggerModule } from '@nestjs/swagger';
import * as Sentry from '@sentry/node';
import { patchNestjsSwagger } from '@wahyubucil/nestjs-zod-openapi';
import { AppModule } from './app.module';
import { SentryFilter } from './monitoring/sentry/exception-filter';
import { initializeSentry } from './monitoring/sentry/instrument';
import { OpusConfig } from './opus-config';
import swaggerConfig from './swagger';
import { createSchedule } from './scheduler/scheduler';

async function bootstrap() {
  if (OpusConfig.NODE_ENV !== 'development') {
    void createSchedule();
  }

  const app = await NestFactory.create(AppModule, {
    logger: OpusConfig.LOG_LEVEL,
  });
  const { httpAdapter } = app.get(HttpAdapterHost);

  // Initialize Sentry
  await initializeSentry(OpusConfig.SENTRY_DSN, OpusConfig.NODE_ENV);
  Sentry.setupNestErrorHandler(app, new SentryFilter(httpAdapter));

  // Setup Openapi docs
  patchNestjsSwagger();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('/docs', app, document, {
    jsonDocumentUrl: '/docs/doc.json',
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  app.enableCors();
  app.enableShutdownHooks();

  const logger = new Logger();
  // Use PORT environment variable provided by Cloud Run, or fall back to GATEWAY_PORT
  const port = process.env.PORT || OpusConfig.GATEWAY_PORT;

  await app.listen(port, '0.0.0.0').then(() => {
    logger.log(`Listening on port ${port}`, 'Bootstrap');
    logger.log(`Environment: ${OpusConfig.NODE_ENV}`, 'Bootstrap');
  });
}

// eslint-disable-next-line @typescript-eslint/no-floating-promises
bootstrap();
