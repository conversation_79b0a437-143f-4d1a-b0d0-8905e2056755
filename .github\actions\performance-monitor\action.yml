name: 'Pipeline Performance Monitor'
description: 'Track and report pipeline performance metrics'
inputs:
  stage:
    description: 'Pipeline stage name'
    required: true
  start-time:
    description: 'Stage start time (Unix timestamp)'
    required: true
  status:
    description: 'Stage status (success/failure)'
    required: true
  metadata:
    description: 'Additional metadata as JSON'
    required: false
    default: '{}'
  slack-webhook:
    description: 'Slack webhook URL for notifications'
    required: false
  thread-ts:
    description: 'Slack thread timestamp for threaded updates'
    required: false

outputs:
  duration:
    description: 'Stage duration in seconds'
    value: ${{ steps.calculate.outputs.duration }}
  duration-formatted:
    description: 'Stage duration formatted as human readable'
    value: ${{ steps.calculate.outputs.duration-formatted }}
  performance-data:
    description: 'Performance data as JSON'
    value: ${{ steps.calculate.outputs.performance-data }}

runs:
  using: 'composite'
  steps:
    - name: Calculate performance metrics
      id: calculate
      shell: bash
      run: |
        START_TIME=${{ inputs.start-time }}
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))

        # Format duration
        if [ $DURATION -ge 3600 ]; then
          HOURS=$((DURATION / 3600))
          MINUTES=$(((DURATION % 3600) / 60))
          SECONDS=$((DURATION % 60))
          FORMATTED="${HOURS}h ${MINUTES}m ${SECONDS}s"
        elif [ $DURATION -ge 60 ]; then
          MINUTES=$((DURATION / 60))
          SECONDS=$((DURATION % 60))
          FORMATTED="${MINUTES}m ${SECONDS}s"
        else
          FORMATTED="${DURATION}s"
        fi

        echo "duration=$DURATION" >> $GITHUB_OUTPUT
        echo "duration-formatted=$FORMATTED" >> $GITHUB_OUTPUT

        # Create performance data JSON
        METADATA='${{ inputs.metadata }}'
        PERFORMANCE_DATA=$(cat << EOF
        {
          "stage": "${{ inputs.stage }}",
          "status": "${{ inputs.status }}",
          "duration": $DURATION,
          "durationFormatted": "$FORMATTED",
          "startTime": $START_TIME,
          "endTime": $END_TIME,
          "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
          "metadata": $METADATA
        }
        EOF
        )

        echo "performance-data<<EOF" >> $GITHUB_OUTPUT
        echo "$PERFORMANCE_DATA" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        echo "📊 Performance Metrics for ${{ inputs.stage }}:"
        echo "  Duration: $FORMATTED ($DURATION seconds)"
        echo "  Status: ${{ inputs.status }}"
        echo "  Start: $(date -d @$START_TIME -u +"%Y-%m-%d %H:%M:%S UTC")"
        echo "  End: $(date -d @$END_TIME -u +"%Y-%m-%d %H:%M:%S UTC")"

    - name: Store performance metrics
      shell: bash
      run: |
        # Create performance metrics file
        mkdir -p .github/performance-metrics
        METRICS_FILE=".github/performance-metrics/${{ inputs.stage }}-$(date +%Y%m%d-%H%M%S).json"

        echo '${{ steps.calculate.outputs.performance-data }}' > "$METRICS_FILE"
        echo "📁 Performance metrics stored in: $METRICS_FILE"

    - name: Update Slack with performance metrics
      if: inputs.slack-webhook != '' && inputs.thread-ts != ''
      shell: bash
      run: |
        # Determine status emoji and color
        if [ "${{ inputs.status }}" = "success" ]; then
          STATUS_EMOJI="✅"
          COLOR="good"
        else
          STATUS_EMOJI="❌"
          COLOR="danger"
        fi

        # Create Slack message
        SLACK_MESSAGE=$(cat << EOF
        {
          "thread_ts": "${{ inputs.thread-ts }}",
          "attachments": [
            {
              "color": "$COLOR",
              "fields": [
                {
                  "title": "📊 ${{ inputs.stage }} Performance",
                  "value": "$STATUS_EMOJI Duration: ${{ steps.calculate.outputs.duration-formatted }}",
                  "short": true
                }
              ],
              "footer": "Pipeline Performance Monitor",
              "ts": $(date +%s)
            }
          ]
        }
        EOF
        )

        # Send to Slack
        curl -X POST -H 'Content-type: application/json' \
          --data "$SLACK_MESSAGE" \
          "${{ inputs.slack-webhook }}" || echo "Failed to send Slack notification"

    - name: Generate performance report
      shell: bash
      run: |
        # Create a performance summary
        cat << EOF > performance-summary-${{ inputs.stage }}.md
        # Performance Report: ${{ inputs.stage }}

        ## Metrics
        - **Duration**: ${{ steps.calculate.outputs.duration-formatted }}
        - **Status**: ${{ inputs.status }}
        - **Start Time**: $(date -d @${{ inputs.start-time }} -u +"%Y-%m-%d %H:%M:%S UTC")
        - **End Time**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")

        ## Performance Analysis
        $(if [ ${{ steps.calculate.outputs.duration }} -lt 60 ]; then
          echo "🚀 **Excellent** - Stage completed in under 1 minute"
        elif [ ${{ steps.calculate.outputs.duration }} -lt 300 ]; then
          echo "✅ **Good** - Stage completed in under 5 minutes"
        elif [ ${{ steps.calculate.outputs.duration }} -lt 600 ]; then
          echo "⚠️ **Moderate** - Stage took 5-10 minutes, consider optimization"
        else
          echo "🐌 **Slow** - Stage took over 10 minutes, optimization recommended"
        fi)

        ## Metadata
        \`\`\`json
        ${{ inputs.metadata }}
        \`\`\`
        EOF

        echo "📄 Performance report generated: performance-summary-${{ inputs.stage }}.md"
