import { CalendarEventsListDto } from '@/shared/dtos/calendars/calendar-data.dto';
import { ListCalendarEventsInputDto } from '@/shared/dtos/calendars/list-calendar-events-input.dto';
import { CreatedUserFullDto } from '@/shared/dtos/users/created-user-full.dto';
import { Controller, Get, Query } from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger/dist/decorators';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { CalendarsService } from './calendars.service';

@Controller('calendars')
@ApiTags('calendars')
@ApiDefaultErrorMessage()
@SwaggerAuth()
export class CalendarsController {
  constructor(private readonly calendarsService: CalendarsService) {}

  @Get('events')
  @ApiOperation({
    summary: 'Returns a list of calendar events',
  })
  @ApiOkResponse({ type: CalendarEventsListDto })
  async getCalendarEventsList(
    @CurrentUser() user: CreatedUserFullDto,
    @Query() filter: ListCalendarEventsInputDto,
  ) {
    return this.calendarsService.getFullCalendar(user, filter);
  }
}
