import { Entity } from '@/core/base/entity';
import { ResourceTypeType } from '@/core/enums/resource-type.enum';
import { FileEntity } from './file.entity';

export class ResourceEntity extends Entity {
  id!: string;
  resourceType!: ResourceTypeType;
  title!: string;
  content!: string;
  accountId!: string;
  uploaderId!: string | null;
  attachments!: FileEntity[];
  publishDate!: Date | null;
}
