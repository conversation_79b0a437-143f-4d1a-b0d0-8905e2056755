import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdUserStatus } from './created-user-status.dto';

export const updateUserStatusInput = createdUserStatus
  .extend({
    status: createdUserStatus.shape.status.nullish(),
    emoji: createdUserStatus.shape.emoji.nullish(),
    until: createdUserStatus.shape.until.nullish(),
  })
  .partial();

export class UpdateUserStatusInputDto extends createZodDto(
  updateUserStatusInput,
) {}
