import { CreatedUserStatusDto } from '@/shared/dtos/user-statuses/created-user-status.dto';
import { UpdateUserStatusNoIdInputDto } from '@/shared/dtos/user-statuses/update-user-status-no-id-input.dto';
import { FindOneUserStatusUseCase } from '@/use-cases/user-status/find-one-user-status.use-case';
import { UpdateUserStatusUseCase } from '@/use-cases/user-status/update-user-status.use-case';
import { Body, Controller, Get, Param, Put } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { UserIdMatch } from '../auth/decorators/requester-match.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';

@ApiTags('users-status')
@Controller('users')
@SwaggerAuth()
@ApiDefaultErrorMessage()
export class UsersStatusController {
  constructor(
    private updateUserStatusUseCase: UpdateUserStatusUseCase,
    private findOneUserStatusUseCase: FindOneUserStatusUseCase,
  ) {}

  @Put('/:userId/status')
  @UserIdMatch({ userIdLocation: 'params', userIdParamName: 'userId' })
  @ApiOperation({
    summary: 'Update the status of a user',
  })
  @ApiOkResponse({
    type: CreatedUserStatusDto,
  })
  async updateUserStatus(
    @Param('userId') userId: string,
    @Body() data: UpdateUserStatusNoIdInputDto,
  ) {
    return this.updateUserStatusUseCase.execute(userId, data);
  }

  @Get('/:userId/status')
  @ApiOperation({
    summary: 'Get the status of a user',
  })
  @ApiOkResponse({
    type: CreatedUserStatusDto,
  })
  async getUserStatus(@Param('userId') userId: string) {
    return this.findOneUserStatusUseCase.execute({ id: userId });
  }
}
