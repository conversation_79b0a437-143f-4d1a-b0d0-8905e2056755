import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { ProfileEntity } from '@/core/domain/entities/profile.entity';
import { EntityConflictException } from '@/core/exceptions/opus-exceptions';
import { ProfilesRepository } from '@/core/repositories/profiles.repository';
import { ExtendedTransactionalAdapterPrisma } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { FindProfileFilterDto } from '@/shared/dtos/profiles/find-profile-filter.dto';
import { FindProfileSortDto } from '@/shared/dtos/profiles/find-profile-sort.dto';
import { objectEntries } from '@/shared/helpers/entries';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaProfilesRepository implements ProfilesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  create(): Promise<ProfileEntity> {
    throw new Error('Method not implemented.');
  }

  async findOne(
    filter: FindProfileFilterDto,
    include = undefined,
    sort: FindProfileSortDto = {},
  ): Promise<ProfileEntity | null> {
    return this.txHost.tx.profile.findFirst({
      where: this.processFilter(filter),
      include: this.processInclude(include),
      orderBy: this.processSort(sort),
    });
  }

  @Transactional()
  async findAll(
    filter: FindProfileFilterDto,
    paginationMeta?: PaginationMetaDto,
    include = undefined,
    sort: FindProfileSortDto = {},
  ): Promise<EntityCount<ProfileEntity>> {
    const data = await this.txHost.tx.profile.findMany({
      where: this.processFilter(filter),
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      include: this.processInclude(include),
      orderBy: this.processSort(sort),
    });
    const count = await this.txHost.tx.profile.count({
      where: this.processFilter(filter),
    });
    return { data, count };
  }

  async update(
    id: string,
    data: Partial<ProfileEntity>,
  ): Promise<ProfileEntity> {
    try {
      return await this.txHost.tx.profile.update({
        where: { id },
        data: this.processUpdate(data),
        include: this.processInclude(undefined),
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            if (
              Array.isArray(error.meta?.target) &&
              error.meta.target.includes('email')
            ) {
              throw new EntityConflictException('Work email is already in use');
            }
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2025':
            const c = error.meta?.cause;
            if (typeof c != 'string') {
              throw new EntityConflictException(
                'One of the input ids does not exist',
              );
            }
            const cause = c.toLowerCase();

            if (cause.includes('department')) {
              throw new EntityConflictException('Department does not exist');
            } else if (cause.includes('user')) {
              throw new EntityConflictException('Profile does not exist');
            }

            throw new EntityConflictException(
              'One of the input ids does not exist',
            );
        }
      }
      throw error;
    }
  }

  async remove(): Promise<void> {
    throw new Error('Method not implemented.');
  }

  private processFilter(
    filter: FindProfileFilterDto,
  ): Prisma.ProfileWhereInput {
    const { nameOrEmail, accountId, isActivated, ...rest } = filter;

    let userFilter: Prisma.ProfileWhereInput['user'] = undefined;

    if (isActivated) {
      userFilter = {
        activatedAt: { not: null },
      };
    } else if (isActivated === false) {
      userFilter = {
        activatedAt: null,
      };
    }

    if (!nameOrEmail) {
      return {
        ...rest,
        user: userFilter,
      };
    }

    return {
      ...rest,
      user: userFilter,
      OR: [
        { personalEmail: { contains: nameOrEmail, mode: 'insensitive' } },
        {
          user: {
            OR: [
              { firstName: { contains: nameOrEmail, mode: 'insensitive' } },
              { lastName: { contains: nameOrEmail, mode: 'insensitive' } },
              { email: { contains: nameOrEmail, mode: 'insensitive' } },
            ],
          },
        },
      ],
    };
  }

  private processSort(
    sort: FindProfileSortDto,
  ): Prisma.ProfileOrderByWithRelationInput[] {
    return objectEntries(sort).map(([k, v]) => {
      if (k == 'firstName' || k == 'lastName') {
        return { user: { [k]: v } };
      }
      return {
        [k]: v,
      };
    });
  }

  private processInclude(include?: PickRelation<ProfileEntity>) {
    include;
    return {
      department: {
        include: {
          headUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              activatedAt: true,
            },
          },
        },
      },
      contract: true,
      user: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
          profilePicture: true,
          activatedAt: true,
        },
      },
    };
  }

  private processUpdate(
    data: Partial<ProfileEntity>,
  ): Prisma.ProfileUpdateArgs['data'] {
    const {
      id,
      user,
      department: _department,
      contract: _contract,
      contractId,
      departmentId,
      ...rest
    } = data;

    const modified: Prisma.ProfileUpdateArgs['data'] = { ...rest };

    if (departmentId) {
      modified.department = { connect: { id: departmentId } };
    }
    if (contractId) {
      modified.contract = { connect: { id: contractId } };
    }

    if (user)
      modified.user = {
        update: {
          firstName: user?.firstName,
          lastName: user?.lastName,
          email: user?.email,
          profilePictureId: user.profilePicture?.id,
        },
      };

    return modified;
  }
}
