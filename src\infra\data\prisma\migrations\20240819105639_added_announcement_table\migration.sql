-- AlterTable
ALTER TABLE "files" ADD COLUMN     "announcement_id " TEXT,
ADD COLUMN     "file_size " INTEGER NOT NULL DEFAULT 10;

-- CreateTable
CREATE TABLE "announcements" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "account_id" TEXT NOT NULL,
    "uploader_id" TEXT NOT NULL,

    CONSTRAINT "announcements_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "announcements" ADD CONSTRAINT "announcements_uploader_id_fkey" FOREIGN KEY ("uploader_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcements" ADD CONSTRAINT "announcements_account_id_fkey" FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "files" ADD CONSTRAINT "files_announcement_id _fkey" FOREIGN KEY ("announcement_id ") REFERENCES "announcements"("id") ON DELETE SET NULL ON UPDATE CASCADE;
