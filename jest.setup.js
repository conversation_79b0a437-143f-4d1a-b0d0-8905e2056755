// Jest setup file for Opus Remote Backend
// This file is executed before running tests to configure the testing environment

// Import zod-openapi boot to extend zod with openapi methods
require('@wahyubucil/nestjs-zod-openapi/boot');

// Set test timeout to 30 seconds (default is 5 seconds)
const isIntegrationTest = process.argv.includes(
  '--testPathPattern=.*\\.integration\\.spec\\.ts$',
);
jest.setTimeout(isIntegrationTest ? 30000 : 10000);

// Mock console methods to reduce noise in test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

// Suppress specific console outputs during tests
console.error = (...args) => {
  // Allow important error messages but suppress noise
  const message = args[0];
  if (typeof message === 'string') {
    // Suppress Prisma and other known warnings
    if (
      message.includes('Prisma') ||
      message.includes('deprecated') ||
      message.includes('ExperimentalWarning')
    ) {
      return;
    }
  }
  originalConsoleError.apply(console, args);
};

console.warn = (...args) => {
  const message = args[0];
  if (typeof message === 'string') {
    // Suppress common warnings
    if (
      message.includes('deprecated') ||
      message.includes('ExperimentalWarning') ||
      message.includes('Prisma')
    ) {
      return;
    }
  }
  originalConsoleWarn.apply(console, args);
};

// Set up global test environment variables
process.env.NODE_ENV = 'development';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_EXPIRES_IN = '1h';
process.env.BCRYPT_ROUNDS = '4'; // Lower rounds for faster tests

// Required environment variables for OpusConfig validation
process.env.GATEWAY_PORT = '3000';
process.env.SENTRY_DSN = 'https://<EMAIL>/test';
process.env.USER_ACTIVATION_REDIRECT_URL = 'http://localhost:4000/activate';
process.env.GOOGLE_PROJECT_ID = 'test-project';
process.env.GOOGLE_CLIENT_ID = 'test-client-id';
process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
process.env.FILES_MANAGER = 'local';
process.env.GOOGLE_BUCKET_NAME = 'test-bucket';
process.env.PASSWORD_SALT_ROUNDS = '4';
process.env.SENDGRID_API_KEY = 'test-sendgrid-key';
process.env.FILES_BASE_URL = 'http://localhost:3000/files';
process.env.API_URL = 'http://localhost:3000';
process.env.WEB_URL = 'http://localhost:8000';
process.env.GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT =
  '<EMAIL>';

// Mock external services for testing
jest.mock('@sentry/nestjs', () => ({
  SentryModule: {
    forRoot: jest.fn(() => ({
      module: class MockSentryModule {},
      providers: [],
      exports: [],
    })),
  },
  SentryService: jest.fn(() => ({
    captureException: jest.fn(),
    captureMessage: jest.fn(),
  })),
}));

// Mock Slack webhook
jest.mock('@slack/webhook', () => ({
  IncomingWebhook: jest.fn(() => ({
    send: jest.fn().mockResolvedValue({ text: 'ok' }),
  })),
}));

// Mock SendGrid
jest.mock('@sendgrid/mail', () => ({
  setApiKey: jest.fn(),
  send: jest.fn().mockResolvedValue([{ statusCode: 202 }]),
}));

// Mock Google Cloud services
jest.mock('@google-cloud/storage', () => ({
  Storage: jest.fn(() => ({
    bucket: jest.fn(() => ({
      file: jest.fn(() => ({
        save: jest.fn().mockResolvedValue(),
        delete: jest.fn().mockResolvedValue(),
        getSignedUrl: jest.fn().mockResolvedValue(['http://mock-url']),
      })),
    })),
  })),
}));

// Mock Google Cloud Scheduler
jest.mock('@google-cloud/scheduler', () => ({
  CloudSchedulerClient: jest.fn(() => ({
    createJob: jest.fn().mockResolvedValue(),
    deleteJob: jest.fn().mockResolvedValue(),
    updateJob: jest.fn().mockResolvedValue(),
  })),
}));

// Global test utilities
global.testUtils = {
  // Helper to create mock request objects
  createMockRequest: (overrides = {}) => ({
    user: { id: 'test-user-id', email: '<EMAIL>' },
    headers: { authorization: 'Bearer test-token' },
    body: {},
    query: {},
    params: {},
    ...overrides,
  }),

  // Helper to create mock response objects
  createMockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis(),
    };
    return res;
  },

  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise((resolve) => setTimeout(resolve, ms)),
};

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Restore console methods after all tests
afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});
