import { Mapper } from '@/core/base/mapper';
import { CreatedTimeAwayTypeDto } from '@/shared/dtos/time-away-types/created-time-away-type.dto';
import { TimeAwayTypeEntity } from '../../entities/time-away-type.entity';

export class CreatedTimeAwayTypeMapper
  implements Mapper<TimeAwayTypeEntity, CreatedTimeAwayTypeDto>
{
  public map(data: TimeAwayTypeEntity): CreatedTimeAwayTypeDto {
    return { id: data.id, description: data.description };
  }
}
