import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { SocketsGateway } from './sockets.gateway';
import { SocketService } from './sockets.service';

@Module({
  imports: [AuthModule, PrismaModule],
  providers: [SocketService, SocketsGateway],
  exports: [SocketsGateway, SocketService],
})
export class SocketsModule {}
