import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { createdTimeAway } from './created-time-away.dto';

export const createTimeAwayAdjustmentInput = createdTimeAway
  .pick({
    hours: true,
    reason: true,
    userId: true,
    timeAwayTypeId: true,
  })
  .extend({
    hours: z.coerce.number().positive().or(z.number().negative()),
  });

export class CreateTimeAwayAdjustmentInputDto extends createZodDto(
  createTimeAwayAdjustmentInput,
) {}
