import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createResourceInput } from './create-resource-input.dto';
import { z } from 'zod';

export const updateResourceInput = createResourceInput
  .omit({ draft: true })
  .partial();

export class UpdateResourceInputDto extends createZodDto(updateResourceInput) {}

export const publishResourceInput = z.object({
  publish: z.boolean(),
});

export class PublishResourceInput extends createZodDto(publishResourceInput) {}
