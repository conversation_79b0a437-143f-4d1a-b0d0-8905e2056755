import { Mapper } from '@/core/base/mapper';
import { AttendanceEntity } from '@/core/domain/entities/attendance.entity';
import { UpsertAttendanceDto } from '@/shared/dtos/attendances/upsert-attendance.dto';

export class UpsertAttendanceMapper
  implements Mapper<UpsertAttendanceDto, AttendanceEntity>
{
  public map(data: UpsertAttendanceDto): AttendanceEntity {
    const attendance = new AttendanceEntity();

    attendance.userId = data.userId;

    if (data.id) {
      attendance.id = data.id;
    }
    if (data.in) {
      attendance.startLat = data.in.lat;
      attendance.startLong = data.in.long;
      attendance.startDatetime = new Date(data.in.datetime);
      attendance.startPictureId = data.in.pictureId;
    }
    if (data.out) {
      attendance.endLat = data.out.lat;
      attendance.endLong = data.out.long;
      attendance.endDatetime = new Date(data.out.datetime);
      attendance.endPictureId = data.out.pictureId;
    }

    return attendance;
  }
}
