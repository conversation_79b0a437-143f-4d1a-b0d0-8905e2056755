import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdTimeAway } from './created-time-away.dto';

export const updateTimeAway = createdTimeAway
  .omit({
    id: true,
    isApproved: true,
    createdAt: true,
    userId: true,
    approvalMessage: true,
    isUserRequest: true,
    isSubtract: true,
    checklist: true,
    approvalHistory: true,
  })
  .required();

export class UpdateTimeAwayDto extends createZodDto(updateTimeAway) {}
