import { Mapper } from '@/core/base/mapper';
import {
  createdResource,
  CreatedResourceDto,
} from '@/shared/dtos/resources/created-resource.dto';
import { ResourceEntity } from '../../entities/resources.entity';

export class CreatedResourceMapper extends Mapper<
  ResourceEntity,
  CreatedResourceDto
> {
  public map(data: ResourceEntity): CreatedResourceDto {
    return createdResource.parse({
      ...data,
      publishDate: data.publishDate?.toISOString() ?? null,
      createdAt: data.createdAt.toISOString(),
    });
  }
}
