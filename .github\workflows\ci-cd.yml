name: 'CI/CD Pipeline - with Single Repository Strategy'

on:
  push:
    #main disabled for safety
    branches: [staging, develop, 'hotfix/*']
  pull_request:
    branches: [staging]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
          - development

concurrency:
  group: '${{ github.workflow }}-${{ github.head_ref || github.ref }}-${{ inputs.environment || github.ref_name }}'
  cancel-in-progress: true

permissions:
  contents: 'read'
  id-token: 'write'
  deployments: 'write'

defaults:
  run:
    shell: 'bash'

env:
  IMAGE_NAME: opus-remote-backend # Single image name
  # Enable Docker BuildKit for faster builds
  DOCKER_BUILDKIT: 1
  BUILDKIT_PROGRESS: plain

jobs:
  # Determine deployment environment and settings
  setup:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.config.outputs.environment }}
      deploy: ${{ steps.config.outputs.deploy }}
      service_name: ${{ steps.config.outputs.service_name }}
      docker_tags: ${{ steps.config.outputs.docker_tags }}
      cpu_limit: ${{ steps.config.outputs.cpu_limit }}
      memory_limit: ${{ steps.config.outputs.memory_limit }}
      max_instances: ${{ steps.config.outputs.max_instances }}
      project_id: ${{ steps.config.outputs.project_id }}
      region: ${{ steps.config.outputs.region }}
      registry_url: ${{ steps.config.outputs.registry_url }}
    steps:
      - name: '🎯 Configure deployment settings'
        id: config
        run: |
          # Detect environment based on event and branch
          EVENT="${{ github.event_name }}"
          BRANCH="${{ github.ref_name }}"

          if [ "${EVENT}" = "workflow_dispatch" ]; then
            ENV="${{ inputs.environment }}"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "staging" ]; then
            ENV="staging"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "main" ]; then
            ENV="production"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "develop" ]; then
            ENV="development"
          elif [[ "${BRANCH}" == hotfix/* ]]; then
            ENV="hotfix"
          else
            ENV="${BRANCH}"
          fi

          case "$ENV" in
            "staging")
              echo "environment=staging" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-staging" >> $GITHUB_OUTPUT
              echo "docker_tags=staging,latest" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=10" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.STAGING_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.STAGING_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.STAGING_GCP_REGION }}-docker.pkg.dev/${{ vars.STAGING_GCP_PROJECT_ID }}/${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "production")
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-production" >> $GITHUB_OUTPUT
              echo "docker_tags=production,v$(date +%Y%m%d)-${{ github.run_number }}" >> $GITHUB_OUTPUT
              echo "cpu_limit=2000m" >> $GITHUB_OUTPUT
              echo "memory_limit=1Gi" >> $GITHUB_OUTPUT
              echo "max_instances=50" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.PRODUCTION_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.PRODUCTION_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.PRODUCTION_GCP_REGION }}-docker.pkg.dev/${{ vars.PRODUCTION_GCP_PROJECT_ID }}/${{ vars.PRODUCTION_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "development")
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-dev" >> $GITHUB_OUTPUT
              echo "docker_tags=develop" >> $GITHUB_OUTPUT
              echo "cpu_limit=500m" >> $GITHUB_OUTPUT
              echo "memory_limit=256Mi" >> $GITHUB_OUTPUT
              echo "max_instances=3" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.DEVELOPMENT_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.DEVELOPMENT_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.DEVELOPMENT_GCP_REGION }}-docker.pkg.dev/${{ vars.DEVELOPMENT_GCP_PROJECT_ID }}/${{ vars.DEVELOPMENT_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "hotfix")
              HOTFIX_NAME=$(echo "${{ github.ref_name }}" | sed 's/hotfix\///' | tr '/' '-')
              echo "environment=staging" >> $GITHUB_OUTPUT  # Deploy hotfixes to staging first
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-hotfix-${HOTFIX_NAME}" >> $GITHUB_OUTPUT
              echo "docker_tags=hotfix-${HOTFIX_NAME}" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=5" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.STAGING_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.STAGING_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.STAGING_GCP_REGION }}-docker.pkg.dev/${{ vars.STAGING_GCP_PROJECT_ID }}/${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "environment=none" >> $GITHUB_OUTPUT
              echo "deploy=false" >> $GITHUB_OUTPUT
              ;;
          esac

  # Full deployment job with dynamic environment support
  deploy:
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.deploy == 'true'
    environment:
      name: ${{ needs.setup.outputs.environment }}
      url: ${{ steps.deploy.outputs.url }}

    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      - name: '🔐 Authenticate to Google Cloud (Dynamic)'
        uses: 'google-github-actions/auth@v2'
        with:
          workload_identity_provider: ${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GCP_WIF_PROVIDER || secrets.STAGING_GCP_WIF_PROVIDER }}
          service_account: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL }}

      - name: '☁️ Set up Cloud SDK (Dynamic)'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          project_id: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_PROJECT_ID || vars.STAGING_GCP_PROJECT_ID }}

      - name: '🐳 Configure Docker for Artifact Registry (Dynamic)'
        run: |
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            REGION="${{ vars.PRODUCTION_GCP_REGION }}"
          else
            REGION="${{ vars.STAGING_GCP_REGION }}"
          fi
          gcloud auth configure-docker ${REGION}-docker.pkg.dev

      - name: '📦 Setup Node.js'
        if: hashFiles('package.json') != ''
        uses: 'actions/setup-node@v4'
        with:
          node-version-file: 'package.json'
          cache: 'npm'

      - name: '🏗️ Install dependencies and build'
        if: hashFiles('package.json') != ''
        run: |
          npm ci
          npm run build

      - name: '🔨 Build and tag Docker image (Dynamic Environment)'
        run: |
          # Set variables based on environment
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            PROJECT_ID="${{ vars.PRODUCTION_GCP_PROJECT_ID }}"
            REGION="${{ vars.PRODUCTION_GCP_REGION }}"
            REPO_NAME="${{ vars.PRODUCTION_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
          else
            PROJECT_ID="${{ vars.STAGING_GCP_PROJECT_ID }}"
            REGION="${{ vars.STAGING_GCP_REGION }}"
            REPO_NAME="${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
          fi

          # Build image once
          docker build -t ${{ env.IMAGE_NAME }}:temp .

          # Set base registry URL
          IMAGE_BASE="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${{ env.IMAGE_NAME }}"
          echo "IMAGE_BASE=${IMAGE_BASE}" >> $GITHUB_ENV

          # Always tag with git SHA (traceability)
          docker tag ${{ env.IMAGE_NAME }}:temp ${IMAGE_BASE}:${{ github.sha }}

          # Tag with environment-specific tags
          IFS=',' read -ra TAGS <<< "${{ needs.setup.outputs.docker_tags }}"
          for tag in "${TAGS[@]}"; do
            docker tag ${{ env.IMAGE_NAME }}:temp ${IMAGE_BASE}:${tag}
          done

      - name: '📤 Push Docker image (Dynamic Environment)'
        run: |
          # Push SHA tag (always)
          docker push ${{ env.IMAGE_BASE }}:${{ github.sha }}

          # Push environment-specific tags
          IFS=',' read -ra TAGS <<< "${{ needs.setup.outputs.docker_tags }}"
          for tag in "${TAGS[@]}"; do
            docker push ${{ env.IMAGE_BASE }}:${tag}
          done
      - name: '🔍 Validate network configuration'
        run: |
          # Validate network variables exist
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            NETWORK="${{ vars.PRODUCTION_VPC_NETWORK }}"
            SUBNET="${{ vars.PRODUCTION_VPC_SUBNET }}"
          else
            NETWORK="${{ vars.STAGING_VPC_NETWORK }}"
            SUBNET="${{ vars.STAGING_VPC_SUBNET }}"
          fi

          if [ -z "${NETWORK}" ] || [ "${NETWORK}" == "null" ]; then
            echo "❌ Network configuration missing"
            exit 1
          fi

          if [ -z "${SUBNET}" ] || [ "${SUBNET}" == "null" ]; then
            echo "❌ Subnet configuration missing"
            exit 1
          fi

      - name: '🚀 Deploy to Cloud Run (Dynamic Environment)'
        id: deploy
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: ${{ needs.setup.outputs.service_name }}
          region: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_REGION || vars.STAGING_GCP_REGION }}
          image: ${{ env.IMAGE_BASE }}:${{ github.sha }}

          # existing environment variables...
          env_vars: |
            NODE_ENV=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_NODE_ENV || vars.STAGING_NODE_ENV }}
            DATABASE_HOSTNAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_HOSTNAME || vars.STAGING_DATABASE_HOSTNAME }}
            DATABASE_USER=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_USER || vars.STAGING_DATABASE_USER }}
            DATABASE_PASSWORD=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_PASSWORD || vars.STAGING_DATABASE_PASSWORD }}
            DATABASE_NAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_NAME || vars.STAGING_DATABASE_NAME }}
            DATABASE_PORT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_PORT || vars.STAGING_DATABASE_PORT }}
            DATABASE_URL=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_URL || secrets.STAGING_DATABASE_URL }}
            JWT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_JWT_SECRET || secrets.STAGING_JWT_SECRET }}
            SENTRY_DSN=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENTRY_DSN || secrets.STAGING_SENTRY_DSN }}
            GATEWAY_PORT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GATEWAY_PORT || vars.STAGING_GATEWAY_PORT }}
            USER_ACTIVATION_REDIRECT_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_USER_ACTIVATION_REDIRECT_URL || vars.STAGING_USER_ACTIVATION_REDIRECT_URL }}
            GOOGLE_PROJECT_ID=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_PROJECT_ID || vars.STAGING_GCP_PROJECT_ID }}
            GOOGLE_CLIENT_ID=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_ID || secrets.STAGING_GOOGLE_CLIENT_ID }}
            GOOGLE_CLIENT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_SECRET || secrets.STAGING_GOOGLE_CLIENT_SECRET }}
            FILES_MANAGER=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_FILES_MANAGER || vars.STAGING_FILES_MANAGER }}
            GOOGLE_BUCKET_NAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GOOGLE_BUCKET_NAME || vars.STAGING_GOOGLE_BUCKET_NAME }}
            PASSWORD_SALT_ROUNDS=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_PASSWORD_SALT_ROUNDS || vars.STAGING_PASSWORD_SALT_ROUNDS }}
            SENDGRID_API_KEY=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENDGRID_API_KEY || secrets.STAGING_SENDGRID_API_KEY }}
            FILES_BASE_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_FILES_BASE_URL || vars.STAGING_FILES_BASE_URL }}
            API_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_API_URL || vars.STAGING_API_URL }}
            WEB_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_WEB_URL || vars.STAGING_WEB_URL }}
            GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL }}
            VERSION=${{ github.sha }}
            BRANCH=${{ github.ref_name }}
            ENVIRONMENT=${{ needs.setup.outputs.environment }}
            DEPLOYED_AT=${{ github.event.head_commit.timestamp }}
            BUILD_NUMBER=${{ github.run_number }}

          # flags: '--cpu=${{ needs.setup.outputs.cpu_limit }} --memory=${{ needs.setup.outputs.memory_limit }} --concurrency=80 --max-instances=${{ needs.setup.outputs.max_instances }} --allow-unauthenticated'
          # flags: >
          #   --cpu=${{ needs.setup.outputs.cpu_limit }}
          #   --memory=${{ needs.setup.outputs.memory_limit }}
          #   --concurrency=80
          #   --max-instances=${{ needs.setup.outputs.max_instances }}
          #   --allow-unauthenticated
          flags: >
            --cpu=${{ needs.setup.outputs.cpu_limit }}
            --memory=${{ needs.setup.outputs.memory_limit }}
            --concurrency=80
            --max-instances=${{ needs.setup.outputs.max_instances }}
            --allow-unauthenticated
            --network=${{ (needs.setup.outputs.environment == 'production') && vars.PRODUCTION_VPC_NETWORK || vars.STAGING_VPC_NETWORK }}
            --subnet=${{ (needs.setup.outputs.environment == 'production') && vars.PRODUCTION_VPC_SUBNET || vars.STAGING_VPC_SUBNET }}
            --vpc-egress=private-ranges-only

      - name: '🧪 Smoke tests'
        run: |
          SERVICE_URL="${{ steps.deploy.outputs.url }}"

          # Basic connectivity test
          if curl -f -s "${SERVICE_URL}" > /dev/null; then
            echo "✅ Service is accessible"
          else
            echo "❌ Service connectivity failed"
            exit 1
          fi

          # Health endpoint test
          HEALTH_ENDPOINTS=("/health" "/healthz" "/ping" "/status")
          for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
            if curl -f -s "${SERVICE_URL}${endpoint}" > /dev/null 2>&1; then
              echo "✅ Health endpoint available: ${endpoint}"
              break
            fi
          done

      - name: '🔥 Hotfix deployment'
        if: startsWith(github.ref_name, 'hotfix/')
        run: |
          echo "🚨 Hotfix deployed: ${{ github.ref_name }}"
          echo "📋 Service: ${{ needs.setup.outputs.service_name }}"
          echo "🔗 URL: ${{ steps.deploy.outputs.url }}"

      - name: '🎉 Production deployment success'
        if: needs.setup.outputs.environment == 'production'
        run: |
          echo "🚀 Production deployment completed!"
          echo "📦 Service: ${{ needs.setup.outputs.service_name }}"
          echo "🔗 URL: ${{ steps.deploy.outputs.url }}"

      - name: '✅ Deployment Summary'
        run: |
          echo "✅ Deployment completed successfully!"
          echo "🎯 Environment: ${{ needs.setup.outputs.environment }}"
          echo "📦 Service: ${{ needs.setup.outputs.service_name }}"
          echo "🔗 URL: ${{ steps.deploy.outputs.url }}"
          echo "🌿 Branch: ${{ github.ref_name }}"
          echo "👤 Actor: ${{ github.actor }}"

  # Slack notification for deployment result
  notify:
    runs-on: ubuntu-latest
    needs: [setup, deploy]
    if: always() && needs.setup.outputs.deploy == 'true'
    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      - name: '🎉 Notify deployment success'
        if: success()
        uses: './.github/actions/unified-slack-notify'
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: 'deployment-success'
          data: '{"branch": "${{ github.ref_name }}", "commit": "${{ github.sha }}", "author": "${{ github.actor }}", "message": "${{ github.event.head_commit.message || ''Manual deployment trigger'' }}", "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", "environment": "${{ needs.setup.outputs.environment }}", "serviceName": "${{ needs.setup.outputs.service_name }}", "serviceUrl": "${{ needs.deploy.outputs.url || ''N/A'' }}", "dockerTags": "${{ needs.setup.outputs.docker_tags }}", "resources": {"cpu": "${{ needs.setup.outputs.cpu_limit }}", "memory": "${{ needs.setup.outputs.memory_limit }}", "maxInstances": "${{ needs.setup.outputs.max_instances }}"}, "buildNumber": "${{ github.run_number }}", "duration": "N/A"}'

      - name: '❌ Notify deployment failure'
        if: failure()
        uses: './.github/actions/unified-slack-notify'
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: 'deployment-failure'
          data: '{"branch": "${{ github.ref_name }}", "commit": "${{ github.sha }}", "author": "${{ github.actor }}", "message": "${{ github.event.head_commit.message || ''Manual deployment trigger'' }}", "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", "environment": "${{ needs.setup.outputs.environment }}", "serviceName": "${{ needs.setup.outputs.service_name }}", "buildNumber": "${{ github.run_number }}", "failedJob": "deploy"}'
