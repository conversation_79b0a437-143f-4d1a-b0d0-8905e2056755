import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { PasswordType } from '../base/base.dto';

export const changePasswordInput = z.object({
  // Old password may not necessarily follow the new password rules
  oldPassword: z.string().optional(),
  newPassword: PasswordType,
  otp: z.string(),
});

export class ChangePasswordInputDto extends createZodDto(changePasswordInput) {}
