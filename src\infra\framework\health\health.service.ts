import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import {
  HealthCheckResponse,
  HealthStatus,
  ServiceHealth,
} from './health.types';

/**
 * HealthService
 *
 * Core service responsible for performing health checks on various system components
 * of the Opus Remote Backend application. This service provides comprehensive monitoring
 * capabilities for:
 *
 * - Database connectivity and performance
 * - Memory usage and availability
 * - Disk I/O operations
 * - Application uptime tracking
 *
 * The service implements three types of health checks:
 * 1. **Comprehensive Health Check** - Full system assessment including all components
 * 2. **Readiness Check** - Focuses on external dependencies required for operation
 * 3. **Liveness Check** - Basic application responsiveness verification
 *
 * Each health check returns standardized responses with detailed metrics including
 * response times, status levels (healthy/degraded/unhealthy), and diagnostic information.
 *
 * The service uses parallel execution for multiple checks to minimize total response time
 * and implements proper error handling to ensure graceful degradation when components fail.
 *
 * @example
 * ```typescript
 * const healthService = new HealthService(prismaService);
 *
 * // Perform comprehensive health check
 * const health = await healthService.checkHealth();
 * console.log(`Overall status: ${health.status}`);
 *
 * // Check specific component
 * const dbHealth = await healthService.checkDatabase();
 * console.log(`Database response time: ${dbHealth.responseTime}ms`);
 * ```
 */
@Injectable()
export class HealthService {
  /** Logger instance for health check operations */
  private readonly logger = new Logger(HealthService.name);

  /** Application start timestamp for uptime calculation */
  private readonly startTime = Date.now();

  /**
   * Creates an instance of HealthService.
   *
   * @param prisma - Prisma service instance for database health checks
   */
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Performs a comprehensive health check of all system components
   *
   * Executes parallel health checks on database, memory, and disk systems to provide
   * a complete assessment of application health. The overall status is determined by
   * the most critical component status:
   *
   * - **healthy**: All components are functioning normally
   * - **degraded**: Some components are slow but functional
   * - **unhealthy**: Critical components (especially database) are failing
   *
   * This method uses Promise.allSettled to ensure all checks complete even if some fail,
   * providing maximum diagnostic information for troubleshooting.
   *
   * @returns Promise<HealthCheckResponse> - Comprehensive health status with detailed service information
   *
   * @example
   * ```typescript
   * const health = await healthService.checkHealth();
   *
   * if (health.status === 'unhealthy') {
   *   console.error('Critical system failure detected');
   *   // Alert operations team
   * } else if (health.status === 'degraded') {
   *   console.warn('System performance degraded');
   *   // Monitor closely
   * }
   *
   * // Check specific service details
   * const dbStatus = health.services.database;
   * console.log(`Database response time: ${dbStatus.responseTime}ms`);
   * ```
   */
  async checkHealth(): Promise<HealthCheckResponse> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkMemory(),
      this.checkDisk(),
    ]);

    const services: Record<string, ServiceHealth> = {};
    let overallStatus: HealthStatus = 'healthy';

    // Database check
    if (checks[0].status === 'fulfilled') {
      services.database = checks[0].value;
    } else {
      services.database = {
        status: 'unhealthy',
        message: 'Database check failed',
        error: checks[0].reason?.message,
        responseTime: 0,
      };
      overallStatus = 'unhealthy';
    }

    // Memory check
    if (checks[1].status === 'fulfilled') {
      services.memory = checks[1].value;
    } else {
      services.memory = {
        status: 'unhealthy',
        message: 'Memory check failed',
        error: checks[1].reason?.message,
        responseTime: 0,
      };
      overallStatus = 'degraded';
    }

    // Disk check
    if (checks[2].status === 'fulfilled') {
      services.disk = checks[2].value;
    } else {
      services.disk = {
        status: 'unhealthy',
        message: 'Disk check failed',
        error: checks[2].reason?.message,
        responseTime: 0,
      };
      overallStatus = 'degraded';
    }

    // If any critical service is unhealthy, mark overall as unhealthy
    if (services.database.status === 'unhealthy') {
      overallStatus = 'unhealthy';
    } else if (
      Object.values(services).some((service) => service.status === 'degraded')
    ) {
      overallStatus = 'degraded';
    }

    const response: HealthCheckResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services,
    };

    this.logger.log(`Health check completed: ${overallStatus}`);
    return response;
  }

  /**
   * Performs a readiness check focusing on external dependencies
   *
   * This method specifically checks if the application is ready to serve requests
   * by verifying connectivity to critical external services, primarily the database.
   *
   * Used by container orchestration platforms (Kubernetes) and load balancers to
   * determine when an instance should start receiving traffic. The check is binary:
   * either the application is ready (healthy) or not ready (unhealthy).
   *
   * Unlike the comprehensive health check, this focuses only on dependencies
   * required for basic operation and doesn't include internal resource checks
   * like memory or disk usage.
   *
   * @returns Promise<HealthCheckResponse> - Readiness status with database connectivity information
   *
   * @example
   * ```typescript
   * const readiness = await healthService.checkReadiness();
   *
   * if (readiness.status === 'healthy') {
   *   console.log('Application ready to receive traffic');
   * } else {
   *   console.log('Application not ready - database unavailable');
   *   // Remove from load balancer pool
   * }
   * ```
   */
  async checkReadiness(): Promise<HealthCheckResponse> {
    // Readiness check focuses on external dependencies
    const databaseCheck = await this.checkDatabase();

    const status: HealthStatus =
      databaseCheck.status === 'healthy' ? 'healthy' : 'unhealthy';

    return {
      status,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseCheck,
      },
    };
  }

  /**
   * Performs a basic liveness check to verify application responsiveness
   *
   * This is the simplest health check that only verifies the application process
   * is alive and can respond to HTTP requests. It doesn't check external dependencies
   * or resource usage - if this method can execute and return, the application is
   * considered alive.
   *
   * Used by container orchestration platforms (Kubernetes) to determine if a
   * container should be restarted. This check should be lightweight and fast
   * to avoid false positives during high load situations.
   *
   * @returns Promise<HealthCheckResponse> - Basic liveness status (always healthy if reachable)
   *
   * @example
   * ```typescript
   * const liveness = await healthService.checkLiveness();
   *
   * // This will always be 'healthy' if the method completes
   * console.log(`Application alive for ${liveness.uptime}ms`);
   * ```
   */
  async checkLiveness(): Promise<HealthCheckResponse> {
    // Liveness check is simple - if we can respond, we're alive
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {},
    };
  }

  /**
   * Performs database connectivity and performance check
   *
   * Executes a simple SQL query to verify database connectivity and measures
   * response time to assess performance. The health status is determined by:
   *
   * - **healthy**: Response time < 1000ms
   * - **degraded**: Response time >= 1000ms but connection successful
   * - **unhealthy**: Connection failed or query error
   *
   * This check is critical for application readiness as most operations
   * depend on database availability.
   *
   * @returns Promise<ServiceHealth> - Database health status with connection details
   * @private
   *
   * @example
   * ```typescript
   * const dbHealth = await this.checkDatabase();
   *
   * if (dbHealth.status === 'unhealthy') {
   *   console.error(`Database failed: ${dbHealth.error}`);
   * } else {
   *   console.log(`Database OK: ${dbHealth.responseTime}ms`);
   * }
   * ```
   */
  private async checkDatabase(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      // Simple database connectivity check
      await this.prisma.$queryRaw`SELECT 1`;

      const responseTime = Date.now() - startTime;

      return {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        message:
          responseTime < 1000
            ? 'Database connection is healthy'
            : 'Database response is slow',
        responseTime,
        details: {
          connectionPool: 'active',
          queryTime: `${responseTime}ms`,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        status: 'unhealthy',
        message: 'Database connection failed',
        error:
          error instanceof Error ? error.message : 'Unknown database error',
        responseTime,
        details: {
          connectionPool: 'failed',
          queryTime: `${responseTime}ms`,
        },
      };
    }
  }

  /**
   * Performs memory usage assessment
   *
   * Analyzes current memory consumption using Node.js process.memoryUsage()
   * and determines health status based on heap usage percentage:
   *
   * - **healthy**: Memory usage < 75%
   * - **degraded**: Memory usage 75-90%
   * - **unhealthy**: Memory usage > 90%
   *
   * Provides detailed memory metrics including heap usage, RSS, and external memory.
   * High memory usage can indicate memory leaks or excessive load.
   *
   * @returns Promise<ServiceHealth> - Memory health status with usage details
   * @private
   *
   * @example
   * ```typescript
   * const memHealth = await this.checkMemory();
   *
   * if (memHealth.status === 'degraded') {
   *   console.warn(`High memory usage: ${memHealth.details.usagePercent}`);
   * }
   * ```
   */
  private async checkMemory(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      const memUsage = process.memoryUsage();
      const totalMemory = memUsage.heapTotal;
      const usedMemory = memUsage.heapUsed;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;

      const responseTime = Date.now() - startTime;

      let status: HealthStatus = 'healthy';
      let message = 'Memory usage is normal';

      if (memoryUsagePercent > 90) {
        status = 'unhealthy';
        message = 'Memory usage is critically high';
      } else if (memoryUsagePercent > 75) {
        status = 'degraded';
        message = 'Memory usage is high';
      }

      return {
        status,
        message,
        responseTime,
        details: {
          heapUsed: `${Math.round(usedMemory / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(totalMemory / 1024 / 1024)}MB`,
          usagePercent: `${memoryUsagePercent.toFixed(1)}%`,
          external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
          rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        status: 'unhealthy',
        message: 'Memory check failed',
        error: error instanceof Error ? error.message : 'Unknown memory error',
        responseTime,
      };
    }
  }

  /**
   * Performs disk I/O performance check
   *
   * Tests disk write/read performance by creating and deleting a temporary file
   * in the system's temp directory. Health status is determined by I/O speed:
   *
   * - **healthy**: I/O operation < 100ms
   * - **degraded**: I/O operation >= 100ms but successful
   * - **unhealthy**: I/O operation failed
   *
   * This check helps identify disk performance issues that could affect
   * application performance, especially for file uploads, logging, and caching.
   *
   * @returns Promise<ServiceHealth> - Disk I/O health status with performance metrics
   * @private
   *
   * @example
   * ```typescript
   * const diskHealth = await this.checkDisk();
   *
   * if (diskHealth.status === 'degraded') {
   *   console.warn(`Slow disk I/O: ${diskHealth.responseTime}ms`);
   * }
   * ```
   */
  private async checkDisk(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
      // Basic disk check - ensure we can write to temp directory
      const fs = await import('fs/promises');
      const path = await import('path');
      const os = await import('os');

      const tempFile = path.join(os.tmpdir(), `health-check-${Date.now()}.tmp`);

      await fs.writeFile(tempFile, 'health-check');
      await fs.unlink(tempFile);

      const responseTime = Date.now() - startTime;

      return {
        status: responseTime < 100 ? 'healthy' : 'degraded',
        message:
          responseTime < 100 ? 'Disk I/O is healthy' : 'Disk I/O is slow',
        responseTime,
        details: {
          writeTime: `${responseTime}ms`,
          tempDir: os.tmpdir(),
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        status: 'unhealthy',
        message: 'Disk I/O check failed',
        error: error instanceof Error ? error.message : 'Unknown disk error',
        responseTime,
      };
    }
  }
}
