import { FilePermission } from '@/core/enums/file.enum';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

export const filePermission = z
  .object({
    permission: z
      .nativeEnum(FilePermission)
      .describe('Who can access the file'),
  })
  .describe('The metadata of the file to be uploaded');
export class FilePermissionDto extends createZodDto(filePermission) {}

export const fileMeta = z
  .object({
    id: IdType.describe('The unique identifier of the file'),
  })
  .merge(filePermission)
  .extend({
    fileName: z.string().describe('The file name of the uploaded file'),
    originalFileName: z.string(),
    url: z.string(),
    fileSize: z.number(),
    contentType: z.string(),
    uploaderId: IdType,
  })
  .describe('The details of the file to be uploaded');

export class FileMetaDto extends createZodDto(fileMeta) {}
