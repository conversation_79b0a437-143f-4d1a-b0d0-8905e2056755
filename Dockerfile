# ------ Base Debian OS with volta ------
FROM debian:stable-slim as base
WORKDIR /opus

# Prerequisites for volta installation
RUN apt-get update \
  && apt-get install -y \
  curl \
  ca-certificates \
  --no-install-recommends

# Setup Bash
SHELL ["/bin/bash", "-c"]
ENV BASH_ENV ~/.bashrc
ENV VOLTA_HOME /root/.volta
ENV PATH $VOLTA_HOME/bin:$PATH

# Install volta
RUN curl https://get.volta.sh | bash

# ------ The actual production image ------
FROM base AS prod
WORKDIR /opus

# Copy package files first for better caching
COPY package.json .
COPY package-lock.json .

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY . .

# Generate Prisma client and build the application
RUN npx prisma generate
RUN npm run build

# Copy and set permissions for start script
COPY ./scripts/start.sh ./scripts/start.sh
RUN chmod +x ./scripts/start.sh

# Expose the port that Cloud Run expects
EXPOSE 8080

# Set the default port for Cloud Run
ENV PORT=8080

CMD ["./scripts/start.sh"]