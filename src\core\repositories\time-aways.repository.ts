import { FindTimeAwayFilterDto } from '@/shared/dtos/time-aways/find-time-away-filter.dto';
import { FindUserTimeAwaySortDto } from '@/shared/dtos/time-aways/find-user-time-away-sort.dto';
import { OmitRelation, Repository } from '../base/repository';
import { TimeAwayEntity } from '../domain/entities/time-away.entity';

export interface UserLeaves {
  userId: TimeAwayEntity['userId'];
  timeAwayTypeId: TimeAwayEntity['timeAwayTypeId'];
  total: number;
}

export abstract class TimeAwaysRepository extends Repository<
  TimeAwayEntity,
  FindTimeAwayFilterDto,
  FindUserTimeAwaySortDto
> {
  abstract sumChangeByUserIdAndTimeAwayTypeId(
    userId: string,
  ): Promise<UserLeaves[]>;

  abstract findOneForRequest(
    filter: FindTimeAwayFilterDto,
  ): Promise<TimeAwayEntity | null>;

  static removeRelationships(
    data: TimeAwayEntity,
  ): OmitRelation<TimeAwayEntity> {
    const { timeAwayType, ...rest } = data;
    return rest;
  }
}
