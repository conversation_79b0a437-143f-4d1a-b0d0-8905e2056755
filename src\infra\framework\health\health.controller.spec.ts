import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { HealthCheckResponse } from './health.types';

describe('HealthController', () => {
  let controller: HealthController;
  let service: HealthService;

  const mockHealthResponse: HealthCheckResponse = {
    status: 'healthy',
    timestamp: '2024-01-01T00:00:00.000Z',
    uptime: 60000,
    version: '1.0.0',
    environment: 'test',
    services: {
      database: {
        status: 'healthy',
        message: 'Database connection is healthy',
        responseTime: 50,
      },
    },
  };

  beforeEach(async () => {
    const mockHealthService = {
      checkHealth: jest.fn().mockResolvedValue(mockHealthResponse),
      checkReadiness: jest.fn().mockResolvedValue(mockHealthResponse),
      checkLiveness: jest.fn().mockResolvedValue(mockHealthResponse),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthService,
          useValue: mockHealthService,
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    service = module.get<HealthService>(HealthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getHealth', () => {
    it('should return health check response', async () => {
      const result = await controller.getHealth();

      expect(result).toEqual(mockHealthResponse);
      expect(service.checkHealth).toHaveBeenCalled();
    });
  });

  describe('getReadiness', () => {
    it('should return readiness check response', async () => {
      const result = await controller.getReadiness();

      expect(result).toEqual(mockHealthResponse);
      expect(service.checkReadiness).toHaveBeenCalled();
    });
  });

  describe('getLiveness', () => {
    it('should return liveness check response', async () => {
      const result = await controller.getLiveness();

      expect(result).toEqual(mockHealthResponse);
      expect(service.checkLiveness).toHaveBeenCalled();
    });
  });
});
