import { addDays, isBefore, isEqual, startOfDay } from 'date-fns';
import { fromZonedTime, toZonedTime } from 'date-fns-tz';

export const startOfDayAtTimezone = (date: Date, timezone: string) => {
  const zonedDate = toZonedTime(date, timezone);
  const startOfDayZoned = startOfDay(zonedDate);
  const utcDate = fromZonedTime(startOfDayZoned, timezone);
  return utcDate;
};

export const eachDayOfIntervalAtTimezone = (
  startDate: Date | string,
  endDate: Date | string,
  timezone: string = 'UTC',
) => {
  startDate = new Date(startDate);
  const interval: Date[] = [];
  for (
    let currentDate = startDate;
    isBefore(currentDate, endDate) || isEqual(currentDate, endDate);
    currentDate = addDays(currentDate, 1)
  ) {
    // Get the start of the day in the specified timezone
    const startOfDay = startOfDayAtTimezone(currentDate, timezone);
    interval.push(startOfDay);
  }
  return interval;
};
export { toZonedTime };
