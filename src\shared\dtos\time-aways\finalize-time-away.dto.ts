import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

export const checkListAnswerInput = z.object({
  id: IdType,
  answer: z.boolean(),
});

export const finalizeTimeAwayInput = z.object({
  isApproved: z.coerce.boolean(),
  approvalMessage: z.string().optional(),
  checkListAnswers: checkListAnswerInput.array().optional(),
  override: z.boolean().optional(),
});

export class FinalizeTimeAwayInputDto extends createZodDto(
  finalizeTimeAwayInput,
) {}
