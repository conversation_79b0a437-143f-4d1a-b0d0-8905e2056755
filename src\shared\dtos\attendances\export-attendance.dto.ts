import { z } from 'zod';
import { findAttendanceFilter } from './find-attendance-filter.dto';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';

export const exportAttendanceInput = findAttendanceFilter
  .pick({
    startDate: true,
    endDate: true,
  })
  .extend({ format: z.enum(['pdf', 'csv']) })
  .required();
export class ExportAttendanceInputDto extends createZodDto(
  exportAttendanceInput,
) {}
