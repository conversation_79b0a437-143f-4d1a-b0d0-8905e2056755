# GitHub Actions Environment Setup Guide

This document provides a comprehensive guide for setting up GitHub Actions variables and secrets for the Opus Remote Backend CI/CD pipeline across Production, Staging, and Development environments.

## 🔧 Environment Overview

The CI/CD pipeline supports three environments:
- **Production** (`main` branch)
- **Staging** (`staging` branch)
- **Development** (`develop` branch)

## 🔐 Security Best Practices

### Variables vs Secrets
- **Variables**: Non-sensitive configuration data (URLs, ports, project IDs)
- **Secrets**: Sensitive data (passwords, API keys, tokens, database credentials)

### Naming Convention
- Format: `{ENVIRONMENT}_{CATEGORY}_{NAME}`
- Examples: `PRODUCTION_GCP_PROJECT_ID`, `STAGING_DATABASE_PASSWORD`

## 📋 Required Configuration

### 🚨 Production Environment

#### Repository Variables (Settings → Secrets and variables → Actions → Variables)
```
PRODUCTION_NODE_ENV=production
PRODUCTION_GCP_PROJECT_ID=your-prod-project-id
PRODUCTION_GCP_REGION=us-central1
PRODUCTION_GCP_DOCKER_REPO=opus-remote-backend
PRODUCTION_GCP_SA_EMAIL=<EMAIL>
PRODUCTION_VPC_NETWORK=your-prod-vpc-network
PRODUCTION_VPC_SUBNET=your-prod-vpc-subnet
PRODUCTION_GATEWAY_PORT=8080
PRODUCTION_USER_ACTIVATION_REDIRECT_URL=https://your-prod-domain.com/activate
PRODUCTION_FILES_MANAGER=gcp
PRODUCTION_GOOGLE_BUCKET_NAME=your-prod-bucket
PRODUCTION_PASSWORD_SALT_ROUNDS=12
PRODUCTION_FILES_BASE_URL=https://storage.googleapis.com/your-prod-bucket
PRODUCTION_API_URL=https://api.your-prod-domain.com
PRODUCTION_WEB_URL=https://your-prod-domain.com
```

#### Repository Secrets (Settings → Secrets and variables → Actions → Secrets)
```
PRODUCTION_GCP_WIF_PROVIDER=projects/123456789/locations/global/workloadIdentityPools/github-actions/providers/github
PRODUCTION_DATABASE_HOSTNAME=your-prod-db-host
PRODUCTION_DATABASE_USER=your-prod-db-user
PRODUCTION_DATABASE_PASSWORD=your-prod-db-password
PRODUCTION_DATABASE_NAME=your-prod-db-name
PRODUCTION_DATABASE_PORT=5432
PRODUCTION_DATABASE_URL=************************************/database
PRODUCTION_JWT_SECRET=your-super-secure-jwt-secret-production
PRODUCTION_SENTRY_DSN=https://<EMAIL>/project-id
PRODUCTION_GOOGLE_CLIENT_ID=your-google-oauth-client-id
PRODUCTION_GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
PRODUCTION_SENDGRID_API_KEY=SG.your-sendgrid-api-key
```

### 📍 Staging Environment

#### Repository Variables
```
STAGING_NODE_ENV=staging
STAGING_GCP_PROJECT_ID=your-staging-project-id
STAGING_GCP_REGION=us-central1
STAGING_GCP_DOCKER_REPO=opus-remote-backend
STAGING_GCP_SA_EMAIL=<EMAIL>
STAGING_VPC_NETWORK=your-staging-vpc-network
STAGING_VPC_SUBNET=your-staging-vpc-subnet
STAGING_GATEWAY_PORT=8080
STAGING_USER_ACTIVATION_REDIRECT_URL=https://staging.your-domain.com/activate
STAGING_FILES_MANAGER=gcp
STAGING_GOOGLE_BUCKET_NAME=your-staging-bucket
STAGING_PASSWORD_SALT_ROUNDS=10
STAGING_FILES_BASE_URL=https://storage.googleapis.com/your-staging-bucket
STAGING_API_URL=https://api-staging.your-domain.com
STAGING_WEB_URL=https://staging.your-domain.com
```

#### Repository Secrets
```
STAGING_GCP_WIF_PROVIDER=projects/123456789/locations/global/workloadIdentityPools/github-actions/providers/github
STAGING_DATABASE_HOSTNAME=your-staging-db-host
STAGING_DATABASE_USER=your-staging-db-user
STAGING_DATABASE_PASSWORD=your-staging-db-password
STAGING_DATABASE_NAME=your-staging-db-name
STAGING_DATABASE_PORT=5432
STAGING_DATABASE_URL=************************************/database
STAGING_JWT_SECRET=your-jwt-secret-staging
STAGING_SENTRY_DSN=https://<EMAIL>/project-id
STAGING_GOOGLE_CLIENT_ID=your-google-oauth-client-id-staging
STAGING_GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret-staging
STAGING_SENDGRID_API_KEY=SG.your-sendgrid-api-key-staging
```

### 🔧 Development Environment

#### Repository Variables
```
DEVELOPMENT_NODE_ENV=development
DEVELOPMENT_GCP_PROJECT_ID=your-dev-project-id
DEVELOPMENT_GCP_REGION=us-central1
DEVELOPMENT_GCP_DOCKER_REPO=opus-remote-backend
DEVELOPMENT_GCP_SA_EMAIL=<EMAIL>
DEVELOPMENT_VPC_NETWORK=your-dev-vpc-network
DEVELOPMENT_VPC_SUBNET=your-dev-vpc-subnet
DEVELOPMENT_GATEWAY_PORT=8080
DEVELOPMENT_USER_ACTIVATION_REDIRECT_URL=https://dev.your-domain.com/activate
DEVELOPMENT_FILES_MANAGER=gcp
DEVELOPMENT_GOOGLE_BUCKET_NAME=your-dev-bucket
DEVELOPMENT_PASSWORD_SALT_ROUNDS=8
DEVELOPMENT_FILES_BASE_URL=https://storage.googleapis.com/your-dev-bucket
DEVELOPMENT_API_URL=https://api-dev.your-domain.com
DEVELOPMENT_WEB_URL=https://dev.your-domain.com
```

#### Repository Secrets
```
DEVELOPMENT_GCP_WIF_PROVIDER=projects/123456789/locations/global/workloadIdentityPools/github-actions/providers/github
DEVELOPMENT_DATABASE_HOSTNAME=your-dev-db-host
DEVELOPMENT_DATABASE_USER=your-dev-db-user
DEVELOPMENT_DATABASE_PASSWORD=your-dev-db-password
DEVELOPMENT_DATABASE_NAME=your-dev-db-name
DEVELOPMENT_DATABASE_PORT=5432
DEVELOPMENT_DATABASE_URL=************************************/database
DEVELOPMENT_JWT_SECRET=your-jwt-secret-development
DEVELOPMENT_SENTRY_DSN=https://<EMAIL>/project-id
DEVELOPMENT_GOOGLE_CLIENT_ID=your-google-oauth-client-id-dev
DEVELOPMENT_GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret-dev
DEVELOPMENT_SENDGRID_API_KEY=SG.your-sendgrid-api-key-dev
```

### 🔔 Shared Secrets (All Environments)
```
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
```

## 🚨 Common Issues and Solutions

### Issue 1: Missing Environment Variables
**Error**: `❌ Network configuration missing for {environment}`
**Solution**: Ensure all VPC network and subnet variables are set for each environment.

### Issue 2: Incorrect Secret/Variable Classification
**Error**: Variables showing as empty or undefined
**Solution**: 
- Move sensitive data (passwords, keys, tokens) to Secrets
- Keep non-sensitive config (URLs, ports, project IDs) as Variables

### Issue 3: Workload Identity Federation Issues
**Error**: Authentication failures during deployment
**Solution**: 
- Verify `{ENV}_GCP_WIF_PROVIDER` secrets are correctly configured
- Ensure service account has proper permissions
- Check that the WIF pool and provider are properly set up

### Issue 4: Database Connection Issues
**Error**: Database connection failures
**Solution**: 
- Verify all database secrets are set: `DATABASE_HOSTNAME`, `DATABASE_USER`, `DATABASE_PASSWORD`, `DATABASE_NAME`, `DATABASE_PORT`
- Ensure `DATABASE_URL` is properly formatted
- Check network connectivity between Cloud Run and database

## 🔍 Validation Checklist

Before deploying, ensure:

### ✅ Production Checklist
- [ ] All PRODUCTION_* variables are set
- [ ] All PRODUCTION_* secrets are set
- [ ] VPC network and subnet are configured
- [ ] Service account has Cloud Run deployment permissions
- [ ] Database is accessible from the VPC
- [ ] Domain and SSL certificates are configured

### ✅ Staging Checklist
- [ ] All STAGING_* variables are set
- [ ] All STAGING_* secrets are set
- [ ] VPC network and subnet are configured
- [ ] Service account has Cloud Run deployment permissions
- [ ] Database is accessible from the VPC

### ✅ Development Checklist
- [ ] All DEVELOPMENT_* variables are set
- [ ] All DEVELOPMENT_* secrets are set
- [ ] VPC network and subnet are configured
- [ ] Service account has Cloud Run deployment permissions
- [ ] Database is accessible from the VPC

## 🛠️ Setup Instructions

1. **Navigate to Repository Settings**
   - Go to your GitHub repository
   - Click on "Settings" tab
   - Select "Secrets and variables" → "Actions"

2. **Add Variables**
   - Click on "Variables" tab
   - Add each variable from the lists above
   - Use exact naming as specified

3. **Add Secrets**
   - Click on "Secrets" tab
   - Add each secret from the lists above
   - Use exact naming as specified

4. **Verify Configuration**
   - Run a test deployment to staging
   - Check logs for any missing variables or secrets
   - Validate that all services are accessible

## 📚 Additional Resources

- [GitHub Actions Environments Documentation](https://docs.github.com/en/actions/deployment/targeting-different-environments/using-environments-for-deployment)
- [Google Cloud Workload Identity Federation](https://cloud.google.com/iam/docs/workload-identity-federation)
- [Cloud Run Deployment Guide](https://cloud.google.com/run/docs/deploying)

## 🆘 Troubleshooting

If you encounter issues:

1. Check the GitHub Actions logs for specific error messages
2. Verify all required variables and secrets are set
3. Ensure proper permissions are granted to service accounts
4. Validate network connectivity and firewall rules
5. Test database connections manually

For additional support, please refer to the project documentation or contact the development team.