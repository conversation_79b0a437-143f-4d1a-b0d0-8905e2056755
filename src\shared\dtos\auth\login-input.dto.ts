import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';

export const googleLoginInput = z
  .object({
    scheme: z.literal('google'),
    code: z.string().describe('The authorization code from the Google auth'),
  })
  .describe('The required payload for authenticating a user using Google');
export class GoogleLoginInputDto extends createZodDto(googleLoginInput) {}

export const emailLoginInput = z
  .object({
    scheme: z.literal('email'),
    email: z.string().describe('The email of the user that wants to sign in'),
    password: z
      .string()
      .describe('The plain text password of the user who wants to sign in'),
  })
  .describe('The required payload for authenticating a user using Google');

export class EmailLoginInput extends createZodDto(emailLoginInput) {}

export const loginInput = z.object({
  auth: z.union([emailLoginInput, googleLoginInput]),
  timezone: z
    .string()
    .optional()
    .describe("The current timezone at user's location location"),
});
export class <PERSON>ginInputDto extends createZodDto(loginInput) {}
