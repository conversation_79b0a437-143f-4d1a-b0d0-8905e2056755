import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { CreatedAttendanceDto } from '@/shared/dtos/attendances/created-attendance.dto';
import { FindUserAttendanceFilterDto } from '@/shared/dtos/attendances/find-user-attendance-filter.dto';
import { GroupedUserDayAttendanceDto } from '@/shared/dtos/attendances/grouped-user-day-attendance.dto';
import { UpsertUserAttendanceDto } from '@/shared/dtos/attendances/upsert-user-attendance.dto';
import { DeleteOneAttendanceUseCase } from '@/use-cases/attendances/delete-one-attendance.use-case';
import { FindOneAttendancesUseCase } from '@/use-cases/attendances/find-one-attendance.use-case';
import { GroupUserAttendancesByDayUseCase } from '@/use-cases/attendances/group-user-attendances-by-day.use-case';
import { UpsertAttendanceUseCase } from '@/use-cases/attendances/upsert-attendance.use-case';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import {
  UserIdMatch,
  UserIdOrAccountIdMatch,
} from '../auth/decorators/requester-match.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

@ApiTags('users-attendance')
@Controller('users')
@SwaggerAuth()
@ApiDefaultErrorMessage()
export class UsersAttendanceController {
  constructor(
    private upsertAttendanceUseCase: UpsertAttendanceUseCase,
    private findOneAttendanceUseCase: FindOneAttendancesUseCase,
    private deleteOneAttendanceUseCase: DeleteOneAttendanceUseCase,
    private groupUserAttendancesByDayUseCase: GroupUserAttendancesByDayUseCase,
  ) {}
  @Put('/:userId/attendances')
  @UserIdMatch({ userIdLocation: 'params', userIdParamName: 'userId' })
  @ApiOperation({
    summary: 'Upsert a time log',
    description:
      'Creates a time log when `id` is not provided, updates otherwise',
  })
  @ApiCreatedResponse({
    type: CreatedAttendanceDto,
    description: 'Time log has been created',
  })
  @ApiOkResponse({
    type: CreatedAttendanceDto,
    description: 'Time log has been updated',
  })
  @ApiConflictResponse({ description: 'Invalid ids' })
  async upsertAttendance(
    @Param('userId') userId: string,
    @Body() data: UpsertUserAttendanceDto,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.upsertAttendanceUseCase.execute({ ...data, userId }, user);
  }

  @Get('/:userId/attendances')
  @UserIdOrAccountIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: "Get the user's time logs",
    description: "Get the user's time logs, grouped by day",
  })
  @ApiOkResponse({
    type: GroupedUserDayAttendanceDto,
    description: 'The time logs of the user grouped by day',
  })
  async findAttendances(
    @Param('userId') userId: string,
    @Query() filter: FindUserAttendanceFilterDto,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.groupUserAttendancesByDayUseCase.execute(
      userId,
      user.timezone,
      filter,
    );
  }

  @Get('/:userId/attendances/latest')
  @UserIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: 'Get latest time log of a user',
  })
  @ApiOkResponse({
    type: CreatedAttendanceDto,
    description: 'The time log',
  })
  async findLatestAttendance(@Param('userId') userId: string) {
    const attendance = await this.findOneAttendanceUseCase.execute(
      { userId },
      { inDate: 'desc' },
    );

    if (!attendance) {
      throw new EntityNotFoundException();
    }

    return attendance;
  }

  @Get('/:userId/attendances/:attendanceId')
  @UserIdOrAccountIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: 'Get time log of a user by `attendanceId`',
  })
  @ApiOkResponse({
    type: CreatedAttendanceDto,
    description: 'The time log',
  })
  async findOneAttendance(
    @Param('userId') userId: string,
    @Param('attendanceId') attendanceId: string,
  ) {
    return this.findOneAttendanceUseCase.execute({
      id: attendanceId,
      userId,
    });
  }

  @Delete('/:userId/attendances/:attendanceId')
  @UserIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiParam({
    name: 'userId',
    type: String,
  })
  @ApiOperation({
    summary: 'Delete the attendance log of a user by `attendanceId`',
  })
  @ApiNoContentResponse({
    description: 'The attendance log has been deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'The attendance log with id cannot be found',
  })
  async deleteOneAttendance(@Param('attendanceId') attendanceId: string) {
    return this.deleteOneAttendanceUseCase.execute(attendanceId);
  }
}
