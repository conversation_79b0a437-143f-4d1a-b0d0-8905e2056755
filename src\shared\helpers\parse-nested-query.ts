import { SomeZodObject, ZodRawShape } from 'zod';

export type ParsedQuery<T extends ZodRawShape> = {
  [P in keyof T]: T[P]['_type'];
};

const sortKey = 'sort';
export const parseNestedQuery = async <T extends SomeZodObject>(
  schema: T,
  query: unknown,
): Promise<ParsedQuery<T['shape']>> => {
  const ret: Record<string, unknown> = {};

  for (const key in schema.shape) {
    if (key !== sortKey) {
      ret[key] = await schema.shape[key].parseAsync(query);
      continue;
    }

    // Retain Sort Order
    const queryCopy = query as any;
    if (!queryCopy[sortKey]) {
      continue;
    }

    const parsed = await schema.shape[key].parseAsync(query);
    const ordered: typeof parsed = {};

    const parsedSortKeys = Object.keys(parsed[sortKey]);
    const querySortKeys = Object.keys(queryCopy[sortKey]);

    for (const currentSortKey of querySortKeys) {
      if (parsedSortKeys.includes(currentSortKey)) {
        ordered[currentSortKey] = parsed[sortKey][currentSortKey];
      }
    }
    ret[key] = { [sortKey]: ordered };
  }
  return ret as ParsedQuery<T['shape']>;
};
