import { FilesManager } from '@/core/abstracts/files-manager';
import { Injectable } from '@nestjs/common';
import { access, mkdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';

@Injectable()
export class LocalFilesManagerService extends FilesManager {
  private uploadPath = './uploads';

  async upload(originalFileName: string, file: Buffer): Promise<string> {
    const randomizedFileName = this.randomizeFileName(originalFileName);
    const path = this.pathMaker(randomizedFileName);

    try {
      await access(this.uploadPath);
    } catch (error) {
      await mkdir(this.uploadPath);
    }

    await writeFile(path, file);

    return randomizedFileName;
  }

  async download(filename: string): Promise<Buffer> {
    const path = this.pathMaker(filename);
    const content = await readFile(path);
    return content;
  }

  private pathMaker(fileName: string) {
    return join(this.uploadPath, fileName);
  }
}
