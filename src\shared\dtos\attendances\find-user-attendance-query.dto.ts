import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { findAttendanceQuery } from './find-attendance-query.dto';
import { findUserAttendanceFilter } from './find-user-attendance-filter.dto';

export const findUserAttendanceQuery = findAttendanceQuery.extend({
  filter: findUserAttendanceFilter,
});

export class FindUserAttendanceQueryDto extends createZodDto(
  findUserAttendanceQuery,
) {}
