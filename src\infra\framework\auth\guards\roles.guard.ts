import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Roles } from '../decorators/roles.decorator';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { InsufficientPermissionException } from '@/core/exceptions/opus-exceptions';
import { OpusRoles } from '@/core/enums/role.enum';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get(Roles, context.getHandler());
    if (!requiredRoles) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const user: CreatedUserDetailsDto = request.user;

    if (user.role.title == OpusRoles.Superadmin) {
      return true;
    }

    if (!requiredRoles.includes(user.role.title as OpusRoles)) {
      throw new InsufficientPermissionException('Insufficient permissions');
    }

    return true;
  }
}
