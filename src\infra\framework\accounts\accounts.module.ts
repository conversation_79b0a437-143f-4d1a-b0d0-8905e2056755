import { AccountsRepository } from '@/core/repositories/accounts.repository';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { CreateAccountUseCase } from '@/use-cases/accounts/create-account.use-case';
import { FindAccountsUseCase } from '@/use-cases/accounts/find-accounts.use-case';
import { Module } from '@nestjs/common';
import { AccountsController } from './accounts.controller';

@Module({
  imports: [PrismaModule],
  controllers: [AccountsController],
  providers: [
    {
      provide: FindAccountsUseCase,
      useFactory: (repository: AccountsRepository) =>
        new FindAccountsUseCase(repository),
      inject: [AccountsRepository],
    },
    {
      provide: CreateAccountUseCase,
      useFactory: (repository: AccountsRepository) =>
        new CreateAccountUseCase(repository),
      inject: [AccountsRepository],
    },
  ],
})
export class AccountsModule {}
