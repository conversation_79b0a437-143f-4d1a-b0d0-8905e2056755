CREATE OR REPLACE VIEW "profile_info" AS
SELECT u.id,
       first_name,
       last_name,
       birth_date,
       marital_status,
       gender,
       address_street_1,
       address_street_2,
       city,
       province,
       postal_code,
       country,
       u.email AS work_email,
       work_phone,
       mobile_phone,
       home_phone,
       personal_email,
       emergency_name,
       emergency_relationship,
       emergency_mobile_phone,
       job_title,
       employment_status,
       u.department_id,
       contract_id,
       profile_picture_id,
       p.created_at
FROM "users" u
LEFT JOIN "profiles" p ON u."id" = p."id";
