name: PR Validation

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - 'main'
      - 'staging'

env:
  NODE_VERSION: '20'

jobs:
  # Lightweight validation for fast feedback
  build-validation:
    name: Build Validation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: |
          echo "Running ESLint checks..."
          npm run lint
          echo "ESLint completed"

      - name: Check TypeScript compilation
        run: |
          echo "Running TypeScript compiler check..."
          npx tsc --noEmit
          echo "TypeScript check completed"

      - name: Generate Prisma client
        run: |
          echo "Generating Prisma client..."
          npx prisma generate
          echo "Prisma client generated"

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma client
        run: |
          echo "Generating Prisma client..."
          npx prisma generate
          echo "Prisma client generated"

      - name: Run unit tests
        run: |
          echo "Running unit tests..."
          npm run test -- --ci --watchAll=false
          echo "Unit tests completed"

  # PR Status Notification
  notify:
    name: PR Status Notification
    runs-on: ubuntu-latest
    needs: [build-validation, unit-tests]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: AI Code Review Notice
        run: |
          echo "🤖 AI Code Review will be handled by CodeRabbit"
          echo "CodeRabbit will automatically analyze this PR and provide comments"
          echo "No GitHub Actions time wasted on simulation - real AI review in progress!"

      - name: Check for CodeRabbit comments
        id: coderabbit-check
        run: |
          # Check if CodeRabbit has already commented on this PR
          COMMENTS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.pull_request.number }}/comments" | \
            jq -r '.[] | select(.user.login == "coderabbitai[bot]") | .id' | wc -l)
          
          if [[ $COMMENTS -gt 0 ]]; then
            echo "coderabbit-status=commented" >> $GITHUB_OUTPUT
            echo "ai-review-message=CodeRabbit has already reviewed and commented on this PR" >> $GITHUB_OUTPUT
          else
            echo "coderabbit-status=pending" >> $GITHUB_OUTPUT
            echo "ai-review-message=CodeRabbit AI review is starting to analyze your code" >> $GITHUB_OUTPUT
          fi

      - name: Determine PR status
        id: status
        run: |
          BUILD_STATUS="${{ needs.build-validation.result }}"
          TEST_STATUS="${{ needs.unit-tests.result }}"
          
          # Get detailed job results
          if [[ "$BUILD_STATUS" == "success" ]]; then
            BUILD_MESSAGE="✅ Build validation passed - ESLint, TypeScript, and Prisma checks completed"
          else
            BUILD_MESSAGE="❌ Build validation failed - Check ESLint, TypeScript, or Prisma issues"
          fi
          
          if [[ "$TEST_STATUS" == "success" ]]; then
            TEST_MESSAGE="✅ Unit tests passed - All test suites completed successfully"
          else
            TEST_MESSAGE="❌ Unit tests failed - Some tests are failing"
          fi

          if [[ "$BUILD_STATUS" == "success" && "$TEST_STATUS" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "overall-message=🚀 PR validation passed! All checks completed successfully." >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "overall-message=❌ PR validation failed. Please fix issues before proceeding." >> $GITHUB_OUTPUT
          fi
          
          echo "build-message=$BUILD_MESSAGE" >> $GITHUB_OUTPUT
          echo "test-message=$TEST_MESSAGE" >> $GITHUB_OUTPUT

      - name: Send PR status notification
        run: |
          STATUS="${{ steps.status.outputs.status }}"
          if [[ "$STATUS" == "success" ]]; then
            STATUS_EMOJI="✅"
            STATUS_COLOR="good"
          else
            STATUS_EMOJI="❌"
            STATUS_COLOR="danger"
          fi
          
          COMMIT_SHORT="${{ github.event.pull_request.head.sha }}"
          COMMIT_SHORT="${COMMIT_SHORT:0:7}"
          
          # Escape quotes in PR title and messages
          PR_TITLE=$(echo '${{ github.event.pull_request.title }}' | sed 's/"/\\"/g')
          OVERALL_MESSAGE=$(echo '${{ steps.status.outputs.overall-message }}' | sed 's/"/\\"/g')
          BUILD_MESSAGE=$(echo '${{ steps.status.outputs.build-message }}' | sed 's/"/\\"/g')
          TEST_MESSAGE=$(echo '${{ steps.status.outputs.test-message }}' | sed 's/"/\\"/g')
          AI_REVIEW_MESSAGE=$(echo '${{ steps.coderabbit-check.outputs.ai-review-message }}' | sed 's/"/\\"/g')
          
          PAYLOAD=$(cat <<EOF
          {
            "text": "${STATUS_EMOJI} PR Validation - ${{ github.head_ref }}",
            "attachments": [
              {
                "color": "${STATUS_COLOR}",
                "blocks": [
                  {
                    "type": "header",
                    "text": {
                      "type": "plain_text",
                      "text": "${STATUS_EMOJI} PR Validation Results",
                      "emoji": true
                    }
                  },
                  {
                    "type": "section",
                    "fields": [
                      {"type": "mrkdwn", "text": "*PR:* #${{ github.event.pull_request.number }}"},
                      {"type": "mrkdwn", "text": "*Branch:* ${{ github.head_ref }}"},
                      {"type": "mrkdwn", "text": "*Target:* ${{ github.base_ref }}"},
                      {"type": "mrkdwn", "text": "*Author:* ${{ github.event.pull_request.user.login }}"},
                      {"type": "mrkdwn", "text": "*Commit:* ${COMMIT_SHORT}"},
                      {"type": "mrkdwn", "text": "*Status:* ${STATUS_EMOJI} ${STATUS}"}
                    ]
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*PR Title:* ${PR_TITLE}\\n\\n*Overall:* ${OVERALL_MESSAGE}"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Build Validation:* ${{ needs.build-validation.result }}\\n${BUILD_MESSAGE}\\n\\n*Unit Tests:* ${{ needs.unit-tests.result }}\\n${TEST_MESSAGE}"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "🤖 *AI Review:* ${{ steps.coderabbit-check.outputs.coderabbit-status }}\\n${AI_REVIEW_MESSAGE}"
                    }
                  },
                  {
                    "type": "actions",
                    "elements": [
                      {
                        "type": "button",
                        "text": {"type": "plain_text", "text": "View PR", "emoji": true},
                        "url": "${{ github.event.pull_request.html_url }}",
                        "style": "primary"
                      },
                      {
                        "type": "button",
                        "text": {"type": "plain_text", "text": "View Workflow", "emoji": true},
                        "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                      }
                    ]
                  },
                  {
                    "type": "context",
                    "elements": [
                      {
                        "type": "mrkdwn",
                        "text": "Environment: development | Validated at $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
                      }
                    ]
                  }
                ]
              }
            ]
          }
          EOF
          )
          
          curl -X POST -H 'Content-type: application/json' \
            --data "$PAYLOAD" \
            "${{ secrets.SLACK_WEBHOOK_URL }}" || echo "⚠️ Slack notification failed but continuing"
