name: 'Unified Error Handler'
description: 'Comprehensive error handling with classification, reporting, and notification integration'

inputs:
  stage:
    description: 'Pipeline stage where error occurred'
    required: true

  error-type:
    description: 'Type of error (build, test, deploy, quality, security, etc.)'
    required: true

  error-message:
    description: 'Detailed error message or description'
    required: false
    default: ''

  error-details:
    description: 'Additional error details and context'
    required: false
    default: ''

  logs-path:
    description: 'Path to relevant log files'
    required: false
    default: ''

  context:
    description: 'Additional context as JSON'
    required: false
    default: '{}'

  # Notification settings
  slack-webhook-url:
    description: 'Slack webhook URL for notifications'
    required: false
    default: ''

  thread-ts:
    description: 'Slack thread timestamp for threading'
    required: false
    default: ''

  mention-users:
    description: 'Users to mention for critical failures'
    required: false
    default: ''

  # Issue creation settings
  create-issue:
    description: 'Whether to create a GitHub issue for critical errors'
    required: false
    default: 'false'

  # Recovery settings
  enable-recovery:
    description: 'Enable automatic recovery suggestions'
    required: false
    default: 'true'

  max-retries:
    description: 'Maximum number of retry attempts suggested'
    required: false
    default: '3'

outputs:
  error-id:
    description: 'Unique error identifier'
    value: ${{ steps.process.outputs.error-id }}

  severity:
    description: 'Determined error severity level'
    value: ${{ steps.process.outputs.severity }}

  escalation-level:
    description: 'Escalation level for notifications'
    value: ${{ steps.process.outputs.escalation-level }}

  actionable-steps:
    description: 'Suggested remediation steps'
    value: ${{ steps.process.outputs.actionable-steps }}

  recovery-commands:
    description: 'Suggested recovery commands'
    value: ${{ steps.process.outputs.recovery-commands }}

  issue-url:
    description: 'URL of created GitHub issue (if applicable)'
    value: ${{ steps.issue.outputs.issue-url }}

  notification-sent:
    description: 'Whether notification was sent successfully'
    value: ${{ steps.notify.outputs.success }}

  thread-ts:
    description: 'Slack thread timestamp'
    value: ${{ steps.notify.outputs.thread-ts }}

runs:
  using: 'composite'
  steps:
    - name: Process error and classify severity
      id: process
      shell: bash
      run: |
        set -e

        echo "🔍 Processing error and determining severity..."

        # Generate unique error ID
        ERROR_ID="error-$(date +%Y%m%d-%H%M%S)-$(echo $RANDOM | md5sum | head -c 8)"
        echo "error-id=$ERROR_ID" >> $GITHUB_OUTPUT

        # Extract inputs
        STAGE="${{ inputs.stage }}"
        ERROR_TYPE="${{ inputs.error-type }}"
        ERROR_MESSAGE="${{ inputs.error-message }}"
        ERROR_DETAILS="${{ inputs.error-details }}"

        # Determine error severity based on type and stage
        case "$ERROR_TYPE" in
          "security"|"vulnerability")
            SEVERITY="critical"
            ESCALATION="critical"
            ;;
          "deploy"|"deployment")
            SEVERITY="critical"
            ESCALATION="high"
            ;;
          "build"|"docker")
            SEVERITY="high"
            ESCALATION="high"
            ;;
          "e2e-tests"|"integration")
            SEVERITY="high"
            ESCALATION="medium"
            ;;
          "unit-tests"|"test")
            SEVERITY="medium"
            ESCALATION="medium"
            ;;
          "quality"|"lint"|"format")
            SEVERITY="medium"
            ESCALATION="low"
            ;;
          *)
            SEVERITY="low"
            ESCALATION="low"
            ;;
        esac

        # Adjust severity based on stage context
        case "$STAGE" in
          "production"*|"prod"*)
            # Escalate production issues
            if [[ "$SEVERITY" == "medium" ]]; then
              SEVERITY="high"
              ESCALATION="high"
            elif [[ "$SEVERITY" == "high" ]]; then
              SEVERITY="critical"
              ESCALATION="critical"
            fi
            ;;
          "staging"*)
            # Moderate escalation for staging
            if [[ "$ESCALATION" == "low" ]]; then
              ESCALATION="medium"
            fi
            ;;
        esac

        echo "severity=$SEVERITY" >> $GITHUB_OUTPUT
        echo "escalation-level=$ESCALATION" >> $GITHUB_OUTPUT

        # Generate stage and error-type specific actionable steps
        case "$ERROR_TYPE" in
          "build"|"docker")
            ACTIONABLE_STEPS="1. Check Dockerfile syntax and build context|2. Verify all COPY paths exist in repository|3. Ensure base images are accessible|4. Review dependency installation logs|5. Validate build arguments and environment variables|6. Check for disk space and memory limits"
            RECOVERY_COMMANDS="docker build --no-cache .|npm run build|docker system prune -f"
            ;;
          "unit-tests"|"test")
            ACTIONABLE_STEPS="1. Run tests locally to reproduce failures|2. Check test environment setup and dependencies|3. Verify database connections and test data|4. Review recent code changes affecting tests|5. Check Jest/test configuration files|6. Clear test cache and retry"
            RECOVERY_COMMANDS="npm test|npm run test:clear-cache|npm ci && npm test"
            ;;
          "e2e-tests"|"integration")
            ACTIONABLE_STEPS="1. Run E2E tests locally with test database|2. Check service dependencies (PostgreSQL, Redis)|3. Verify API endpoints and authentication|4. Review E2E test configuration|5. Check for timing issues or race conditions|6. Validate test data setup"
            RECOVERY_COMMANDS="npm run test:e2e|docker-compose up -d && npm run test:e2e|npm run test:e2e:reset"
            ;;
          "deploy"|"deployment")
            ACTIONABLE_STEPS="1. Check Cloud Run service configuration|2. Verify service account permissions|3. Review deployment logs in Google Cloud Console|4. Check environment variables and secrets|5. Validate health check endpoints|6. Verify VPC and network configuration"
            RECOVERY_COMMANDS="gcloud run services describe SERVICE_NAME|gcloud run deploy --source .|kubectl rollout restart deployment"
            ;;
          "quality"|"lint"|"format")
            ACTIONABLE_STEPS="1. Run linting tools locally to identify issues|2. Use auto-fix commands where available|3. Check linting configuration files|4. Verify TypeScript compilation|5. Format code according to project standards|6. Review code style guidelines"
            RECOVERY_COMMANDS="npm run lint:fix|npm run format|npx prettier --write .|npx eslint --fix ."
            ;;
          "security"|"vulnerability")
            ACTIONABLE_STEPS="1. Review security audit results in detail|2. Update vulnerable dependencies immediately|3. Check for exposed secrets or credentials|4. Review code for security best practices|5. Validate access controls and permissions|6. Run additional security scans"
            RECOVERY_COMMANDS="npm audit fix|npm audit fix --force|snyk test|npm update"
            ;;
          *)
            ACTIONABLE_STEPS="1. Review error logs and messages carefully|2. Check recent code changes and commits|3. Verify environment configuration|4. Consult documentation and troubleshooting guides|5. Test locally to reproduce the issue|6. Contact team for assistance if needed"
            RECOVERY_COMMANDS="git log --oneline -10|git diff HEAD~1|npm run dev"
            ;;
        esac

        echo "actionable-steps=$ACTIONABLE_STEPS" >> $GITHUB_OUTPUT
        echo "recovery-commands=$RECOVERY_COMMANDS" >> $GITHUB_OUTPUT

        # Display error summary
        echo ""
        echo "🚨 ERROR SUMMARY"
        echo "=================="
        echo "ID: $ERROR_ID"
        echo "Stage: $STAGE"
        echo "Type: $ERROR_TYPE"
        echo "Severity: $SEVERITY"
        echo "Escalation: $ESCALATION"
        echo "Time: $(date -u +\"%Y-%m-%d %H:%M:%S UTC\")"
        echo ""
        echo "🔧 IMMEDIATE ACTIONS:"
        echo "$ACTIONABLE_STEPS" | sed 's/|/\n/g' | sed 's/^/  /'
        echo ""
        if [[ "${{ inputs.enable-recovery }}" == "true" ]]; then
          echo "💡 RECOVERY COMMANDS:"
          echo "$RECOVERY_COMMANDS" | sed 's/|/\n/g' | sed 's/^/  /'
          echo ""
        fi

    - name: Generate comprehensive error report
      shell: bash
      run: |
        ERROR_ID="${{ steps.process.outputs.error-id }}"
        SEVERITY="${{ steps.process.outputs.severity }}"
        ESCALATION="${{ steps.process.outputs.escalation-level }}"

        echo "📋 Generating comprehensive error report..."

        # Create detailed error report
        cat << EOF > error-report-$ERROR_ID.md
        # Error Report: $ERROR_ID

        ## Summary
        - **Error ID:** $ERROR_ID
        - **Stage:** ${{ inputs.stage }}
        - **Type:** ${{ inputs.error-type }}
        - **Severity:** $SEVERITY
        - **Escalation Level:** $ESCALATION
        - **Timestamp:** $(date -u +\"%Y-%m-%d %H:%M:%S UTC\")
        - **Workflow:** ${{ github.workflow }}
        - **Run ID:** ${{ github.run_id }}
        - **Branch:** ${{ github.ref_name }}
        - **Commit:** ${{ github.sha }}
        - **Actor:** ${{ github.actor }}

        ## Error Details
        ### Primary Error Message
        \`\`\`
        ${{ inputs.error-message }}
        \`\`\`

        ### Additional Details
        \`\`\`
        ${{ inputs.error-details }}
        \`\`\`

        ### Context Information
        \`\`\`json
        ${{ inputs.context }}
        \`\`\`

        ## Actionable Steps
        $(echo "${{ steps.process.outputs.actionable-steps }}" | sed 's/|/\n/g')

        ## Recovery Commands
        $(echo "${{ steps.process.outputs.recovery-commands }}" | sed 's/|/\n/g' | sed 's/^/\`\`\`bash\n/' | sed 's/$/\n\`\`\`/')

        ## Resources
        - [Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
        - [Commit Details](https://github.com/${{ github.repository }}/commit/${{ github.sha }})
        - [Repository](https://github.com/${{ github.repository }})
        - [Branch Comparison](https://github.com/${{ github.repository }}/compare/${{ github.ref_name }})

        ## Troubleshooting Guide
        ### Immediate Actions
        1. **Review the error logs** in the workflow run
        2. **Check recent changes** that might have introduced the issue
        3. **Test locally** to reproduce the problem
        4. **Verify environment** configuration and dependencies

        ### Environment-Specific Checks
        - **Development:** Ensure local environment matches CI configuration
        - **Staging:** Verify staging-specific configurations and data
        - **Production:** Check production environment variables and secrets

        ### Getting Help
        - Review project documentation and README
        - Check similar issues in the repository
        - Consult team knowledge base and runbooks
        - Contact the development team or on-call engineer

        ### Prevention
        - Add tests to prevent similar issues
        - Update documentation with lessons learned
        - Consider adding monitoring or alerts
        - Review and improve CI/CD pipeline robustness
        EOF

        echo "✅ Error report generated: error-report-$ERROR_ID.md"

    - name: Upload error report and artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: error-report-${{ steps.process.outputs.error-id }}
        path: |
          error-report-${{ steps.process.outputs.error-id }}.md
          ${{ inputs.logs-path }}
        retention-days: 30

    - name: Create GitHub issue for critical errors
      id: issue
      if: inputs.create-issue == 'true' && (steps.process.outputs.severity == 'critical' || steps.process.outputs.escalation-level == 'critical')
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const errorId = '${{ steps.process.outputs.error-id }}';
          const reportPath = `error-report-${errorId}.md`;

          let reportContent = '';
          try {
            reportContent = fs.readFileSync(reportPath, 'utf8');
          } catch (error) {
            reportContent = 'Error report file not found';
          }

          const issue = await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: `🚨 Critical Pipeline Error: ${{ inputs.stage }} - ${{ inputs.error-type }}`,
            body: reportContent,
            labels: [
              'bug', 
              'ci-cd', 
              '${{ steps.process.outputs.severity }}', 
              '${{ inputs.error-type }}',
              'auto-generated'
            ],
            assignees: ['${{ github.actor }}']
          });

          console.log(`Created issue: ${issue.data.html_url}`);
          core.setOutput('issue-url', issue.data.html_url);

          return issue.data.html_url;

    - name: Send unified notification
      id: notify
      if: inputs.slack-webhook-url != ''
      uses: ./.github/actions/unified-slack-notify
      with:
        webhook-url: ${{ inputs.slack-webhook-url }}
        notification-type: 'failure-escalation'
        status: 'failure'
        thread-ts: ${{ inputs.thread-ts }}
        severity: ${{ steps.process.outputs.escalation-level }}
        mentions: ${{ inputs.mention-users }}
        data: |
          {
            "errorId": "${{ steps.process.outputs.error-id }}",
            "stage": "${{ inputs.stage }}",
            "errorType": "${{ inputs.error-type }}",
            "severity": "${{ steps.process.outputs.severity }}",
            "escalationLevel": "${{ steps.process.outputs.escalation-level }}",
            "errorMessage": "${{ inputs.error-message }}",
            "errorDetails": "${{ inputs.error-details }}",
            "actionableSteps": "${{ steps.process.outputs.actionable-steps }}",
            "recoveryCommands": "${{ steps.process.outputs.recovery-commands }}",
            "issueUrl": "${{ steps.issue.outputs.issue-url }}",
            "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
            "branch": "${{ github.ref_name }}",
            "commit": "${{ github.sha }}",
            "author": "${{ github.actor }}",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
          }

    - name: Set error context for downstream jobs
      shell: bash
      run: |
        echo "💾 Setting error context for downstream jobs..."

        # Create error context file for other jobs to consume
        cat << EOF > pipeline-error-context.json
        {
          "errorId": "${{ steps.process.outputs.error-id }}",
          "stage": "${{ inputs.stage }}",
          "type": "${{ inputs.error-type }}",
          "severity": "${{ steps.process.outputs.severity }}",
          "escalationLevel": "${{ steps.process.outputs.escalation-level }}",
          "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "workflow": "${{ github.workflow }}",
          "runId": "${{ github.run_id }}",
          "branch": "${{ github.ref_name }}",
          "commit": "${{ github.sha }}",
          "actor": "${{ github.actor }}",
          "actionableSteps": "${{ steps.process.outputs.actionable-steps }}",
          "recoveryCommands": "${{ steps.process.outputs.recovery-commands }}",
          "issueUrl": "${{ steps.issue.outputs.issue-url }}",
          "notificationSent": "${{ steps.notify.outputs.success }}",
          "threadTs": "${{ steps.notify.outputs.thread-ts }}"
        }
        EOF

        echo "✅ Error context saved for downstream jobs"

        # Also set as environment variable for current job
        echo "PIPELINE_ERROR_CONTEXT<<EOF" >> $GITHUB_ENV
        cat pipeline-error-context.json >> $GITHUB_ENV
        echo "EOF" >> $GITHUB_ENV

branding:
  icon: 'alert-triangle'
  color: 'red'
