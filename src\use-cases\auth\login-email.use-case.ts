import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { TokenManager } from '@/core/abstracts/token-manager';
import { UseCase } from '@/core/base/use-case';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import {
  InsufficientPermissionException,
  InvalidCredentialException,
} from '@/core/exceptions/opus-exceptions';
import { UsersRepository } from '@/core/repositories/users.repository';
import { EmailLoginInput } from '@/shared/dtos/auth/login-input.dto';
import { AuthTokensDto } from '@/shared/dtos/auth/token.dto';

export class LoginEmailUseCase implements UseCase<AuthTokensDto> {
  private readonly createdUserMapper: CreatedUserMapper;

  constructor(
    private readonly passwordHasher: PasswordHasher,
    private readonly usersRepository: UsersRepository,
    private readonly tokenManager: TokenManager,
  ) {
    this.createdUserMapper = new CreatedUserMapper();
  }

  public async execute(
    input: EmailLoginInput,
    timezone?: string,
  ): Promise<AuthTokensDto> {
    const { email, password } = input;
    const user = await this.usersRepository.findOne({ email });

    if (!user) {
      throw new InvalidCredentialException('User is not registered');
    }

    if (!user.activatedAt) {
      throw new InsufficientPermissionException('User is not activated');
    }

    if (!user.password) {
      throw new InvalidCredentialException('Incorrect password');
    }

    if (!(await this.passwordHasher.verifyHash(password, user.password))) {
      throw new InvalidCredentialException('Incorrect password');
    }

    if (timezone) {
      await this.usersRepository.update(user.id, {
        timezone,
      });
    }

    const tokens = await this.tokenManager.generateTokens(
      this.createdUserMapper.map(user),
    );
    return tokens;
  }
}
