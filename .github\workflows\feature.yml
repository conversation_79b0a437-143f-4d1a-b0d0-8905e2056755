name: Feature Branch CI

on:
  push:
    branches:
      - 'feature/**'
      - 'feat/**'
      - 'fix/**'
      - 'hotfix/**'
  pull_request:
    branches:
      - 'feature/**'
      - 'feat/**'
      - 'fix/**'
      - 'hotfix/**'

env:
  NODE_VERSION: '20'

jobs:
  # Fast feedback jobs for feature branches
  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint with detailed reporting
        run: |
          echo "Running ESLint checks..."
          npm run lint -- --format=json --output-file=eslint-results.json || true
          npm run lint
          echo "ESLint completed"

      - name: Check Prettier formatting
        run: |
          echo "Checking Prettier formatting..."
          npx prettier --check "src/**/*.ts" --write=false
          echo "Prettier formatting check completed"

      - name: Run TypeScript compiler check
        run: |
          echo "Running TypeScript compiler check..."
          npx tsc --noEmit
          echo "TypeScript check completed"

      - name: Run security audit
        run: |
          echo "Running npm security audit..."
          npm audit --audit-level=high --json > audit-results.json || true
          # Allow moderate vulnerabilities for now, focus on high/critical
          npm audit --audit-level=critical || echo "Security audit found issues but continuing..."
          echo "Security audit completed"

      - name: Run additional security scanning with Snyk
        continue-on-error: true
        run: |
          echo "Running Snyk security scan..."
          npx snyk test --json > snyk-results.json || true
          echo "Snyk scan completed (non-blocking)"

      - name: Upload code quality artifacts
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: feature-code-quality-results
          path: |
            eslint-results.json
            audit-results.json
            snyk-results.json
          retention-days: 7

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    outputs:
      coverage-percentage: ${{ steps.coverage.outputs.percentage }}
      test-results: ${{ steps.test.outputs.results }}
      performance-data: ${{ steps.performance.outputs.performance-data }}
      duration: ${{ steps.performance.outputs.duration-formatted }}
    steps:
      - name: Record job start time
        id: start-time
        run: echo "start=$(date +%s)" >> $GITHUB_OUTPUT

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Optimize npm cache
        id: npm-cache
        uses: ./.github/actions/cache-optimizer
        with:
          cache-type: 'npm'
          cache-key: npm-${{ runner.os }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            npm-${{ runner.os }}-
            npm-
          paths: |
            ~/.npm
            node_modules

      - name: Install dependencies
        run: |
          if [ "${{ steps.npm-cache.outputs.cache-hit }}" != "true" ]; then
            echo "Cache miss - installing dependencies"
            npm ci
          else
            echo "Cache hit - dependencies already available"
            echo "Time saved: ${{ steps.npm-cache.outputs.time-saved }}"
          fi

      - name: Generate Prisma client
        run: |
          echo "Generating Prisma client..."
          npx prisma generate
          echo "Prisma client generated"

      - name: Run unit tests with coverage
        id: test
        run: |
          echo "Running unit tests with coverage..."
          npm run test:cov -- --ci --coverage --watchAll=false --coverageReporters=text-lcov --coverageReporters=json-summary --coverageReporters=html --coverageReporters=text

          # Extract test results summary
          if [ -f "junit.xml" ]; then
            TESTS_TOTAL=$(grep -o 'tests="[0-9]*"' junit.xml | grep -o '[0-9]*' | head -1)
            TESTS_FAILED=$(grep -o 'failures="[0-9]*"' junit.xml | grep -o '[0-9]*' | head -1)
            TESTS_PASSED=$((TESTS_TOTAL - TESTS_FAILED))
            echo "results=Total: $TESTS_TOTAL, Passed: $TESTS_PASSED, Failed: $TESTS_FAILED" >> $GITHUB_OUTPUT
          else
            echo "results=Test results not available" >> $GITHUB_OUTPUT
          fi

          echo "Unit tests completed"

      - name: Parse and validate coverage
        id: coverage
        run: |
          echo "Parsing coverage results..."

          if [ -f "./coverage/coverage-summary.json" ]; then
            COVERAGE_DATA=$(cat ./coverage/coverage-summary.json)
            
            LINES_PCT=$(echo $COVERAGE_DATA | node -e "
              const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
              console.log(data.total.lines.pct);
            ")
            
            BRANCHES_PCT=$(echo $COVERAGE_DATA | node -e "
              const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
              console.log(data.total.branches.pct);
            ")
            
            FUNCTIONS_PCT=$(echo $COVERAGE_DATA | node -e "
              const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
              console.log(data.total.functions.pct);
            ")
            
            STATEMENTS_PCT=$(echo $COVERAGE_DATA | node -e "
              const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
              console.log(data.total.statements.pct);
            ")
            
            echo "percentage=$LINES_PCT" >> $GITHUB_OUTPUT
            echo "Coverage Summary:"
            echo "- Lines: $LINES_PCT%"
            echo "- Branches: $BRANCHES_PCT%"
            echo "- Functions: $FUNCTIONS_PCT%"
            echo "- Statements: $STATEMENTS_PCT%"
            
            # Check coverage threshold (adjusted for unit tests)
            if (( $(echo "$LINES_PCT < 10" | bc -l) )); then
              echo "❌ Coverage $LINES_PCT% is below 10% threshold"
              exit 1
            else
              echo "✅ Coverage $LINES_PCT% meets 10% threshold"
            fi
          else
            echo "❌ Coverage summary file not found"
            exit 1
          fi

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
          verbose: true

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: feature-test-results
          path: |
            coverage/
            junit.xml
            test-results.xml
          retention-days: 7

      - name: Track performance metrics
        id: performance
        if: always()
        uses: ./.github/actions/performance-monitor
        with:
          stage: 'unit-tests'
          start-time: ${{ steps.start-time.outputs.start }}
          status: ${{ job.status }}
          metadata: |
            {
              "testStatus": "${{ steps.test.outcome }}",
              "coverage": "${{ steps.coverage.outputs.percentage }}",
              "testResults": "${{ steps.test.outputs.results }}",
              "cacheHit": "${{ steps.npm-cache.outputs.cache-hit }}",
              "timeSaved": "${{ steps.npm-cache.outputs.time-saved }}",
              "branch": "${{ github.ref_name }}"
            }

  # Lightweight notification for feature branches using the new notification system
  notify:
    name: Slack Notification
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests]
    if: always() && vars.SLACK_WEBHOOK_URL != ''
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Determine status
        id: status
        run: |
          if [[ "${{ needs.code-quality.result }}" == "failure" || "${{ needs.unit-tests.result }}" == "failure" ]]; then
            echo "status=failure" >> $GITHUB_OUTPUT
          else
            echo "status=success" >> $GITHUB_OUTPUT
          fi

      - name: Prepare commit message
        id: commit-message
        run: |
          # Escape and sanitize commit message for JSON
          COMMIT_MSG="${{ github.event.head_commit.message || 'No commit message' }}"
          # Remove newlines and escape quotes
          COMMIT_MSG=$(echo "$COMMIT_MSG" | tr '\n' ' ' | sed 's/"/\\"/g')
          echo "message=$COMMIT_MSG" >> $GITHUB_OUTPUT

      - name: Send feature branch notification
        uses: ./.github/actions/unified-slack-notify
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: 'feature-branch'
          status: ${{ steps.status.outputs.status }}
          data: |
            {
              "branch": "${{ github.ref_name }}",
              "commit": "${{ github.sha }}",
              "author": "${{ github.actor }}",
              "message": "${{ steps.commit-message.outputs.message }}",
              "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "results": {
                "codeQuality": "${{ needs.code-quality.result }}",
                "unitTests": "${{ needs.unit-tests.result }}"
              }
            }

  # Performance metrics for optimization
  performance-metrics:
    name: Performance Metrics
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests]
    if: always()
    steps:
      - name: Calculate and report metrics
        run: |
          START_TIME="${{ github.event.head_commit.timestamp }}"
          END_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

          echo "Pipeline Performance Metrics:"
          echo "- Branch: ${{ github.ref_name }}"
          echo "- Commit: ${{ github.sha }}"
          echo "- Code Quality: ${{ needs.code-quality.result }}"
          echo "- Unit Tests: ${{ needs.unit-tests.result }}"
          echo "- Started: $START_TIME"
          echo "- Completed: $END_TIME"

          # Calculate duration (simplified)
          echo "- Status: Feature branch validation completed"

          # This provides visibility into pipeline performance for optimization
          if [[ "${{ needs.code-quality.result }}" == "success" && "${{ needs.unit-tests.result }}" == "success" ]]; then
            echo "✅ All checks passed - Ready for PR to staging/main"
          else
            echo "❌ Some checks failed - Please review and fix issues"
          fi
