import { FilesManager } from '@/core/abstracts/files-manager';
import { UseCase } from '@/core/base/use-case';
import { CreateFileMapper } from '@/core/domain/mappers/files/create-file.mapper';
import { CreatedFileMapper } from '@/core/domain/mappers/files/created-file.mapper';
import { FilesRepository } from '@/core/repositories/files.repository';
import { CreateFileInputDto } from '@/shared/dtos/files/create-file-input.dto';
import { FileMetaDto } from '@/shared/dtos/files/file-meta.dto';

export class UpdateOneFileUseCase implements UseCase<FileMetaDto> {
  private readonly createFileMapper: CreateFileMapper;
  private readonly createdFileMapper: CreatedFileMapper;

  constructor(
    private readonly filesRepository: FilesRepository,
    private readonly filesManager: FilesManager,
  ) {
    this.createFileMapper = new CreateFileMapper();
    this.createdFileMapper = new CreatedFileMapper();
  }

  public async execute(data: CreateFileInputDto): Promise<FileMetaDto> {
    const modifiedFileName = await this.filesManager.upload(
      data.fileName,
      data.content,
    );

    const file = this.createFileMapper.map({
      ...data,
      fileName: modifiedFileName,
      originalFileName: data.fileName,
    });

    const createdFile = await this.filesRepository.create(file);
    return this.createdFileMapper.map(createdFile);
  }
}
