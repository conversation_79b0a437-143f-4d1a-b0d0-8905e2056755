import { EmailSender } from '@/core/abstracts/email-sender';
import { OpusRoles } from '@/core/enums/role.enum';
import {
  EntityConflictException,
  EntityNotFoundException,
  InsufficientPermissionException,
} from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreateStaffInputDto } from '@/shared/dtos/profiles/create-staff-input.dto';
import { PaginatedCreatedProfileDto } from '@/shared/dtos/profiles/created-profile-paginated.dto';
import { CreatedProfileDto } from '@/shared/dtos/profiles/created-profile.dto';
import { FindProfileFilterDto } from '@/shared/dtos/profiles/find-profile-filter.dto';
import { FindProfileSortDto } from '@/shared/dtos/profiles/find-profile-sort.dto';
import { UpdateProfileInputDto } from '@/shared/dtos/profiles/update-profile-input.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { AuthService } from '../auth/auth.service';
import { MailBuilderService } from '../mailer/mail-builder.service';
import { profileMapper } from './profile.mapper';

@Injectable()
export class ProfileService {
  static PROFILE_INCLUDE = {
    department: {
      include: {
        headUser: {
          select: {
            user: {
              select: { id: true, firstName: true, lastName: true },
            },
          },
        },
      },
    },
    profilePicture: true,
  } satisfies Prisma.ProfileInfoInclude;

  constructor(
    private readonly prisma: PrismaService,
    private readonly authService: AuthService,
    private readonly mailBuilderService: MailBuilderService,
    private readonly emailSender: EmailSender,
  ) {}
  async createStaff(
    creator: CreatedUserDetailsDto,
    staff: CreateStaffInputDto,
  ): Promise<CreatedProfileDto> {
    if (creator.role.title !== OpusRoles.Admin) {
      throw new InsufficientPermissionException(
        'User not allowed to create profiles',
      );
    }

    if (!creator.account?.id) {
      throw new InsufficientPermissionException(
        'Account has not been initialized',
      );
    }

    const { profile: createData, leaveCredits } = staff;
    try {
      const createdProfile = await this.prisma.$transaction(async (tx) => {
        const {
          firstName,
          lastName,
          workEmail,
          createdAt: joinDate,
          ...rest
        } = createData;
        const user = await tx.user.create({
          data: {
            email: createData.workEmail,
            firstName: createData.firstName,
            lastName: createData.lastName,
            userStatus: { create: {} },
            profile: {
              create: {
                ...rest,
                joinDate: new Date(joinDate),
              },
            },
            role: { connect: { title: OpusRoles.Staff } },
            account: { connect: { id: creator.account?.id } },
          },
          select: {
            id: true,
            profileInfo: { include: ProfileService.PROFILE_INCLUDE },
          },
        });

        for (const leaveCredit of leaveCredits) {
          await tx.timeAway.create({
            data: {
              hours: [Math.abs(leaveCredit.hours)],
              isSubtract: leaveCredit.hours < 0,
              startDate: new Date(),
              endDate: new Date(),
              userId: user.id,
              timeAwayTypeId: leaveCredit.timeAwayTypeId,
              isApproved: true,
              isUserRequest: false,
              approvalMessage: 'Initial balance on Opus Account Creation',
            },
          });
        }

        return user.profileInfo!;
      });

      const activationToken = await this.authService.generateActivationToken(
        createdProfile.id,
      );

      const mailData = await this.mailBuilderService.buildUserActivationEmail(
        createdProfile.workEmail,
        createdProfile.firstName,
        createdProfile.lastName,
        creator.firstName,
        creator.lastName,
        creator.email,
        creator.account.accountName,
        activationToken,
      );
      void this.emailSender.send(mailData);

      return profileMapper(createdProfile);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Email is already used');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }

      throw error;
    }
  }

  async paginateProfiles(
    input: Omit<Prisma.ProfileInfoFindManyArgs, 'skip' | 'take' | 'include'>,
    pagination?: PaginationMetaDto,
  ) {
    return this.prisma.$transaction(async (tx) => {
      const data = await tx.profileInfo.findMany({
        ...input,
        include: ProfileService.PROFILE_INCLUDE,
        skip: pagination ? (pagination.page - 1) * pagination.limit : undefined,
        take: pagination ? pagination.limit : undefined,
      });
      const count = await tx.profileInfo.count({ where: input.where });

      return { data: data, count };
    });
  }

  async findOneProfile(
    requester: CreatedUserDetailsDto,
    filter: FindProfileFilterDto,
  ): Promise<CreatedProfileDto> {
    if (requester.role.title != OpusRoles.Admin) {
      filter.isActivated = true;
    }
    const profile = await this.prisma.profileInfo.findFirst({
      where: this.__processFilter(filter),
      include: ProfileService.PROFILE_INCLUDE,
    });

    if (!profile) {
      throw new EntityNotFoundException('Profile not found');
    }
    return profileMapper(profile);
  }

  async findProfiles(
    requester: CreatedUserDetailsDto,
    filter: FindProfileFilterDto,
    paginationMeta: PaginationMetaDto,
    sort?: FindProfileSortDto,
  ): Promise<PaginatedCreatedProfileDto> {
    if (requester.role.title != OpusRoles.Admin) {
      filter.isActivated = true;
    }

    const { data, count } = await this.paginateProfiles(
      {
        where: this.__processFilter(filter),
        orderBy: sort,
      },
      paginationMeta,
    );

    return {
      data: data.map(profileMapper),
      paging: getPaginationDetails(data, paginationMeta, count),
    };
  }

  async updateProfile(
    id: CreatedProfileDto['id'],
    input: UpdateProfileInputDto,
    updater: CreatedUserDetailsDto,
  ): Promise<CreatedProfileDto | null> {
    if (updater.id !== id) {
      const user = await this.prisma.profileInfo.findUnique({ where: { id } });

      if (updater.accountId !== user?.accountId) {
        throw new EntityNotFoundException('Profile does not exist');
      }
      if (updater.role.title !== OpusRoles.Admin) {
        throw new InsufficientPermissionException(
          'Insufficient permissions to edit profile ',
        );
      }
    }

    const {
      lastName,
      firstName,
      birthDate,
      workEmail,
      createdAt,
      profilePictureId,
      ...rest
    } = input.profile;

    const user = await this.prisma.user.update({
      where: { id },
      data: {
        firstName,
        lastName,
        email: workEmail,
        profilePictureId,
        profile: {
          update: {
            ...rest,
            birthDate: birthDate ? new Date(birthDate) : undefined,
            joinDate: createdAt ? new Date(createdAt) : undefined,
          },
        },
      },
      select: { profileInfo: { include: ProfileService.PROFILE_INCLUDE } },
    });

    return profileMapper(user.profileInfo!);
  }

  private __processFilter(filter: FindProfileFilterDto) {
    const { nameOrEmail, accountId, isActivated, ...rest } = filter;

    const where: Prisma.ProfileInfoWhereInput = {
      ...rest,
      activatedAt: isActivated
        ? { not: null }
        : isActivated === false
          ? null
          : undefined,
    };

    if (nameOrEmail) {
      where.OR = [
        { workEmail: { contains: nameOrEmail, mode: 'insensitive' } },
        { firstName: { contains: nameOrEmail, mode: 'insensitive' } },
        { lastName: { contains: nameOrEmail, mode: 'insensitive' } },
        { personalEmail: { contains: nameOrEmail, mode: 'insensitive' } },
      ];
    }
    return where;
  }

  async resendInvite(
    requester: CreatedUserDetailsDto,
    profileId: string,
  ): Promise<void> {
    if (requester.role.title != OpusRoles.Admin) {
      throw new InsufficientPermissionException();
    }

    const profile = await this.findOneProfile(requester, {
      id: profileId,
    });

    if (profile.activatedAt !== null) {
      throw new EntityNotFoundException('Profile is already activated');
    }

    const activationToken = await this.authService.generateActivationToken(
      profile.id,
    );

    const mailData = await this.mailBuilderService.buildUserActivationEmail(
      profile.workEmail,
      profile.firstName,
      profile.lastName,
      requester.firstName,
      requester.lastName,
      requester.email,
      requester.account?.accountName ?? '',
      activationToken,
    );

    void this.emailSender.send(mailData);
    return;
  }
}
