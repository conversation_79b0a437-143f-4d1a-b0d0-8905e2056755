import { Entity } from '@/core/base/entity';
import { UserStatus } from '@prisma/client';
import { AccountEntity } from './account.entity';
import type { FileEntity } from './file.entity';
import type { RoleEntity } from './role.entity';

export class UserEntity extends Entity {
  id!: string;
  email!: string;
  firstName!: string;
  lastName!: string;
  timezone!: string;
  password!: string | null;

  accountId!: string | null;
  roleId!: string;
  profilePictureId!: string | null;

  googleRefreshToken!: string | null;

  account?: AccountEntity | null;
  role?: RoleEntity;
  profilePicture?: Omit<FileEntity, 'uploader'> | null;
  uploadedFiles?: Omit<FileEntity, 'uploader'>[];
  userStatus?: UserStatus | null;

  lastPasswordChange!: Date | null;
  activatedAt!: Date | null;
}
