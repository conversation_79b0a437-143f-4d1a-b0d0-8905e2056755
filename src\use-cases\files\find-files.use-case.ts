import { UseCase } from '@/core/base/use-case';
import { CreatedFileMapper } from '@/core/domain/mappers/files/created-file.mapper';
import { FilesRepository } from '@/core/repositories/files.repository';
import { PaginatedFileMetaDto } from '@/shared/dtos/files/file-meta-paginated.dto';
import { FindFileFilterDto } from '@/shared/dtos/files/find-file-filter.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';

export class FindFilesUseCase implements UseCase<PaginatedFileMetaDto> {
  private readonly createdFileMapper: CreatedFileMapper;

  constructor(private readonly filesRepository: FilesRepository) {
    this.createdFileMapper = new CreatedFileMapper();
  }

  public async execute(
    filter: FindFileFilterDto,
    paginationMeta: PaginationMetaDto,
  ): Promise<PaginatedFileMetaDto> {
    const { data, count } = await this.filesRepository.findAll(
      filter,
      paginationMeta,
    );
    return {
      data: data.map(this.createdFileMapper.map),
      paging: getPaginationDetails(data, paginationMeta, count),
    };
  }
}
