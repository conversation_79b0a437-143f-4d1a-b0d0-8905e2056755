name: Slack Notifications

on:
  workflow_call:
    inputs:
      notification-type:
        description: 'Type of notification to send'
        required: true
        type: string

      status:
        description: 'Status of the operation'
        required: false
        type: string
        default: 'success'

      data:
        description: 'JSON data for the notification'
        required: false
        type: string
        default: '{}'

      thread-ts:
        description: 'Slack thread timestamp'
        required: false
        type: string

      severity:
        description: 'Failure severity level'
        required: false
        type: string
        default: 'medium'

    outputs:
      thread-ts:
        description: 'Slack thread timestamp for future replies'
        value: ${{ jobs.notify.outputs.thread-ts }}

      message-ts:
        description: 'Slack message timestamp'
        value: ${{ jobs.notify.outputs.message-ts }}

    secrets:
      SLACK_WEBHOOK_URL:
        required: true

      SLACK_MENTIONS_ON_FAILURE:
        required: false

jobs:
  notify:
    name: Send Slack Notification
    runs-on: ubuntu-latest
    outputs:
      thread-ts: ${{ steps.slack.outputs.thread-ts }}
      message-ts: ${{ steps.slack.outputs.message-ts }}
      status: ${{ steps.slack.outputs.status }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send Slack notification
        id: slack
        uses: ./.github/actions/unified-slack-notify
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: ${{ inputs.notification-type }}
          channel: '#devops-alerts'
          branch: ${{ github.ref_name }}
          commit: ${{ github.sha }}
          author: ${{ github.actor }}
          environment: 'production'
          status: ${{ inputs.status }}
          severity: ${{ inputs.severity }}
          workflow-url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          mentions: ${{ secrets.SLACK_MENTIONS_ON_FAILURE }}
          data: ${{ inputs.data }}
          thread-ts: ${{ inputs.thread-ts }}
          escalation-enabled: 'false'

      - name: Handle notification failure
        if: steps.slack.outputs.status == 'failure'
        run: |
          echo "⚠️ Slack notification failed, but continuing workflow"
          echo "This is a non-blocking failure to prevent notification issues from breaking the pipeline"
