name: 'Unified Slack Notify'
description: 'Unified Slack notification action for all pipeline stages'

inputs:
  webhook-url:
    description: 'Slack webhook URL'
    required: true

  notification-type:
    description: 'Type of notification (create-pipeline, test-results, build-status, deployment-status, deployment-start, deployment-success, deployment-failure, pipeline-summary, failure-escalation, feature-branch, basic-test)'
    required: true

  status:
    description: 'Status of the operation (success, failure, warning, info)'
    required: false
    default: 'info'

  data:
    description: 'Additional data as JSON string'
    required: false
    default: '{}'

  thread-ts:
    description: 'Thread timestamp for follow-up notifications'
    required: false
    default: ''

  mentions:
    description: 'Users to mention in the notification'
    required: false
    default: ''

  severity:
    description: 'Severity level (low, medium, high, critical, emergency)'
    required: false
    default: 'medium'

  escalation-enabled:
    description: 'Enable escalation for failures'
    required: false
    default: 'false'

  channel:
    description: 'Slack channel to send notification to'
    required: false
    default: '#general'

  critical-channel:
    description: 'Critical channel for escalations'
    required: false
    default: '#alerts'

  branch:
    description: 'Git branch name'
    required: false
    default: ''

  commit:
    description: 'Git commit SHA'
    required: false
    default: ''

  author:
    description: 'Commit author'
    required: false
    default: ''

  environment:
    description: 'Deployment environment'
    required: false
    default: 'development'

  workflow-url:
    description: 'Workflow URL'
    required: false
    default: ''

outputs:
  thread-ts:
    description: 'Thread timestamp for follow-up notifications'
    value: ${{ steps.notify.outputs.thread-ts }}

  message-ts:
    description: 'Message timestamp'
    value: ${{ steps.notify.outputs.message-ts }}

  success:
    description: 'Whether the notification was sent successfully'
    value: ${{ steps.notify.outputs.success }}

runs:
  using: 'composite'
  steps:
    - name: Validate inputs
      shell: bash
      run: |
        echo "[INFO] Validating notification inputs..."
        WEBHOOK_URL="${{ inputs.webhook-url }}"
        NOTIFICATION_TYPE="${{ inputs.notification-type }}"
        SEVERITY="${{ inputs.severity }}"

        echo "[DEBUG] webhook-url length: ${#WEBHOOK_URL}"
        echo "[DEBUG] notification-type: '$NOTIFICATION_TYPE'"
        echo "[DEBUG] severity: '$SEVERITY'"
        echo "[DEBUG] webhook-url starts with: ${WEBHOOK_URL:0:20}..."

        # Validate webhook URL
        if [[ -z "$WEBHOOK_URL" ]]; then
          echo "[ERROR] Error: webhook-url is required"
          echo "[ERROR] Please ensure SLACK_WEBHOOK_URL secret is set in your repository"
          exit 1
        fi

        # Validate notification type
        VALID_TYPES=("create-pipeline" "test-results" "build-status" "deployment-status" "deployment-start" "deployment-success" "deployment-failure" "pipeline-summary" "failure-escalation" "feature-branch" "basic-test")

        if [[ -z "$NOTIFICATION_TYPE" ]]; then
          echo "[ERROR] Error: notification-type is required but empty"
          exit 1
        fi

        if [[ ! " ${VALID_TYPES[@]} " =~ " $NOTIFICATION_TYPE " ]]; then
          echo "[ERROR] Error: Invalid notification-type '$NOTIFICATION_TYPE'"
          echo "Valid types: ${VALID_TYPES[*]}"
          exit 1
        fi

        # Validate severity
        VALID_SEVERITIES=("low" "medium" "high" "critical" "emergency")

        if [[ -z "$SEVERITY" ]]; then
          echo "[ERROR] Error: severity is required but empty"
          exit 1
        fi

        if [[ ! " ${VALID_SEVERITIES[@]} " =~ " $SEVERITY " ]]; then
          echo "[ERROR] Error: Invalid severity '$SEVERITY'"
          echo "Valid severities: ${VALID_SEVERITIES[*]}"
          exit 1
        fi

        echo "[SUCCESS] Input validation passed"

    - name: Prepare notification data
      shell: bash
      id: prepare
      env:
        INPUT_DATA_RAW: ${{ inputs.data }}
      run: |
        echo "[INFO] Preparing notification data..."

        # Parse input data safely from environment variable
        INPUT_DATA="$INPUT_DATA_RAW"

        # Set default values for GitHub context if not provided
        BRANCH="${{ inputs.branch }}"
        COMMIT="${{ inputs.commit }}"
        AUTHOR="${{ inputs.author }}"
        WORKFLOW_URL="${{ inputs.workflow-url }}"

        # Use GitHub context as fallback if inputs are empty
        if [[ -z "$BRANCH" ]]; then
          BRANCH="${{ github.ref_name }}"
        fi

        if [[ -z "$COMMIT" ]]; then
          COMMIT="${{ github.sha }}"
        fi

        if [[ -z "$AUTHOR" ]]; then
          AUTHOR="${{ github.actor }}"
        fi

        if [[ -z "$WORKFLOW_URL" ]]; then
          WORKFLOW_URL="${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        fi

        # Create comprehensive notification data with proper JSON formatting
        NOTIFICATION_DATA=$(jq -nc \
          --arg branch "$BRANCH" \
          --arg commit "$COMMIT" \
          --arg author "$AUTHOR" \
          --arg environment "${{ inputs.environment }}" \
          --arg status "${{ inputs.status }}" \
          --arg severity "${{ inputs.severity }}" \
          --arg workflowUrl "$WORKFLOW_URL" \
          --arg mentions "${{ inputs.mentions }}" \
          --arg threadTs "${{ inputs.thread-ts }}" \
          '{
            branch: $branch,
            commit: $commit,
            author: $author,
            environment: $environment,
            status: $status,
            severity: $severity,
            workflowUrl: $workflowUrl,
            mentions: $mentions,
            threadTs: $threadTs
          }')

        echo "[DEBUG] Generated notification data: $NOTIFICATION_DATA"

        # Merge with input data if provided and validate JSON
        if [[ "$INPUT_DATA" != "{}" && "$INPUT_DATA" != "" ]]; then
          # Validate input JSON first by trying to parse it
          if PARSED_INPUT=$(echo "$INPUT_DATA" | jq . 2>/dev/null); then
            MERGED_DATA=$(echo "$NOTIFICATION_DATA" "$PARSED_INPUT" | jq -s '.[0] * .[1]')
            # Use heredoc format for GitHub Actions output to handle multiline JSON
            {
              echo "notification-data<<EOF"
              echo "$MERGED_DATA"
              echo "EOF"
            } >> $GITHUB_OUTPUT
            echo "[DEBUG] Merged with input data successfully"
          else
            echo "[WARNING] Invalid input JSON data: $INPUT_DATA"
            echo "[WARNING] Using default data instead"
            {
              echo "notification-data<<EOF"
              echo "$NOTIFICATION_DATA"
              echo "EOF"
            } >> $GITHUB_OUTPUT
          fi
        else
          {
            echo "notification-data<<EOF"
            echo "$NOTIFICATION_DATA"
            echo "EOF"
          } >> $GITHUB_OUTPUT
        fi

        echo "[SUCCESS] Notification data prepared"

    - name: Send Slack notification
      shell: bash
      id: notify
      env:
        SLACK_WEBHOOK_URL: ${{ inputs.webhook-url }}
        SLACK_CHANNEL: ${{ inputs.channel }}
        SLACK_ESCALATION_ENABLED: ${{ inputs.escalation-enabled }}
      run: |
        echo "[INFO] Sending Slack notification..."

        # Check script path
        SCRIPT_PATH="${{ github.action_path }}/../../scripts/unified-slack-notifications.js"
        echo "[DEBUG] Script path: $SCRIPT_PATH"

        if [[ ! -f "$SCRIPT_PATH" ]]; then
          echo "[ERROR] Script not found at: $SCRIPT_PATH"
          ls -la "${{ github.action_path }}/../../scripts/" || echo "Scripts directory not found"
          exit 1
        fi

        # Set executable permissions
        chmod +x "$SCRIPT_PATH" || echo "[WARNING] Could not set executable permissions"

        # Get notification data from prepare step
        NOTIFICATION_TYPE="${{ inputs.notification-type }}"

        # Read notification data from GitHub output
        NOTIFICATION_DATA=""
        if [[ -f "$GITHUB_OUTPUT" ]]; then
          # Extract the JSON data between EOF markers
          NOTIFICATION_DATA=$(sed -n '/notification-data<<EOF/,/^EOF$/p' "$GITHUB_OUTPUT" | sed '1d;$d')
        fi

        # Fallback if no data found
        if [[ -z "$NOTIFICATION_DATA" || "$NOTIFICATION_DATA" == "" ]]; then
          echo "[WARNING] No notification data found, creating basic data"
          NOTIFICATION_DATA=$(jq -nc \
            --arg branch "${{ github.ref_name }}" \
            --arg commit "${{ github.sha }}" \
            --arg author "${{ github.actor }}" \
            --arg status "${{ inputs.status }}" \
            '{
              branch: $branch,
              commit: $commit,
              author: $author,
              status: $status
            }')
        fi

        echo "[DEBUG] Type: $NOTIFICATION_TYPE"
        echo "[DEBUG] Data: $NOTIFICATION_DATA"
        echo "[DEBUG] Webhook URL length: ${#SLACK_WEBHOOK_URL}"
        echo "[DEBUG] Channel: $SLACK_CHANNEL"

        # Execute the unified notification script with proper error handling
        set +e  # Don't exit on error
        node "$SCRIPT_PATH" "$NOTIFICATION_TYPE" "$NOTIFICATION_DATA"
        SCRIPT_EXIT_CODE=$?
        set -e  # Re-enable exit on error

        if [[ $SCRIPT_EXIT_CODE -eq 0 ]]; then
          echo "success=true" >> $GITHUB_OUTPUT
          echo "[SUCCESS] Notification sent successfully"
          
          # Check if script already set thread-ts and message-ts outputs
          # If not, set default values as fallback
          if ! grep -q "thread-ts=" "$GITHUB_OUTPUT" 2>/dev/null; then
            THREAD_TS=$(date +%s)
            echo "thread-ts=$THREAD_TS" >> $GITHUB_OUTPUT
            echo "[DEBUG] Set fallback thread-ts: $THREAD_TS"
          fi
          
          if ! grep -q "message-ts=" "$GITHUB_OUTPUT" 2>/dev/null; then
            MESSAGE_TS=$(date +%s)
            echo "message-ts=$MESSAGE_TS" >> $GITHUB_OUTPUT
            echo "[DEBUG] Set fallback message-ts: $MESSAGE_TS"
          fi
        else
          echo "success=false" >> $GITHUB_OUTPUT
          echo "[ERROR] Failed to send notification (exit code: $SCRIPT_EXIT_CODE)"
          
          # Don't fail the workflow for notification failures unless it's critical
          if [[ "${{ inputs.severity }}" == "critical" || "${{ inputs.severity }}" == "emergency" ]]; then
            echo "[CRITICAL] Critical notification failed - failing workflow"
            exit 1
          else
            echo "[WARNING] Non-critical notification failed - continuing workflow"
          fi
        fi

    - name: Handle escalation
      shell: bash
      if: inputs.escalation-enabled == 'true' && (inputs.status == 'failure' || inputs.status == 'error')
      env:
        SLACK_WEBHOOK_URL: ${{ inputs.webhook-url }}
        SLACK_CHANNEL: ${{ inputs.channel }}
        SLACK_CRITICAL_CHANNEL: ${{ inputs.critical-channel }}
      run: |
        echo "[INFO] Handling failure escalation..."

        # Set default values for GitHub context if not provided
        BRANCH="${{ inputs.branch }}"
        COMMIT="${{ inputs.commit }}"
        AUTHOR="${{ inputs.author }}"
        WORKFLOW_URL="${{ inputs.workflow-url }}"

        # Use GitHub context as fallback if inputs are empty
        if [[ -z "$BRANCH" ]]; then
          BRANCH="${{ github.ref_name }}"
        fi

        if [[ -z "$COMMIT" ]]; then
          COMMIT="${{ github.sha }}"
        fi

        if [[ -z "$AUTHOR" ]]; then
          AUTHOR="${{ github.actor }}"
        fi

        if [[ -z "$WORKFLOW_URL" ]]; then
          WORKFLOW_URL="${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        fi

        # Prepare escalation data
        ESCALATION_DATA=$(jq -nc \
          --arg stage "${{ inputs.notification-type }}" \
          --arg error "Pipeline stage failed" \
          --arg branch "$BRANCH" \
          --arg commit "$COMMIT" \
          --arg author "$AUTHOR" \
          --arg environment "${{ inputs.environment }}" \
          --arg severity "${{ inputs.severity }}" \
          --arg workflowUrl "$WORKFLOW_URL" \
          --arg mentions "${{ inputs.mentions }}" \
          '{
            stage: $stage,
            error: $error,
            branch: $branch,
            commit: $commit,
            author: $author,
            environment: $environment,
            severity: $severity,
            workflowUrl: $workflowUrl,
            mentions: $mentions
          }')

        # Send escalation notification
        SCRIPT_PATH="${{ github.action_path }}/../../scripts/unified-slack-notifications.js"
        if node "$SCRIPT_PATH" "failure-escalation" "$ESCALATION_DATA"; then
          echo "[SUCCESS] Escalation notification sent"
        else
          echo "[ERROR] Failed to send escalation notification"
        fi

    - name: Save context for threading
      shell: bash
      if: inputs.notification-type == 'create-pipeline'
      run: |
        echo "[INFO] Saving thread context for future notifications..."

        # Set default values for GitHub context if not provided
        BRANCH="${{ inputs.branch }}"
        COMMIT="${{ inputs.commit }}"

        # Use GitHub context as fallback if inputs are empty
        if [[ -z "$BRANCH" ]]; then
          BRANCH="${{ github.ref_name }}"
        fi

        if [[ -z "$COMMIT" ]]; then
          COMMIT="${{ github.sha }}"
        fi

        # Create context file for threading
        CONTEXT_FILE="$GITHUB_WORKSPACE/.slack-context.json"
        THREAD_TS="${{ steps.notify.outputs.thread-ts }}"

        if [[ -n "$THREAD_TS" ]]; then
          jq -nc \
            --arg threadTs "$THREAD_TS" \
            --arg channel "${{ inputs.channel }}" \
            --arg createdAt "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            --arg branch "$BRANCH" \
            --arg commit "$COMMIT" \
            --arg environment "${{ inputs.environment }}" \
            '{
              threadTs: $threadTs,
              channel: $channel,
              createdAt: $createdAt,
              branch: $branch,
              commit: $commit,
              environment: $environment
            }' > "$CONTEXT_FILE"
          
          echo "[SUCCESS] Thread context saved"
        else
          echo "[WARNING] No thread timestamp available"
        fi

branding:
  icon: 'message-circle'
  color: 'blue'
