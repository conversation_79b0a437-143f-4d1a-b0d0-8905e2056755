import { z } from 'zod';

const nodeEnv = z.enum(['production', 'staging', 'development']);
type NodeEnv = z.infer<typeof nodeEnv>;

const logLevel = z.enum(['verbose', 'debug', 'log', 'warn', 'error', 'fatal']);
type LogLevel = z.infer<typeof logLevel>;

const opusConfig = z.object({
  GATEWAY_PORT: z.coerce.number(),
  SENTRY_DSN: z.string(),
  NODE_ENV: nodeEnv,
  USER_ACTIVATION_REDIRECT_URL: z.string().url(),
  LOG_LEVEL: logLevel.array(),
  GOOGLE_PROJECT_ID: z.string(),
  GOOGLE_CLIENT_ID: z.string(),
  GOOGLE_CLIENT_SECRET: z.string(),
  JWT_SECRET: z.string(),
  FILES_MANAGER: z.enum(['google', 'local']),
  GOOGLE_BUCKET_NAME: z.string(),
  PASSWORD_SALT_ROUNDS: z.coerce.number(),
  SENDGRID_API_KEY: z.string(),
  FILES_BASE_URL: z.string().url(),
  API_URL: z.string().url(),
  WEB_URL: z.string().url(),
  GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT: z.string().email(),
});

export const getLogLevel = (level: NodeEnv): LogLevel[] => {
  switch (level) {
    case 'production':
    case 'staging':
      return ['log'];
    case 'development':
    default:
      return ['verbose'];
  }
};

export const OpusConfig = opusConfig.parse({
  GATEWAY_PORT: process.env.GATEWAY_PORT,
  SENTRY_DSN: process.env.SENTRY_DSN ?? '',
  NODE_ENV: process.env.NODE_ENV,
  USER_ACTIVATION_REDIRECT_URL: process.env.USER_ACTIVATION_REDIRECT_URL,
  LOG_LEVEL: getLogLevel(process.env.NODE_ENV as NodeEnv),
  GOOGLE_PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  JWT_SECRET: process.env.JWT_SECRET,
  FILES_MANAGER: process.env.FILES_MANAGER,
  GOOGLE_BUCKET_NAME: process.env.GOOGLE_BUCKET_NAME,
  PASSWORD_SALT_ROUNDS: process.env.PASSWORD_SALT_ROUNDS,
  SENDGRID_API_KEY: process.env.SENDGRID_API_KEY,
  FILES_BASE_URL: process.env.FILES_BASE_URL,
  API_URL: process.env.API_URL,
  WEB_URL: process.env.WEB_URL,
  GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT:
    process.env.GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT,
});
