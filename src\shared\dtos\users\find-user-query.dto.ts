import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { paginationMeta } from '../pagination/page-meta.dto';
import { findUserFilter } from './find-user-filter.dto';
import { findUserInclude } from './find-user-include.dto';
import { findUserSort } from './find-user-sort.dto';

export const findUserQuery = z
  .object({
    filter: findUserFilter,
    sort: z.object({ sort: findUserSort }).partial(),
    include: z.object({ include: findUserInclude }).partial(),
    paging: paginationMeta,
  })
  .partial();

export class FindUserQueryDto extends createZodDto(findUserQuery) {}
