import { TokenManager } from '@/core/abstracts/token-manager';
import {
  EntityNotFoundException,
  InsufficientPermissionException,
  InvalidCredentialException,
} from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { TokenPayloadDto } from '@/shared/dtos/auth/token.dto';
import {
  createdUserFull,
  CreatedUserFullDto,
} from '@/shared/dtos/users/created-user-full.dto';
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { OAuth2Client } from 'google-auth-library';
import { OPTIONAL_JWT_KEY, PUBLIC_KEY } from '../../constants';
import { OpusConfig } from '../../opus-config';
import { OpusRoles } from '@/core/enums/role.enum';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly tokenManager: TokenManager,
    private readonly prisma: PrismaService,
  ) {}

  async parseAuthorization(authHeader: string): Promise<TokenPayloadDto> {
    if (!authHeader) {
      throw new InvalidCredentialException('Unauthorized');
    }

    const [scheme, token] = authHeader.split(' ');

    if (scheme !== 'Bearer' || !token) {
      throw new InvalidCredentialException('Invalid token');
    }

    return this.tokenManager.verify(token);
  }

  async validate(payload: TokenPayloadDto): Promise<CreatedUserFullDto> {
    const user = await this.prisma.user.findUnique({
      where: { id: payload.sub },
      include: {
        role: {
          include: {
            timeAwayApprovers: {
              select: { id: true },
              where: {
                approvalHierarchyId: { not: null },
                approverRole: { users: { some: { id: payload.sub } } },
              },
            },
          },
        },
        profilePicture: true,
        userStatus: true,
        account: true,
        profile: {
          select: {
            employmentStatus: true,
            jobTitle: true,
            managedDepartment: { select: { id: true } },
            departmentId: true,
          },
        },
        timeAwayApprovers: {
          select: { id: true },
          where: { approvalHierarchyId: { not: null } },
        },
      },
    });
    if (
      user?.lastPasswordChange &&
      user.lastPasswordChange.valueOf() > payload.iat * 1000
    ) {
      throw new InvalidCredentialException('User password was changed');
    }
    if (!user) {
      throw new EntityNotFoundException();
    }
    if (!user.activatedAt) {
      throw new InsufficientPermissionException('User is not yet activated');
    }

    const isLeaveApprover =
      user.timeAwayApprovers.length > 0 ||
      user.role.timeAwayApprovers.length > 0 ||
      !!user.profile?.managedDepartment ||
      user.role.title === OpusRoles.Admin;

    return createdUserFull.parse({
      ...user,
      profile: {
        ...user.profile,
        managedDepartmentId: user.profile?.managedDepartment?.id ?? null,
      },
      hasPassword: !!user.password,
      activatedAt: user.activatedAt.toISOString(),
      userStatus: {
        ...user.userStatus,
        until: user.userStatus?.until
          ? user.userStatus.until.toISOString()
          : null,
      },
      isLeaveApprover,
    } as CreatedUserFullDto);
  }

  async __verifyGoogleToken(authHeader: string) {
    try {
      const oauth2Client = new OAuth2Client(
        OpusConfig.GOOGLE_CLIENT_ID,
        OpusConfig.GOOGLE_CLIENT_SECRET,
        'postmessage',
      );
      const accessToken = authHeader.split(' ')[1];
      const identity = await oauth2Client.verifyIdToken({
        idToken: accessToken,
      });

      if (
        identity.getPayload()?.email !==
        OpusConfig.GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT
      ) {
        throw new InvalidCredentialException('Unauthorized');
      }
      return true;
    } catch (error) {
      throw new InvalidCredentialException('Unauthorized');
    }
  }

  async canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const isOptional = this.reflector.getAllAndOverride<boolean>(
      OPTIONAL_JWT_KEY,
      [context.getHandler(), context.getClass()],
    );

    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (isOptional && !authHeader) {
      return true;
    }

    // TODO: Implement google cloud scheduler account verification
    const isCloudScheduler = request.headers['x-cloudscheduler'];
    const userAgent = request.headers['user-agent'];

    if (isCloudScheduler && userAgent == 'Google-Cloud-Scheduler') {
      return this.__verifyGoogleToken(authHeader);
    }

    const parsedPayload = await this.parseAuthorization(authHeader);
    const user = await this.validate(parsedPayload);
    request.user = user;

    return true;
  }
}
