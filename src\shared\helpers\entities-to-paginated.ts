import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { Paging } from '@/shared/dtos/pagination/paginated-base';

export const getPaginationDetails = (
  data: unknown[],
  paginationMeta: PaginationMetaDto,
  totalCount: number,
): Paging => {
  const { limit, page } = paginationMeta;
  const totalPages = Math.ceil(totalCount / limit);
  const nextPage = page + 1 > totalPages ? null : page + 1;
  const prevPage = page - 1 < 1 ? null : page - 1;
  return {
    count: data.length,
    currentPage: page,
    nextPage,
    prevPage,
    totalCount,
    totalPages,
  };
};
