import { UserActivity } from '@/core/enums/user-activity.enum';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

export const createdUserStatus = z.object({
  id: IdType,
  status: z.string().nullable(),
  emoji: z.string().nullable(),
  until: z.string().datetime().nullable(),
  activity: z.nativeEnum(UserActivity),
});

export class CreatedUserStatusDto extends createZodDto(createdUserStatus) {}
