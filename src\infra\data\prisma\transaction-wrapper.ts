export function transaction<T extends (...args: any[]) => any>() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: Parameters<T>) {
      let result;
      try {
        result = originalMethod.apply(this, args);
        return result;
      } catch (error) {
        throw error; // Re-throw the error to allow for further error handling
      }
    };

    return descriptor;
  };
}
