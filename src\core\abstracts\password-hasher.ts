export type PasswordHasherOptions = {
  saltRounds: number;
};

export abstract class PasswordHasher {
  protected readonly options: PasswordHasherOptions;

  constructor(options: PasswordHasherOptions) {
    this.options = options;
  }
  /**
   * Hashes a plaintext password
   *
   * @param password The plaintext password to hash
   * @returns A promise that resolves with the hashed password
   */
  abstract hash(password: string): Promise<string>;

  /**
   * Verifies a plaintext password against a hashed password
   *
   * @param password The plaintext password to verify
   * @param hashedPassword The hashed password to compare against
   * @returns A promise that resolves with true if the passwords match, false otherwise
   */
  abstract verifyHash(
    password: string,
    hashedPassword: string,
  ): Promise<boolean>;
}
