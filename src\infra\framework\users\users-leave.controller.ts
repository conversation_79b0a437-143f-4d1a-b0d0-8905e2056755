import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreateUserTimeAwayDto } from '@/shared/dtos/time-aways/create-user-time-away.dto';
import { CreatedTimeAwayDto } from '@/shared/dtos/time-aways/created-time-away.dto';
import { PaginatedCreatedTimeAwaysDto } from '@/shared/dtos/time-aways/created-time-aways-paginated.dto';
import { FindUserTimeAwayQueryDto } from '@/shared/dtos/time-aways/find-user-time-away-query.dto';
import { PaginatedTimeAwayHistoryDto } from '@/shared/dtos/time-aways/time-away-history-paginated.dto';
import { UpdateTimeAwayDto } from '@/shared/dtos/time-aways/update-time-away.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { parseNestedQuery } from '@/shared/helpers/parse-nested-query';
import { DeleteTimeAwayUseCase } from '@/use-cases/time-aways/delete-time-away.use-case';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import {
  UserIdMatch,
  UserIdOrAccountIdMatch,
} from '../auth/decorators/requester-match.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { LeaveCreditsService } from '../leave-credits/leave-credits.service';
import { LeavesService } from '../leaves/leaves.service';

@ApiTags('users-leave')
@Controller('users')
@SwaggerAuth()
@ApiDefaultErrorMessage()
export class UsersLeaveController {
  constructor(
    private deleteTimeAwayUseCase: DeleteTimeAwayUseCase,

    // TODO: Migrate use cases to service
    private leavesService: LeavesService,
    private leaveCreditsServices: LeaveCreditsService,
  ) {}
  @Post('/:userId/leaves')
  @UserIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiParam({
    name: 'userId',
    type: String,
  })
  @ApiOperation({
    summary: 'Request a leave for a user',
  })
  @ApiOkResponse({
    type: PaginatedCreatedTimeAwaysDto,
  })
  async requestUserleave(
    @CurrentUser() user: CreatedUserDetailsDto,
    @Body() input: CreateUserTimeAwayDto,
  ) {
    return this.leavesService.requestLeave(user, {
      ...input,
      userId: user.id,
    });
  }

  @Get('/:userId/leaves')
  @UserIdOrAccountIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiQuery({
    type: FindUserTimeAwayQueryDto,
  })
  @ApiOperation({
    summary: 'Get the leaves of a user',
  })
  @ApiOkResponse({
    type: PaginatedCreatedTimeAwaysDto,
  })
  async getUserLeaves(
    @Param('userId') userId: string,
    @Query() query: unknown,
  ) {
    const { filter, paging, sort } = await parseNestedQuery(
      FindUserTimeAwayQueryDto.zodSchema,
      query,
    );
    return this.leavesService.findUserLeaves(
      { ...filter, userId },
      paging!,
      sort?.sort,
    );
  }

  @Put('/:userId/leaves/:timeAwayId')
  @UserIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: 'Update a pending leave of a user',
  })
  @ApiOkResponse({
    description: 'Leave has been updated successfully',
    type: CreatedTimeAwayDto,
  })
  async updateUserLeave(
    @CurrentUser() user: CreatedUserDetailsDto,
    @Param('userId') userId: string,
    @Param('timeAwayId') timeAwayId: string,
    @Body() input: UpdateTimeAwayDto,
  ) {
    return this.leavesService.updateLeave(user, timeAwayId, input);
  }

  @Delete('/:userId/leaves/:timeAwayId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UserIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: 'Delete a pending leave of a user',
  })
  @ApiNoContentResponse({
    description: 'Leave has been deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'Leave/user does not exist',
  })
  async deleteUserLeave(
    @CurrentUser() user: CreatedUserDetailsDto,
    @Param('userId') userId: string,
    @Param('timeAwayId') timeAwayId: string,
  ) {
    return this.deleteTimeAwayUseCase.execute(user, timeAwayId);
  }

  @Get('/:userId/leave-history')
  @UserIdOrAccountIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: "Get a list of the user's requested leaves",
  })
  @ApiOkResponse({
    type: PaginatedTimeAwayHistoryDto,
  })
  async getLeaveHistory(
    @Param('userId') userId: string,
    @Query() pagination: PaginationMetaDto,
  ) {
    return this.leavesService.getLeavesHistory(userId, pagination);
  }

  @Get('/:userId/leave-credits')
  @UserIdOrAccountIdMatch({
    userIdLocation: 'params',
    userIdParamName: 'userId',
  })
  @ApiOperation({
    summary: 'Get the leaves of a user',
  })
  @ApiOkResponse({
    type: PaginatedCreatedTimeAwaysDto,
  })
  async getUserLeaveCredits(@Param('userId') userId: string) {
    return this.leaveCreditsServices.getLeaveCredits(userId);
  }
}
