import { EntityCount } from '@/core/base/entity';
import { UserStatusEntity } from '@/core/domain/entities/user-status.entity';
import {
  EntityConflictException,
  EntityNotFoundException,
} from '@/core/exceptions/opus-exceptions';
import { UserStatusesRepository } from '@/core/repositories/user-statuses.repository';
import { ExtendedTransactionalAdapterPrisma } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { FindUserStatusFilterDto } from '@/shared/dtos/user-statuses/find-user-status-filter.dto';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaUserStatusesRepository implements UserStatusesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: UserStatusEntity): Promise<UserStatusEntity> {
    try {
      const userStatus = await this.txHost.tx.userStatus.create({
        data,
      });
      return userStatus;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Id is already used');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }

      throw error;
    }
  }

  async findOne(
    filter: FindUserStatusFilterDto,
  ): Promise<UserStatusEntity | null> {
    return this.txHost.tx.userStatus.findFirst({
      where: filter,
    });
  }

  @Transactional()
  async findAll(
    filter: FindUserStatusFilterDto,
    paginationMeta?: PaginationMetaDto,
  ): Promise<EntityCount<UserStatusEntity>> {
    const data = await this.txHost.tx.userStatus.findMany({
      where: filter,
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
    });
    const count = await this.txHost.tx.userStatus.count({ where: filter });
    return { data, count };
  }

  async update(
    id: string,
    data: Partial<UserStatusEntity>,
  ): Promise<UserStatusEntity> {
    try {
      const userStatus = await this.txHost.tx.userStatus.upsert({
        update: data,
        create: data,
        where: { id },
      });
      return userStatus;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Id is already used');
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }

      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      await this.txHost.tx.userStatus.delete({ where: { id } });
    } catch (error) {
      throw new EntityNotFoundException(`UserStatus ${id} does not exist`);
    }
  }
}
