import { FindResourceFilterDto } from '@/shared/dtos/resources/find-resource-filter.dto';
import { Repository } from '../base/repository';
import { ResourceEntity } from '../domain/entities/resources.entity';

export abstract class ResourcesRepository extends Repository<
  ResourceEntity,
  FindResourceFilterDto,
  unknown
> {
  static removeRelationships(data: ResourceEntity) {
    const { attachments, ...rest } = data;
    return rest;
  }
}
