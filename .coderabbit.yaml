# CodeRabbit AI Code Review Configuration
# This file configures CodeRabbit's behavior for automated code reviews

# Language settings
language: en-US

# Review settings
reviews:
  # Enable automatic reviews on pull requests
  auto_review: true

  # Review depth and thoroughness
  review_level: comprehensive

  # Enable specific review features
  enable_security_review: true
  enable_performance_review: true
  enable_best_practices_review: true
  enable_code_style_review: true

  # Custom review instructions for this Node.js/NestJS project
  custom_instructions: |
    Please focus on:
    - NestJS best practices and patterns
    - TypeScript type safety and proper typing
    - Security vulnerabilities, especially in authentication and authorization
    - Performance optimizations for database queries and API responses
    - Proper error handling and logging
    - Code maintainability and readability
    - Test coverage and quality
    - Dependency injection patterns
    - API design and RESTful principles

# File patterns to include/exclude
path_filters:
  # Include these file types for review
  include:
    - '**/*.ts'
    - '**/*.js'
    - '**/*.json'
    - '**/*.yml'
    - '**/*.yaml'
    - '**/*.md'

  # Exclude these paths from review
  exclude:
    - 'node_modules/**'
    - 'dist/**'
    - 'build/**'
    - 'coverage/**'
    - '*.log'
    - 'package-lock.json'
    - '.env*'

# Comment settings
comments:
  # Enable inline comments on specific lines
  enable_inline_comments: true

  # Enable summary comments
  enable_summary_comments: true

  # Minimum confidence level for posting comments (0.0 to 1.0)
  confidence_threshold: 0.7

  # Maximum number of comments per PR
  max_comments_per_pr: 25

# Integration settings
integration:
  # Enable GitHub integration
  github:
    enabled: true

    # Post review status checks
    enable_status_checks: true

    # Enable PR summary generation
    enable_pr_summary: true

    # Enable walkthrough generation
    enable_walkthrough: true

# Advanced settings
advanced:
  # Enable experimental features
  enable_experimental_features: false

  # Custom model settings (if available)
  model_preferences:
    focus_areas:
      - security
      - performance
      - maintainability
      - testing

# Notification settings
notifications:
  # Enable notifications for review completion
  enable_completion_notifications: true

  # Enable notifications for critical issues
  enable_critical_issue_notifications: true
