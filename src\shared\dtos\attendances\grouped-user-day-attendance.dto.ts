import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { createdAttendance } from './created-attendance.dto';

export const dayLogEntry = createdAttendance
  .omit({ userId: true, day: true })
  .openapi('DayLogEntry');
export const dayLog = z
  .object({
    day: z.string().datetime(),
    totalMillis: z.number(),
    entries: dayLogEntry.array(),
  })
  .openapi('DayLog');
export type DayLog = z.infer<typeof dayLog>;

export const groupedUserDayAttendance = z.object({
  userId: IdType,
  dayLogs: dayLog.array(),
  totalMillis: z.number(),
});
export class GroupedUserDayAttendanceDto extends createZodDto(
  groupedUserDayAttendance,
) {}
