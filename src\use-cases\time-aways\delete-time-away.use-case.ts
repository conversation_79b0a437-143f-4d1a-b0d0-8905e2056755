import { UseCase } from '@/core/base/use-case';
import {
  EntityConflictException,
  EntityNotFoundException,
} from '@/core/exceptions/opus-exceptions';
import { TimeAwaysRepository } from '@/core/repositories/time-aways.repository';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class DeleteTimeAwayUseCase implements UseCase<void> {
  constructor(private readonly repository: TimeAwaysRepository) {}

  public async execute(
    user: CreatedUserDetailsDto,
    timeAwayId: string,
  ): Promise<void> {
    const existing = await this.repository.findOne({
      userId: user.id,
      id: timeAwayId,
    });

    if (!existing) {
      throw new EntityNotFoundException(
        `Time away with ${timeAwayId} does not exist`,
      );
    }

    if (existing.isApproved !== null) {
      throw new EntityConflictException('Cannot delete approved leave');
    }

    await this.repository.remove(timeAwayId);
  }
}
