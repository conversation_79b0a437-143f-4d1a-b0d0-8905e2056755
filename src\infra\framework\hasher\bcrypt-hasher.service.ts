import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';

@Injectable()
export class BcryptHasherService extends PasswordHasher {
  async hash(password: string): Promise<string> {
    return bcrypt.hash(password, this.options.saltRounds);
  }
  async verifyHash(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }
}
