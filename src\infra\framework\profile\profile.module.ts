import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { HasherModule } from '../hasher/hasher.module';
import { LeaveCreditsModule } from '../leave-credits/leave-credits.module';
import { MailerModule } from '../mailer/mailer.module';
import { ProfileController } from './profile.controller';
import { ProfileService } from './profile.service';

@Module({
  imports: [
    HasherModule,
    PrismaModule,
    MailerModule,
    AuthModule,
    LeaveCreditsModule,
  ],
  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService],
})
export class ProfileModule {}
