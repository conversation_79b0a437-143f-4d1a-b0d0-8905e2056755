import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { RoleEntity } from '@/core/domain/entities/role.entity';
import {
  EntityConflictException,
  EntityNotFoundException,
} from '@/core/exceptions/opus-exceptions';
import { RolesRepository } from '@/core/repositories/roles.repository';
import { ExtendedTransactionalAdapterPrisma } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { FindRoleFilterDto } from '@/shared/dtos/roles/find-role-filter.dto';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaRolesRepository implements RolesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: RoleEntity): Promise<RoleEntity> {
    try {
      return await this.txHost.tx.role.create({ data });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Role title is already used');
        }
      }
      throw error;
    }
  }

  async findOne(
    filter: FindRoleFilterDto,
    include?: PickRelation<RoleEntity> | undefined,
  ): Promise<RoleEntity | null> {
    return this.txHost.tx.role.findFirst({ where: filter, include });
  }

  @Transactional()
  async findAll(
    filter: FindRoleFilterDto,
    paginationMeta?: PaginationMetaDto,
    include?: PickRelation<RoleEntity> | undefined,
  ): Promise<EntityCount<RoleEntity>> {
    const data = await this.txHost.tx.role.findMany({
      where: filter,
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      include,
    });
    const count = await this.txHost.tx.role.count({ where: filter });
    return { data, count };
  }

  async update(id: string, data: Partial<RoleEntity>): Promise<RoleEntity> {
    try {
      return await this.txHost.tx.role.update({ where: { id }, data });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new EntityConflictException('Role does not exist');
        }
      }
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      await this.txHost.tx.role.delete({ where: { id } });
    } catch (error) {
      throw new EntityNotFoundException(`Role ${id} does not exist`);
    }
  }
}
