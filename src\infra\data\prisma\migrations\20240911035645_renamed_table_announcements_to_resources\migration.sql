/*
  Warnings:

  - You are about to drop the column `announcement_id ` on the `files` table. All the data in the column will be lost.
  - You are about to drop the `announcements` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "ResourceType" AS ENUM ('Announcement', 'Resource');

-- DropForeignKey
ALTER TABLE "files" DROP CONSTRAINT "files_announcement_id _fkey";

-- AlterTable
ALTER TABLE "files" RENAME COLUMN "announcement_id " TO "resource_id";

-- DropPrimaryKey
ALTER TABLE "announcements" DROP CONSTRAINT "announcements_pkey";

-- DropForeignKey
ALTER TABLE "announcements" DROP CONSTRAINT "announcements_account_id_fkey";

-- DropForeignKey
ALTER TABLE "announcements" DROP CONSTRAINT "announcements_uploader_id_fkey";

-- AlterTable
ALTER TABLE "announcements" RENAME TO "resources";

-- AddColumn
ALTER TABLE "resources" ADD COLUMN "publish_date" TIMESTAMP(3);

-- AddColumn
ALTER TABLE "resources" ADD COLUMN "resource_type" "ResourceType" NOT NULL DEFAULT 'Announcement';

-- AddPrimaryKey
ALTER TABLE "resources" ADD CONSTRAINT "resources_pkey" PRIMARY KEY ("id");

-- AddForeignKey
ALTER TABLE "resources" ADD CONSTRAINT "resources_uploader_id_fkey" FOREIGN KEY ("uploader_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "resources" ADD CONSTRAINT "resources_account_id_fkey" FOREIGN KEY ("account_id") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "files" ADD CONSTRAINT "files_resource_id_fkey" FOREIGN KEY ("resource_id") REFERENCES "resources"("id") ON DELETE SET NULL ON UPDATE CASCADE;
