import { OtpPurpose } from '@/core/enums/otp-purpose.enum';
import { ChangePasswordInputDto } from '@/shared/dtos/auth/change-password-input.dto';
import { GenerateOtpInputDto } from '@/shared/dtos/auth/generate-otp-input.dto';
import { LoginInputDto } from '@/shared/dtos/auth/login-input.dto';
import {
  ActivateUserInput,
  AuthTokensDto,
  RefreshTokenInputDto,
} from '@/shared/dtos/auth/token.dto';
import { MessageDto } from '@/shared/dtos/message/message.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { CreatedUserFullDto } from '@/shared/dtos/users/created-user-full.dto';
import { ChangePasswordUseCase } from '@/use-cases/auth/change-password.use-case';
import { GenerateOtpUseCase } from '@/use-cases/auth/generate-otp.use-case';
import { LoginEmailUseCase } from '@/use-cases/auth/login-email.use-case';
import { LoginGoogleUseCase } from '@/use-cases/auth/login-google.use-case';
import { RefreshTokenUseCase } from '@/use-cases/auth/refresh-token.use-case';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { CurrentUser } from './decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from './decorators/error.decorator';
import { Public } from './decorators/public.decorator';
import { SwaggerAuth } from './decorators/swagger-auth.decorator';

@ApiTags('auth')
@Controller('auth')
@ApiDefaultErrorMessage()
export class AuthController {
  constructor(
    private readonly loginGoogleUseCase: LoginGoogleUseCase,
    private readonly loginEmailUseCase: LoginEmailUseCase,
    private readonly refreshTokenUseCase: RefreshTokenUseCase,
    private readonly generateOtpUseCase: GenerateOtpUseCase,
    private readonly changePasswordUseCase: ChangePasswordUseCase,
    private readonly authService: AuthService,
  ) {}

  @Post('login')
  @Public()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Generate tokens for the user',
    description:
      'Generate tokens for the user using either `Google` or `Email & Password`',
  })
  @ApiBody({ type: LoginInputDto })
  @ApiOkResponse({
    description: 'Credentials been successfully verified',
    type: AuthTokensDto,
  })
  async validateLogin(@Body() data: LoginInputDto): Promise<AuthTokensDto> {
    return this.authService.login(data);
  }

  @Post('refresh')
  @Public()
  @HttpCode(200)
  @ApiOperation({
    summary: 'Generates a new access token',
  })
  @ApiOkResponse({
    description:
      'The newly generated access token from the provided refresh token',
    type: AuthTokensDto,
  })
  @ApiUnauthorizedResponse({ description: 'Invalid refresh token' })
  async refresh(@Body() input: RefreshTokenInputDto) {
    return this.refreshTokenUseCase.execute(input);
  }

  @Get('me')
  @SwaggerAuth()
  @ApiOperation({
    summary: 'Get the details of the current user',
  })
  @ApiOkResponse({
    description: 'The user details of the currently signed in user',
    type: CreatedUserDetailsDto,
  })
  async me(@CurrentUser() user: CreatedUserDetailsDto) {
    return user;
  }

  @Post('generate-otp')
  @HttpCode(HttpStatus.NO_CONTENT)
  @SwaggerAuth()
  @ApiOperation({
    summary: 'Generates an OTP',
    description: `Generates an OTP that can be used for ${Object.values(
      OtpPurpose,
    )
      .map((v) => `\`${v}\``)
      .join(' | ')}`,
  })
  @ApiNoContentResponse({
    description: 'OTP was generated and sent to email',
  })
  async generateOtp(
    @Body() input: GenerateOtpInputDto,
    @CurrentUser() user: CreatedUserFullDto,
  ) {
    await this.generateOtpUseCase.execute(user, input.purpose);
    return;
  }

  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  @SwaggerAuth()
  @ApiOperation({
    summary: 'Change the password of the user',
  })
  @ApiOkResponse({
    description: 'Password has been updated successfully',
    type: MessageDto,
  })
  async changePassword(
    @CurrentUser() user: CreatedUserFullDto,
    @Body() input: ChangePasswordInputDto,
  ) {
    return this.changePasswordUseCase.execute(user, input);
  }

  @Post('activate')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Public()
  @ApiOperation({
    summary: 'Activate a user',
  })
  @ApiNoContentResponse({
    description: 'User has been activated',
  })
  async activate(@Query() input: ActivateUserInput) {
    return this.authService.activateUser(input.token);
  }
}
