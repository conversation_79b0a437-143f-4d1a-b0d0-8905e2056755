# Design Document

## Overview

This design outlines the refactoring of the existing NestJS application from a complex clean architecture pattern to a modular NestJS architecture that follows framework conventions and best practices. The current architecture separates code by architectural layers (domains, use-cases, infrastructure, presentation), which creates unnecessary complexity and makes feature development slower.

The new modular architecture will organize code by business features, making it easier to locate, understand, and modify related functionality. Each module will contain all the necessary components (controllers, services, repositories, DTOs, entities) for a specific business domain.

## Architecture

### Current Architecture Problems

1. **Over-engineered Clean Architecture**: The current structure has excessive layers (domains, use-cases, infrastructure, presentation) that don't provide value in a NestJS context
2. **Scattered Code**: Related functionality is spread across multiple directories making it hard to find and modify
3. **Complex Dependencies**: Use cases are manually wired with factory functions instead of using NestJS dependency injection
4. **Inconsistent Patterns**: Mix of clean architecture and NestJS patterns creates confusion

### New Modular Architecture

The new architecture will follow NestJS conventions with a feature-based modular structure:

```
src/
├── modules/                    # Feature modules
│   ├── users/                 # User management module
│   │   ├── controllers/       # HTTP controllers
│   │   ├── services/          # Business logic services
│   │   ├── repositories/      # Data access layer
│   │   ├── dto/              # Data transfer objects
│   │   ├── entities/         # Database entities/models
│   │   ├── guards/           # Module-specific guards
│   │   ├── interceptors/     # Module-specific interceptors
│   │   └── users.module.ts   # Module definition
│   ├── auth/                 # Authentication module
│   ├── attendance/           # Attendance tracking module
│   ├── leaves/              # Leave management module
│   ├── resources/           # Resource management module
│   ├── departments/         # Department management module
│   └── accounts/            # Account management module
├── shared/                   # Shared utilities and services
│   ├── services/            # Shared business services
│   ├── guards/              # Global guards
│   ├── interceptors/        # Global interceptors
│   ├── pipes/               # Global pipes
│   ├── decorators/          # Custom decorators
│   ├── dto/                 # Shared DTOs
│   ├── interfaces/          # Shared interfaces
│   └── utils/               # Utility functions
├── core/                     # Core application services
│   ├── database/            # Database configuration and migrations
│   ├── config/              # Application configuration
│   ├── exceptions/          # Custom exceptions
│   └── constants/           # Application constants
├── common/                   # Common NestJS components
│   ├── filters/             # Exception filters
│   ├── middleware/          # Custom middleware
│   └── types/               # Common type definitions
└── app.module.ts            # Root application module
```

## Components and Interfaces

### Module Structure

Each feature module will follow a consistent structure:

#### Controllers

- Handle HTTP requests and responses
- Validate input using DTOs
- Delegate business logic to services
- Return standardized responses

#### Services

- Contain business logic
- Orchestrate operations between repositories
- Handle complex business rules
- Manage transactions

#### Repositories

- Abstract data access layer
- Implement repository pattern
- Handle database operations
- Provide clean interface for data access

#### DTOs (Data Transfer Objects)

- Define input/output data structures
- Include validation rules
- Transform data between layers
- Ensure type safety

#### Entities

- Represent database models
- Define relationships
- Include basic validation
- Map to Prisma models

### Shared Components

#### Database Module

- Prisma client configuration
- Connection management
- Transaction handling
- Migration utilities

#### Configuration Module

- Environment variable management
- Application settings
- Feature flags
- External service configuration

#### Authentication & Authorization

- JWT token handling
- Role-based access control
- Guards for route protection
- User session management

### Module Dependencies

Modules will interact through well-defined interfaces:

```typescript
// Example: Users module importing Auth module
@Module({
  imports: [AuthModule.forFeature()],
  // ...
})
export class UsersModule {}
```

## Data Models

The existing Prisma schema will be maintained but organized differently:

### Entity Organization

- Each module will have its own entity definitions
- Shared entities will be in the core/database directory
- Relationships will be properly typed
- Validation will be handled at the DTO level

### Repository Pattern

```typescript
// Abstract repository interface
export abstract class BaseRepository<T> {
  abstract findById(id: string): Promise<T | null>;
  abstract create(data: Partial<T>): Promise<T>;
  abstract update(id: string, data: Partial<T>): Promise<T>;
  abstract delete(id: string): Promise<void>;
}

// Concrete implementation
@Injectable()
export class UsersRepository extends BaseRepository<User> {
  constructor(private prisma: PrismaService) {
    super();
  }
  // Implementation details...
}
```

### Service Layer

```typescript
@Injectable()
export class UsersService {
  constructor(
    private usersRepository: UsersRepository,
    private authService: AuthService,
  ) {}

  async createUser(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    // Business logic implementation
  }
}
```

## Error Handling

### Exception Hierarchy

- Custom exception classes extending NestJS HttpException
- Domain-specific exceptions (UserNotFoundException, InvalidCredentialsException)
- Global exception filter for consistent error responses
- Proper HTTP status codes and error messages

### Error Response Format

```typescript
{
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}
```

## Testing Strategy

### Unit Testing

- Test services in isolation using mocked dependencies
- Test repositories with in-memory database
- Test controllers with mocked services
- Achieve high code coverage for business logic

### Integration Testing

- Test module interactions
- Test database operations with test database
- Test API endpoints end-to-end
- Test authentication and authorization flows

### Test Organization

```
src/modules/users/
├── __tests__/
│   ├── users.service.spec.ts
│   ├── users.controller.spec.ts
│   ├── users.repository.spec.ts
│   └── users.integration.spec.ts
```

### Testing Utilities

- Shared test utilities in `test/` directory
- Database seeding for consistent test data
- Mock factories for creating test objects
- Custom matchers for domain-specific assertions

## Migration Strategy

### Phase 1: Core Infrastructure

1. Set up new module structure
2. Create shared services and utilities
3. Migrate database configuration
4. Set up testing infrastructure

### Phase 2: Feature Module Migration

1. Start with least dependent modules (auth, users)
2. Migrate one module at a time
3. Maintain backward compatibility during migration
4. Update imports and dependencies incrementally

### Phase 3: Cleanup and Optimization

1. Remove old architecture files
2. Update documentation
3. Optimize imports and dependencies
4. Performance testing and optimization

### Backward Compatibility

- Maintain existing API endpoints during migration
- Use adapter pattern where necessary
- Gradual migration without breaking changes
- Feature flags for new vs old implementations

## Performance Considerations

### Database Optimization

- Efficient query patterns in repositories
- Proper indexing strategy
- Connection pooling configuration
- Query optimization and monitoring

### Caching Strategy

- Redis integration for session management
- Application-level caching for frequently accessed data
- Cache invalidation strategies
- Performance monitoring and metrics

### Scalability

- Stateless service design
- Horizontal scaling capabilities
- Load balancing considerations
- Resource usage optimization

## Security Considerations

### Authentication & Authorization

- JWT token security
- Role-based access control (RBAC)
- API rate limiting
- Input validation and sanitization

### Data Protection

- Sensitive data encryption
- Secure password hashing
- GDPR compliance considerations
- Audit logging for sensitive operations

### API Security

- CORS configuration
- Request validation
- SQL injection prevention
- XSS protection
