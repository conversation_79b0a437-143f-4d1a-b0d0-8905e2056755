import { OpusRoles } from '@/core/enums/role.enum';
import { CreatedTimeAwayDto } from '@/shared/dtos/time-aways/created-time-away.dto';
import { FinalizeTimeAwayInputDto } from '@/shared/dtos/time-aways/finalize-time-away.dto';
import { GroupedLeavesFilterDto } from '@/shared/dtos/time-aways/grouped-leaves-filter.dto';
import { ManyGroupedLeavesDto } from '@/shared/dtos/time-aways/grouped-leaves.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { Body, Controller, Get, Param, Put, Query } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { LeavesService } from './leaves.service';

@Controller('leaves')
@ApiTags('leaves')
@ApiDefaultErrorMessage()
@SwaggerAuth()
@Roles([OpusRoles.Admin])
export class LeavesController {
  constructor(private readonly leavesService: LeavesService) {}

  @Get('/overview')
  @ApiOkResponse({ type: ManyGroupedLeavesDto })
  @ApiOperation({
    summary: 'Get overview of leaves',
  })
  async getLeaves(
    @Query() filter: GroupedLeavesFilterDto,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.leavesService.getLeavesAdminOverview(user, {
      ...filter,
    });
  }

  @Put('/:timeAwayId/approve')
  @ApiOkResponse({ type: CreatedTimeAwayDto })
  @ApiOperation({
    summary: 'Approve or disapprove a leave by id',
  })
  async finalizeLeave(
    @Param('timeAwayId') timeAwayId: string,
    @Body() input: FinalizeTimeAwayInputDto,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.leavesService.approve(timeAwayId, input, user);
  }
}
