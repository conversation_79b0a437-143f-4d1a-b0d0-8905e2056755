import { UpsertDepartmentInputDto } from '@/shared/dtos/department/create-department.dto';
import {
  CreatedDepartmentDto,
  ManyCreatedDepartmentsDto,
} from '@/shared/dtos/department/created-department.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { Body, Controller, Delete, Get, Param, Put } from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { DepartmentsService } from './departments.service';

@Controller('departments')
@ApiTags('departments')
@ApiDefaultErrorMessage()
@SwaggerAuth()
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Put()
  @ApiOkResponse({ type: CreatedDepartmentDto })
  @ApiOperation({
    summary: 'Upsert a department',
  })
  async upsertDepartment(
    @Body() input: UpsertDepartmentInputDto,
    @CurrentUser() creator: CreatedUserDetailsDto,
  ) {
    return this.departmentsService.upsertDepartment(input, creator);
  }

  @Get()
  @ApiOkResponse({ type: ManyCreatedDepartmentsDto })
  @ApiOperation({
    summary: 'Get departments',
  })
  async getDepartments(@CurrentUser() requester: CreatedUserDetailsDto) {
    return this.departmentsService.getDepartments(requester);
  }

  @Delete('/:id')
  @ApiNoContentResponse()
  @ApiOperation({
    summary: 'Delete a department by id',
  })
  async deleteDepartment(
    @Param('id') id: string,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.departmentsService.deleteDepartment(id, user);
  }
}
