import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { Module } from '@nestjs/common';
import { LeaveApprovalHierarchiesController } from './leave-approval-hierarchies.controller';
import { LeaveApprovalHierarchiesService } from './leave-approval-hierarchies.service';

@Module({
  imports: [PrismaModule],
  providers: [LeaveApprovalHierarchiesService],
  controllers: [LeaveApprovalHierarchiesController],
  exports: [LeaveApprovalHierarchiesService],
})
export class LeaveApprovalHierarchiesModule {}
