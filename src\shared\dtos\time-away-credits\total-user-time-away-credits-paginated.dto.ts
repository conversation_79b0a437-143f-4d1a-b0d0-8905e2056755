import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { paginateSchema } from '../pagination/paginated-base';
import { totalUserTimeAwayCredits } from './total-user-time-away-credits.dto';

const paginatedTotalUserTimeAwayCredits = paginateSchema(
  totalUserTimeAwayCredits,
);

export class PaginatedTotalUserTimeAwayCreditsDto extends createZodDto(
  paginatedTotalUserTimeAwayCredits,
) {}
