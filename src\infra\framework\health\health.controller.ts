import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Public } from '../auth/decorators/public.decorator';
import { HealthService } from './health.service';
import { HealthCheckResponse, HealthCheckResponseDto } from './health.types';

/**
 * HealthController
 *
 * REST API controller that provides health monitoring endpoints for the Opus Remote Backend application.
 * This controller exposes three main health check endpoints that are publicly accessible (no authentication required):
 *
 * - `/health` - Comprehensive health check including database, memory, and disk status
 * - `/health/ready` - Readiness probe for Kubernetes/container orchestration
 * - `/health/live` - Liveness probe for Kubernetes/container orchestration
 *
 * These endpoints are essential for:
 * - Load balancer health checks
 * - Container orchestration (Kubernetes readiness/liveness probes)
 * - Monitoring and alerting systems
 * - DevOps automation and deployment pipelines
 *
 * All endpoints return standardized health check responses with detailed service status information.
 *
 * @example
 * ```typescript
 * // Example response from /health endpoint
 * {
 *   "status": "healthy",
 *   "timestamp": "2024-01-15T10:30:00.000Z",
 *   "uptime": 3600000,
 *   "version": "1.0.0",
 *   "environment": "production",
 *   "services": {
 *     "database": { "status": "healthy", "responseTime": 25 },
 *     "memory": { "status": "healthy", "responseTime": 2 },
 *     "disk": { "status": "healthy", "responseTime": 15 }
 *   }
 * }
 * ```
 */
@ApiTags('Health')
@Controller('health')
export class HealthController {
  /**
   * Creates an instance of HealthController.
   *
   * @param healthService - The health service that performs actual health checks
   */
  constructor(private readonly healthService: HealthService) {}

  /**
   * Comprehensive health check endpoint
   *
   * Performs a complete health assessment of the application including:
   * - Database connectivity and response time
   * - Memory usage and availability
   * - Disk I/O performance
   *
   * This endpoint is used by monitoring systems to determine the overall health
   * of the application. It returns detailed information about each service component.
   *
   * @returns Promise<HealthCheckResponse> - Complete health status with service details
   * @throws {HttpException} 503 - When critical services are unhealthy
   *
   * @example
   * ```typescript
   * // GET /health
   * // Response when healthy:
   * {
   *   "status": "healthy",
   *   "services": {
   *     "database": { "status": "healthy", "responseTime": 25 },
   *     "memory": { "status": "healthy", "responseTime": 2 }
   *   }
   * }
   * ```
   */
  @Get()
  @Public()
  @ApiOperation({
    summary: 'Health check endpoint',
    description:
      'Returns the health status of the application and its dependencies',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is healthy',
    type: HealthCheckResponseDto,
  })
  @ApiResponse({
    status: 503,
    description: 'Application is unhealthy',
  })
  async getHealth(): Promise<HealthCheckResponse> {
    return this.healthService.checkHealth();
  }

  /**
   * Kubernetes readiness probe endpoint
   *
   * Determines if the application is ready to receive traffic. This endpoint
   * focuses on external dependencies (primarily database connectivity) that
   * are required for the application to function properly.
   *
   * Used by Kubernetes and load balancers to determine when to start routing
   * traffic to this instance. If this check fails, the instance will be removed
   * from the load balancer pool until it becomes ready again.
   *
   * @returns Promise<HealthCheckResponse> - Readiness status focusing on critical dependencies
   * @throws {HttpException} 503 - When application is not ready to serve requests
   *
   * @example
   * ```typescript
   * // GET /health/ready
   * // Response when ready:
   * {
   *   "status": "healthy",
   *   "services": {
   *     "database": { "status": "healthy", "responseTime": 30 }
   *   }
   * }
   * ```
   */
  @Get('ready')
  @Public()
  @ApiOperation({
    summary: 'Readiness check endpoint',
    description: 'Returns whether the application is ready to serve requests',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is ready',
  })
  @ApiResponse({
    status: 503,
    description: 'Application is not ready',
  })
  async getReadiness(): Promise<HealthCheckResponse> {
    return this.healthService.checkReadiness();
  }

  /**
   * liveness probe endpoint
   *
   * Simple check to determine if the application process is alive and responsive.
   * This is the most basic health check that only verifies the application can
   * respond to HTTP requests.
   *
   * Used by Kubernetes to determine if a container should be restarted. If this
   * check fails repeatedly, Kubernetes will kill and restart the container.
   * This check should be lightweight and not depend on external services.
   *
   * @returns Promise<HealthCheckResponse> - Basic liveness status
   *
   * @example
   * ```typescript
   * // GET /health/live
   * // Response (always healthy if reachable):
   * {
   *   "status": "healthy",
   *   "timestamp": "2024-01-15T10:30:00.000Z",
   *   "uptime": 3600000,
   *   "services": {}
   * }
   * ```
   */
  @Get('live')
  @Public()
  @ApiOperation({
    summary: 'Liveness check endpoint',
    description: 'Returns whether the application is alive',
  })
  @ApiResponse({
    status: 200,
    description: 'Application is alive',
  })
  async getLiveness(): Promise<HealthCheckResponse> {
    return this.healthService.checkLiveness();
  }
}
