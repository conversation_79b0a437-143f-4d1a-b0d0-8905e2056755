import { UseCase } from '@/core/base/use-case';
import { CreatedUserStatusMapper } from '@/core/domain/mappers/user-status/created-user-status.mapper';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { UserStatusesRepository } from '@/core/repositories/user-statuses.repository';
import { CreatedUserStatusDto } from '@/shared/dtos/user-statuses/created-user-status.dto';
import { FindUserStatusFilterDto } from '@/shared/dtos/user-statuses/find-user-status-filter.dto';

export class FindOneUserStatusUseCase implements UseCase<CreatedUserStatusDto> {
  private createdUserStatusMapper: CreatedUserStatusMapper;

  constructor(private readonly repository: UserStatusesRepository) {
    this.createdUserStatusMapper = new CreatedUserStatusMapper();
  }

  public async execute(
    data: FindUserStatusFilterDto,
  ): Promise<CreatedUserStatusDto> {
    const createdUserStatus = await this.repository.findOne(data);

    if (!createdUserStatus) {
      throw new EntityNotFoundException();
    }

    return this.createdUserStatusMapper.map(createdUserStatus);
  }
}
