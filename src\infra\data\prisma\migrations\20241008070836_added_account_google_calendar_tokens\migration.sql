-- CreateTable
CREATE TABLE "GoogleCalendarToken" (
    "accountId" TEXT NOT NULL,
    "calendarId" TEXT NOT NULL,
    "refreshToken" TEXT NOT NULL,

    CONSTRAINT "GoogleCalendarToken_pkey" PRIMARY KEY ("accountId","calendarId")
);

-- AddForeignKey
ALTER TABLE "GoogleCalendarToken" ADD CONSTRAINT "GoogleCalendarToken_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
