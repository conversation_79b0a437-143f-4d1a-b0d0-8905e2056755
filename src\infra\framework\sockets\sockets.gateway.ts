import { UserStatusUpdateNotifier } from '@/core/abstracts/user-status-update-manager';
import { UserStatusesRepository } from '@/core/repositories/user-statuses.repository';
import { SchedulerRegistry } from '@nestjs/schedule';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { CronJob } from 'cron';
import { Server } from 'socket.io';
import { LoggerService } from '../logger/logger.service';
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketType,
} from './socket-type';
import { SocketService } from './sockets.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class SocketsGateway
  implements
    OnGatewayConnection,
    OnGatewayDisconnect,
    OnGatewayInit,
    UserStatusUpdateNotifier
{
  private readonly logger: LoggerService = new LoggerService('SocketsGateway');

  constructor(
    private readonly userStatusesRepository: UserStatusesRepository,
    private readonly socketService: SocketService,
    private schedulerRegistry: SchedulerRegistry,
  ) {}

  @WebSocketServer()
  server!: Server<ClientToServerEvents, ServerToClientEvents>;
  async afterInit() {
    this.server.use(this.socketService.verifyClient.bind(this.socketService));
  }

  async handleConnection(client: SocketType) {
    this.logger.debug(`${client.user.email} has connected`);
    this.notifyUserStatusUpdate(client.user.id);
    return this.userStatusesRepository.update(client.user.id, {
      activity: 'Online',
    });
  }

  async handleDisconnect(client: SocketType) {
    this.logger.debug(`${client.user.email} has disconnected`);
    this.notifyUserStatusUpdate(client.user.id);
    return this.userStatusesRepository.update(client.user.id, {
      activity: 'Offline',
    });
  }

  resetUserStatus(userId: string, until: Date) {
    const jobName = `${userId}-reset-user-status`;

    let job;
    try {
      job = this.schedulerRegistry.getCronJob(jobName);

      if (job) {
        this.logger.debug(`Deleting cron job ${jobName}`);
        this.schedulerRegistry.deleteCronJob(jobName);
      }
    } catch (error) {
      this.logger.debug(`Cron job ${jobName} cannot be found`);
    }

    job = new CronJob(until, async () => {
      this.logger.debug(`Executing cron job ${jobName}`);
      await this.userStatusesRepository.update(userId, {
        until: null,
        status: null,
        emoji: null,
      });
      this.notifyUserStatusUpdate(userId);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
    this.logger.debug(
      `Added cron job ${jobName} set to run at ${until.toLocaleString()}`,
    );
  }

  notifyUserStatusUpdate(userId: string) {
    this.server.emit('userStatusUpdate', userId);
  }
}
