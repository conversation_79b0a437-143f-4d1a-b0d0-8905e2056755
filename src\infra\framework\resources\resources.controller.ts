import { OpusRoles } from '@/core/enums/role.enum';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreateResourceInputDto } from '@/shared/dtos/resources/create-resource-input.dto';
import { PaginatedCreateResourceDto } from '@/shared/dtos/resources/created-resource-paginated.dto';
import { CreatedResourceDto } from '@/shared/dtos/resources/created-resource.dto';
import { FindResourceFilterInputDto } from '@/shared/dtos/resources/find-resource-filter.dto';
import {
  PublishResourceInput,
  UpdateResourceInputDto,
} from '@/shared/dtos/resources/update-resource-input.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { CreateResourceUseCase } from '@/use-cases/resources/create-resource.use-case';
import { DeleteResourceUseCase } from '@/use-cases/resources/delete-resource.use-case';
import { FindResourcesUseCase } from '@/use-cases/resources/find-resources.use-case';
import { UpdateResourceUseCase } from '@/use-cases/resources/update-resource.use-case';
import { Transactional } from '@nestjs-cls/transactional';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { ResourcesService } from './resources.service';

@Controller('resources')
@ApiTags('resources')
@ApiDefaultErrorMessage()
@SwaggerAuth()
export class ResourcesController {
  constructor(
    private readonly createResourceUseCase: CreateResourceUseCase,
    private readonly findResourcesUseCase: FindResourcesUseCase,
    private readonly updateResourceUseCase: UpdateResourceUseCase,
    private readonly deleteResourceUseCase: DeleteResourceUseCase,
    private readonly resourcesService: ResourcesService,
  ) {}

  @Post()
  @Roles([OpusRoles.Admin])
  @ApiCreatedResponse({ type: CreatedResourceDto })
  @ApiOperation({
    summary: 'Create a resource',
  })
  async createAnnoucement(
    @Body() data: CreateResourceInputDto,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.createResourceUseCase.execute(data, user);
  }

  @Get()
  @ApiOkResponse({ type: PaginatedCreateResourceDto })
  @ApiOperation({
    summary: 'Get a paginated list of resources',
  })
  async findResources(
    @Query() pagination: PaginationMetaDto,
    @Query() filter: FindResourceFilterInputDto,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.findResourcesUseCase.execute(
      { accountId: user.accountId ?? undefined, ...filter },
      pagination,
    );
  }

  @Put('/:resourceId')
  @Roles([OpusRoles.Admin])
  @ApiOkResponse({ type: PaginatedCreateResourceDto })
  @ApiOperation({
    summary: 'Update a resource by id',
  })
  @Transactional()
  async updateResource(
    @Param('resourceId') resourceId: string,
    @Body() input: UpdateResourceInputDto,
    @CurrentUser() updater: CreatedUserDetailsDto,
  ) {
    return this.updateResourceUseCase.execute(resourceId, input, updater);
  }

  @Delete('/:resourceId')
  @Roles([OpusRoles.Admin])
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({
    description: 'Delete a resource by id',
  })
  @ApiOperation({
    summary: 'Delete a resource by id',
  })
  async deleteResource(
    @Param('resourceId') resourceId: string,
    @CurrentUser() updater: CreatedUserDetailsDto,
  ) {
    return this.deleteResourceUseCase.execute(resourceId, updater);
  }

  @Put('/publish/:resourceId')
  @Roles([OpusRoles.Admin])
  @ApiOkResponse({ type: CreatedResourceDto })
  @ApiOperation({
    summary: 'Publish a resource by id',
  })
  async publishResource(
    @Param('resourceId') resourceId: string,
    @CurrentUser() updater: CreatedUserDetailsDto,
    @Body() input: PublishResourceInput,
  ) {
    return this.resourcesService.setResourcePublish(
      resourceId,
      input.publish,
      updater,
    );
  }
}
