/*
  Warnings:

  - You are about to drop the column `date` on the `time_aways` table. All the data in the column will be lost.
  - The `hours` column on the `time_aways` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - A unique constraint covering the columns `[description]` on the table `time_away_types` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `updated_at` to the `time_away_types` table without a default value. This is not possible if the table is not empty.
  - Added the required column `end_date` to the `time_aways` table without a default value. This is not possible if the table is not empty.
  - Added the required column `start_date` to the `time_aways` table without a default value. This is not possible if the table is not empty.
  - Added the required column `time_away_type_id` to the `time_aways` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "time_away_types" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "time_aways" DROP COLUMN "date",
ADD COLUMN     "end_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "isApproved" BOOLEAN,
ADD COLUMN     "start_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "time_away_type_id" TEXT NOT NULL,
DROP COLUMN "hours",
ADD COLUMN     "hours" DOUBLE PRECISION[];

-- CreateTable
CREATE TABLE "time_away_credits" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "time_away_type_id" TEXT NOT NULL,
    "change" DOUBLE PRECISION NOT NULL,
    "remarks" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "time_away_credits_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "time_away_types_description_key" ON "time_away_types"("description");

-- AddForeignKey
ALTER TABLE "time_aways" ADD CONSTRAINT "time_aways_time_away_type_id_fkey" FOREIGN KEY ("time_away_type_id") REFERENCES "time_away_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_credits" ADD CONSTRAINT "time_away_credits_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_credits" ADD CONSTRAINT "time_away_credits_time_away_type_id_fkey" FOREIGN KEY ("time_away_type_id") REFERENCES "time_away_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
