import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { TimeAwayEntity } from '@/core/domain/entities/time-away.entity';
import {
  EntityConflictException,
  EntityNotFoundException,
} from '@/core/exceptions/opus-exceptions';
import {
  TimeAwaysRepository,
  UserLeaves,
} from '@/core/repositories/time-aways.repository';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { FindTimeAwayFilterDto } from '@/shared/dtos/time-aways/find-time-away-filter.dto';
import { FindUserTimeAwaySortDto } from '@/shared/dtos/time-aways/find-user-time-away-sort.dto';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { ExtendedTransactionalAdapterPrisma } from './prisma.service';

@Injectable()
export class PrismaTimeAwaysRepository implements TimeAwaysRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: TimeAwayEntity): Promise<TimeAwayEntity> {
    try {
      const timeAway = await this.txHost.tx.timeAway.create({
        data: TimeAwaysRepository.removeRelationships(data),
        include: { timeAwayType: true },
      });
      return timeAway;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
        }
      }
      throw error;
    }
  }

  async findOne(filter: FindTimeAwayFilterDto): Promise<TimeAwayEntity | null> {
    return this.txHost.tx.timeAway.findFirst({
      where: this.processFilter(filter),
      include: {
        timeAwayType: true,
        approvalHistory: { include: { checkListAnswers: true } },
      },
    });
  }

  async findOneForRequest(
    filter: FindTimeAwayFilterDto,
  ): Promise<TimeAwayEntity | null> {
    return this.txHost.tx.timeAway.findFirst({
      where: this.processFilterForRequest(filter),
    });
  }

  async sumChangeByUserIdAndTimeAwayTypeId(
    userId: string,
  ): Promise<UserLeaves[]> {
    const data = await this.txHost.tx.timeAway.findMany({
      where: { userId, isApproved: true },
      select: {
        userId: true,
        hours: true,
        isSubtract: true,
        timeAwayTypeId: true,
      },
    });

    const grouped = data.reduce<UserLeaves[]>((prev, curr) => {
      const existingIndex = prev.findIndex(
        (v) =>
          v.userId === curr.userId && v.timeAwayTypeId === curr.timeAwayTypeId,
      );

      const currTotal = curr.hours.reduce(
        (sum, currHour) => sum + currHour * (curr.isSubtract ? -1 : 1),
        0,
      );

      if (existingIndex >= 0) {
        prev[existingIndex].total += currTotal;
      } else {
        prev.push({
          userId: curr.userId,
          timeAwayTypeId: curr.timeAwayTypeId,
          total: currTotal,
        });
      }

      return prev;
    }, []);

    return grouped;
  }

  @Transactional()
  async findAll(
    filter: FindTimeAwayFilterDto,
    paginationMeta: PaginationMetaDto,
    include?: PickRelation<TimeAwayEntity>,
    sort: FindUserTimeAwaySortDto = { startDate: 'asc' },
  ): Promise<EntityCount<TimeAwayEntity>> {
    const data = await this.txHost.tx.timeAway.findMany({
      where: this.processFilter(filter),
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      orderBy: sort,
      include: { approvalHistory: { include: { checkListAnswers: true } } },
    });
    const count = await this.txHost.tx.timeAway.count({
      where: this.processFilter(filter),
    });
    return { data, count };
  }

  async update(
    id: string,
    data: Partial<TimeAwayEntity>,
  ): Promise<TimeAwayEntity> {
    try {
      return await this.txHost.tx.timeAway.update({
        where: { id },
        data: TimeAwaysRepository.removeRelationships(data as TimeAwayEntity),
        include: { timeAwayType: true, user: true },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2025':
            throw new EntityConflictException('Time away does not exist');
        }
      }
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      await this.txHost.tx.timeAway.delete({ where: { id } });
    } catch (error) {
      throw new EntityNotFoundException(`Time away ${id} does not exist`);
    }
  }

  private processFilter(
    filter: FindTimeAwayFilterDto,
  ): Prisma.TimeAwayWhereInput {
    const { startDate, endDate, accountId, ...rest } = filter;
    return {
      ...rest,
      user: { accountId },
      AND: [
        {
          startDate: { gte: startDate },
        },
        { endDate: { lte: endDate } },
      ],
    };
  }

  private processFilterForRequest(
    filter: FindTimeAwayFilterDto,
  ): Prisma.TimeAwayWhereInput {
    const { startDate, endDate, isApproved, accountId, ...rest } = filter;
    return {
      ...rest,
      isApproved: isApproved,
      user: { accountId },
      OR: [
        {
          AND: [
            {
              startDate: { lte: startDate },
            },
            { endDate: { gte: startDate } },
          ],
        },
        {
          AND: [
            {
              startDate: { lte: endDate },
            },
            { endDate: { gte: endDate } },
          ],
        },
      ],
    };
  }
}
