import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { SetMetadata } from '@nestjs/common';
import { USER_ATTRIBUTE_MATCH_KEY } from '../../constants';

export interface UserAttributeMatchProps<T> {
  location: 'query' | 'params';
  userAttributeName: keyof CreatedUserDetailsDto;
  paramName: keyof T;
}
export const UserAttributeMatch = <T>(
  userAttributeMatch: UserAttributeMatchProps<T>,
) => SetMetadata(USER_ATTRIBUTE_MATCH_KEY, userAttributeMatch);
