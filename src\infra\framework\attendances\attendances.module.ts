import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { TimeAwaysRepository } from '@/core/repositories/time-aways.repository';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { GroupAttendancesByDayUseCase } from '@/use-cases/attendances/group-attendances-by-day.use-case';
import { GroupUserAttendancesByDayUseCase } from '@/use-cases/attendances/group-user-attendances-by-day.use-case';
import { Module } from '@nestjs/common';
import { MailerModule } from '../mailer/mailer.module';
import { ProfileModule } from '../profile/profile.module';
import { ProfileService } from '../profile/profile.service';
import { AttendancesController } from './attendances.controller';
import { AttendancesService } from './attendances.service';

@Module({
  imports: [PrismaModule, MailerModule, ProfileModule],
  controllers: [AttendancesController],
  providers: [
    AttendancesService,
    {
      provide: GroupUserAttendancesByDayUseCase,
      useFactory: (attendancesRepository: AttendancesRepository) =>
        new GroupUserAttendancesByDayUseCase(attendancesRepository),
      inject: [AttendancesRepository],
    },
    {
      provide: GroupAttendancesByDayUseCase,
      useFactory: (
        timeAwaysRepository: TimeAwaysRepository,
        groupUserAttendancesByDayUseCase: GroupUserAttendancesByDayUseCase,
        profilesService: ProfileService,
      ) =>
        new GroupAttendancesByDayUseCase(
          timeAwaysRepository,
          groupUserAttendancesByDayUseCase,
          profilesService,
        ),
      inject: [
        TimeAwaysRepository,
        GroupUserAttendancesByDayUseCase,
        ProfileService,
      ],
    },
  ],
  exports: [AttendancesService],
})
export class AttendancesModule {}
