import { TimeAwayTypes } from '@/core/enums/time-away-types.enum';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

const totalTimeAwayCredits = z
  .object({
    timeAwayTypeId: IdType,
    timeAwayType: z.nativeEnum(TimeAwayTypes),
    total: z.number(),
  })
  .array();

export const totalUserTimeAwayCredits = z.object({
  userId: IdType,
  totals: totalTimeAwayCredits,
});

export class TotalUserTimeAwayCreditsDto extends createZodDto(
  totalUserTimeAwayCredits,
) {}
