import { TimeAwaysRepository } from '@/core/repositories/time-aways.repository';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import {
  AnniversariesCalendar,
  BirthdaysCalendar,
  CalendarEventsListDto,
  LeavesCalendar,
} from '@/shared/dtos/calendars/calendar-data.dto';
import {
  CalendarType,
  CalendarTypeFilter,
  countryHolidayIds,
  ListCalendarDateFilter,
  ListCalendarEventsInputDto,
} from '@/shared/dtos/calendars/list-calendar-events-input.dto';
import { CreatedUserFullDto } from '@/shared/dtos/users/created-user-full.dto';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { format } from 'date-fns';
import { OAuth2Client } from 'google-auth-library';
import { calendar_v3 } from 'googleapis';
import { ExternalCalendarEventsDto } from '../../../shared/dtos/calendars/external-calendar-events.dto';
import { LoggerService } from '../logger/logger.service';
import { OpusConfig } from '../opus-config';
import { profileMapper } from '../profile/profile.mapper';
import { ProfileService } from '../profile/profile.service';

@Injectable()
export class CalendarsService {
  private loggger: LoggerService = new LoggerService('CalendarsService');

  constructor(
    private readonly timeAwaysRepository: TimeAwaysRepository,
    private readonly prisma: PrismaService,
  ) {}

  private async __getGoogleEvents(
    refreshToken: string,
    calendarId: string,
    dateFilter: ListCalendarDateFilter,
  ): Promise<ExternalCalendarEventsDto | null> {
    const oauth2client = new OAuth2Client(
      OpusConfig.GOOGLE_CLIENT_ID,
      OpusConfig.GOOGLE_CLIENT_SECRET,
      'postmessage',
    );

    oauth2client.setCredentials({ refresh_token: refreshToken });
    oauth2client.on('tokens', (tokens) => {
      oauth2client.setCredentials({ ...tokens });
    });

    const calendarClient = new calendar_v3.Calendar({ auth: oauth2client });

    let events: ExternalCalendarEventsDto | null = null;
    try {
      const res = await calendarClient.events.list({
        calendarId,
        singleEvents: true,
        timeMin: dateFilter.startDate,
        timeMax: dateFilter.endDate,
      });

      if (res.data.items && res.data.items?.length > 0) {
        events = res.data.items.reduce<ExternalCalendarEventsDto>(
          (prev, curr) => {
            if (curr.status == 'confirmed' || curr.status == 'tentative') {
              prev.push({
                id: curr.id,
                attendees: curr.attendees?.map((attendee) => attendee.email),
                start: curr.start?.dateTime ?? curr.start?.date,
                end: curr.end?.dateTime ?? curr.end?.date,
                description: curr.description,
                link: curr.htmlLink,
                summary: curr.summary,
                // @ts-expect-error allow value
                country: countryHolidayIds[calendarId],
              });
            }
            return prev;
          },
          [],
        );
      } else {
        events = [];
      }
    } catch (error) {
      this.loggger.verbose(error);
    }

    return events;
  }

  async getGoogleEvents(
    user: CreatedUserFullDto,
    dateFilter: ListCalendarDateFilter,
  ): Promise<ExternalCalendarEventsDto | null> {
    if (!user.googleRefreshToken) {
      return null;
    }
    const events = this.__getGoogleEvents(
      user.googleRefreshToken,
      'primary',
      dateFilter,
    );
    return events;
  }

  async getGoogleHolidays(
    user: CreatedUserFullDto,
    dateFilter: ListCalendarDateFilter,
  ): Promise<ExternalCalendarEventsDto | null> {
    if (!user.accountId) {
      return null;
    }

    const accountRefreshTokens = await this.prisma.googleCalendarToken.findMany(
      {
        where: { accountId: user.accountId },
      },
    );

    if (accountRefreshTokens.length === 0) {
      return null;
    }

    const holidays: ExternalCalendarEventsDto = [];

    for (const art of accountRefreshTokens) {
      const holiday = await this.__getGoogleEvents(
        art.refreshToken,
        art.calendarId,
        dateFilter,
      );

      if (!holiday) continue;

      holidays.push(...holiday);
    }

    const filtered = [...new Map(holidays.map((v) => [v.id, v])).values()];

    return filtered;
  }

  async getBirthdays(
    user: CreatedUserFullDto,
    dateFilter: ListCalendarDateFilter,
  ) {
    const result = (await this.prisma.$queryRaw(
      Prisma.sql`WITH filtered AS (
    SELECT
        id,
        first_name,
        last_name,
        birth_date,
        profile_picture_id,
        MAKE_DATE(
            ${format(dateFilter.startDate, 'yyyy')}::integer,
            CAST(EXTRACT(MONTH FROM birth_date) AS INTEGER),
            CAST(EXTRACT(DAY FROM birth_date) AS INTEGER)
        ) AS next_birthday
    FROM 
        profile_info
    WHERE 
        activated_at IS NOT NULL and birth_date IS NOT NULL AND account_id = ${user.accountId}
)
SELECT
  filtered.id,
  first_name,
  last_name,
  birth_date,
  next_birthday,
  profile_picture_id
FROM filtered
JOIN files ON files.id = profile_picture_id 
WHERE next_birthday >= ${dateFilter.startDate}::timestamp AND next_birthday <= ${dateFilter.endDate}::timestamp
`,
    )) as any[];

    const mapped: BirthdaysCalendar = result.map((res) => ({
      firstName: res.first_name,
      lastName: res.last_name,
      birthDate: format(res.birth_date, 'yyyy-MM-dd'),
      nextBirthday: format(res.next_birthday, 'yyyy-MM-dd'),
      picUrl: res.profile_picture_id
        ? `${OpusConfig.FILES_BASE_URL}/${res.profile_picture_id}`
        : null,
    }));
    return mapped;
  }

  async getAnniversaries(
    user: CreatedUserFullDto,
    dateFilter: ListCalendarDateFilter,
  ) {
    const result = (await this.prisma.$queryRaw(
      Prisma.sql`WITH filtered AS (
    SELECT
        id,
        first_name,
        last_name,
        join_date,
        profile_picture_id,
        MAKE_DATE(
            ${format(dateFilter.startDate, 'yyyy')}::integer,
            CAST(EXTRACT(MONTH FROM join_date) AS INTEGER),
            CAST(EXTRACT(DAY FROM join_date) AS INTEGER)
        ) AS next_anniversary
    FROM 
        profile_info
    WHERE 
        activated_at IS NOT NULL and account_id = ${user.accountId}
)
SELECT
  filtered.id,
  first_name,
  last_name,
  join_date,
  next_anniversary,
  profile_picture_id,
  CAST(EXTRACT('YEAR' FROM AGE(next_anniversary, join_date )) as INTEGER) as years
FROM filtered
LEFT JOIN files ON files.id = profile_picture_id 
WHERE
(join_date <= ${dateFilter.startDate}::timestamp OR join_date <= ${dateFilter.endDate}::timestamp)
AND next_anniversary >= ${dateFilter.startDate}::timestamp 
AND next_anniversary <= ${dateFilter.endDate}::timestamp
`,
    )) as any[];

    const mapped: AnniversariesCalendar = result.map((res) => ({
      firstName: res.first_name,
      lastName: res.last_name,
      joinDate: format(res.join_date, 'yyyy-MM-dd'),
      nextAnniversary: format(res.next_anniversary, 'yyyy-MM-dd'),
      years: res.years,
      picUrl: res.profile_picture_id
        ? `${OpusConfig.FILES_BASE_URL}/${res.profile_picture_id}`
        : null,
    }));
    return mapped;
  }

  async getLeaveCalendar(
    user: CreatedUserFullDto,
    dateFilter: ListCalendarDateFilter,
  ) {
    const { data: timeAways } = await this.timeAwaysRepository.findAll({
      isApproved: true,
      isUserRequest: true,
      accountId: user.accountId ?? undefined,
      startDate: dateFilter.startDate,
      endDate: dateFilter.endDate,
    });

    const leavesCalendar: LeavesCalendar = [];

    for (const timeAway of timeAways) {
      const { id, timeAwayTypeId, startDate, endDate, userId } = timeAway;
      const profile = await this.prisma.profileInfo.findUnique({
        where: { id: userId },
        include: ProfileService.PROFILE_INCLUDE,
      });

      if (!profile) {
        continue;
      }

      const {
        id: profileId,
        firstName,
        lastName,
        profilePicture,
      } = profileMapper(profile);

      leavesCalendar.push({
        leave: {
          id,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          timeAwayTypeId,
        },
        profile: { id: profileId, firstName, lastName, profilePicture },
      });
    }

    return leavesCalendar;
  }

  private __checkCalendarType(
    calendarType: CalendarType,
    calendarTypeFilter: CalendarTypeFilter,
  ) {
    return Array.isArray(calendarTypeFilter)
      ? calendarTypeFilter.includes(calendarType)
      : calendarTypeFilter === calendarType;
  }

  async getFullCalendar(
    user: CreatedUserFullDto,
    { calendarTypes, ...dateFilter }: ListCalendarEventsInputDto,
  ): Promise<CalendarEventsListDto> {
    const calendars: CalendarEventsListDto = {};
    if (
      calendarTypes === 'all' ||
      this.__checkCalendarType('google', calendarTypes)
    ) {
      calendars.google = await this.getGoogleEvents(user, dateFilter);
    }

    if (
      calendarTypes === 'all' ||
      this.__checkCalendarType('google-holiday', calendarTypes)
    ) {
      calendars.googleHolidays = await this.getGoogleHolidays(user, dateFilter);
    }

    if (
      calendarTypes === 'all' ||
      this.__checkCalendarType('leaves', calendarTypes)
    ) {
      calendars.leaves = await this.getLeaveCalendar(user, dateFilter);
    }

    if (
      calendarTypes === 'all' ||
      this.__checkCalendarType('birthdays', calendarTypes)
    ) {
      calendars.birthdays = await this.getBirthdays(user, dateFilter);
    }

    if (
      calendarTypes === 'all' ||
      this.__checkCalendarType('anniversaries', calendarTypes)
    ) {
      calendars.anniversaries = await this.getAnniversaries(user, dateFilter);
    }

    return calendars;
  }
}
