import { CreateAccountInputDto } from '@/shared/dtos/accounts/create-account-input.dto';
import { PaginatedCreatedAccountsDto } from '@/shared/dtos/accounts/created-account-paginated.dto';
import { CreatedAccountDto } from '@/shared/dtos/accounts/created-account.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { CreateAccountUseCase } from '@/use-cases/accounts/create-account.use-case';
import { FindAccountsUseCase } from '@/use-cases/accounts/find-accounts.use-case';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { Public } from '../auth/decorators/public.decorator';

@Controller('accounts')
@ApiTags('accounts')
@ApiDefaultErrorMessage()
export class AccountsController {
  constructor(
    private readonly createAccountUseCase: CreateAccountUseCase,
    private readonly findAccountsUseCase: FindAccountsUseCase,
  ) {}

  @Public()
  @Post()
  @ApiOperation({
    summary: 'Create an account',
  })
  @ApiCreatedResponse({ type: CreatedAccountDto })
  async createAccount(@Body() input: CreateAccountInputDto) {
    return this.createAccountUseCase.execute(input);
  }

  @Public()
  @Get()
  @ApiOkResponse({ type: PaginatedCreatedAccountsDto })
  @ApiOperation({
    summary: 'Get a paginated list of accounts',
  })
  async findAccounts(@Query() pagination: PaginationMetaDto) {
    return this.findAccountsUseCase.execute(pagination);
  }
}
