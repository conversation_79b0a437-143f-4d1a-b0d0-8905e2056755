import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { UseCase } from '@/core/base/use-case';
import { CreateUserMapper } from '@/core/domain/mappers/users/create-user.mapper';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import { UsersRepository } from '@/core/repositories/users.repository';
import { CreateUserInputDto } from '@/shared/dtos/users/create-user-input.dto';
import { CreatedUserDto } from '@/shared/dtos/users/created-user.dto';

export class CreateUserUseCase implements UseCase<CreatedUserDto> {
  private createUserMapper: CreateUserMapper;
  private createdUserMapper: CreatedUserMapper;

  constructor(
    private readonly repository: UsersRepository,
    private readonly passwordHasher: PasswordHasher,
  ) {
    this.createUserMapper = new CreateUserMapper();
    this.createdUserMapper = new CreatedUserMapper();
  }

  public async execute(user: CreateUserInputDto): Promise<CreatedUserDto> {
    if (user.password) {
      user.password = await this.passwordHasher.hash(user.password);
    }
    const entity = this.createUserMapper.map(user);
    entity.activatedAt = new Date();

    const createdUser = await this.repository.create(entity);
    return this.createdUserMapper.map(createdUser);
  }
}
