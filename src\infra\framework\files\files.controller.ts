import { DownloadFileInputDto } from '@/shared/dtos/files/download-file-input.dto';
import {
  FileMetaDto,
  FilePermissionDto,
} from '@/shared/dtos/files/file-meta.dto';
import { UploadFileInputDto } from '@/shared/dtos/files/upload-file-input.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { DownloadFileUseCase } from '@/use-cases/files/download-file.use-case';
import { UploadFileUseCase } from '@/use-cases/files/upload-file.use-case';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  StreamableFile,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBody,
  ApiConsumes,
  ApiCreatedResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { OptionalJWT } from '../auth/decorators/optional-jwt.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';

@Controller('files')
@ApiTags('files')
@SwaggerAuth()
@ApiDefaultErrorMessage()
export class FilesController {
  constructor(
    private readonly uploadFileUseCase: UploadFileUseCase,
    private readonly downloadFileUseCase: DownloadFileUseCase,
  ) {}

  @Post()
  @UseInterceptors(FileInterceptor('file' as keyof UploadFileInputDto))
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UploadFileInputDto })
  @ApiOperation({
    summary: 'Upload a file',
    description: 'Upload a file using multipart/form-data',
  })
  @ApiCreatedResponse({ type: FileMetaDto })
  async upload(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() user: CreatedUserDetailsDto,
    @Body() data: FilePermissionDto,
  ) {
    return this.uploadFileUseCase.execute({
      content: file.buffer,
      contentType: file.mimetype,
      fileName: file.originalname,
      originalFileName: file.originalname,
      permission: data.permission,
      uploaderId: user.id,
      fileSize: file.size,
    });
  }

  @Get(':id')
  @OptionalJWT()
  async download(
    @Param() input: DownloadFileInputDto,
    @CurrentUser() user: CreatedUserDetailsDto | undefined,
  ) {
    const file = await this.downloadFileUseCase.execute({ id: input.id }, user);
    return new StreamableFile(file.content, { type: file.contentType });
  }
}
