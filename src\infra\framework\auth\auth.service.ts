import { ExternalImageManager } from '@/core/abstracts/external-image-manager';
import { FilesManager } from '@/core/abstracts/files-manager';
import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { TokenManager } from '@/core/abstracts/token-manager';
import { CreateFileMapper } from '@/core/domain/mappers/files/create-file.mapper';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import { FilePermission } from '@/core/enums/file.enum';
import {
  EntityConflictException,
  InsufficientPermissionException,
  InvalidCredentialException,
  InvalidJwtException,
} from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { UsersRepository } from '@/core/repositories/users.repository';
import {
  EmailLoginInput,
  GoogleLoginInputDto,
  LoginInputDto,
} from '@/shared/dtos/auth/login-input.dto';
import { AuthTokensDto, TokenPayloadDto } from '@/shared/dtos/auth/token.dto';
import { Injectable } from '@nestjs/common';
import { addDays, getUnixTime } from 'date-fns';
import { GoogleLoginService } from './social/google-login.service';
import { OAuth2Client } from 'google-auth-library';
import { OpusConfig } from '../opus-config';
import { calendar_v3 } from 'googleapis';
import { PrismaService } from '@/infra/data/prisma/prisma.service';

@Injectable()
export class AuthService {
  private readonly createdUserMapper: CreatedUserMapper =
    new CreatedUserMapper();
  private readonly createFileMapper: CreateFileMapper = new CreateFileMapper();

  constructor(
    private readonly tokenManager: TokenManager,
    private readonly usersRepository: UsersRepository,
    private readonly passwordHasher: PasswordHasher,
    private readonly googleLogin: GoogleLoginService,
    private readonly externalImage: ExternalImageManager,
    private readonly filesManager: FilesManager,
    private readonly filesRepository: FilesRepository,
    private readonly prisma: PrismaService,
  ) {}
  async activateUser(activationToken: string): Promise<void> {
    const parsedToken = Buffer.from(activationToken, 'base64').toString(
      'ascii',
    );
    const parsedPayload =
      await this.tokenManager.verify<TokenPayloadDto>(parsedToken);

    if (parsedPayload.aud !== 'activate') {
      throw new InvalidJwtException('Invalid activation token');
    }

    const user = await this.usersRepository.findOne({ id: parsedPayload.sub });

    if (!user) {
      throw new InvalidJwtException('Invalid activation token');
    }

    if (user.activatedAt !== null) {
      throw new EntityConflictException('User is already activated');
    }

    await this.usersRepository.update(user.id, {
      activatedAt: new Date(),
    });
  }

  async generateActivationToken(userId: string): Promise<string> {
    const now = new Date();
    addDays(now, 7);

    const activationExpires = addDays(now, 7);
    const activationToken = await this.tokenManager.sign({
      sub: userId,
      iat: getUnixTime(now),
      exp: getUnixTime(activationExpires),
      aud: 'activate',
    });

    return Buffer.from(activationToken).toString('base64');
  }

  private async __updateAccountGoogleHolidayTokens(
    accountId: string,
    userGoogleRefreshToken: string,
  ) {
    const oauth2client = new OAuth2Client(
      OpusConfig.GOOGLE_CLIENT_ID,
      OpusConfig.GOOGLE_CLIENT_SECRET,
      'postmessage',
    );
    oauth2client.setCredentials({ refresh_token: userGoogleRefreshToken });
    oauth2client.on('tokens', (tokens) => {
      oauth2client.setCredentials({ ...tokens });
    });

    const calendarClient = new calendar_v3.Calendar({ auth: oauth2client });

    try {
      const calendarList = (await calendarClient.calendarList.list()).data;
      const calendarIds =
        calendarList.items
          ?.filter(({ id }) =>
            id
              ?.toLocaleLowerCase()
              .includes('<EMAIL>'),
          )
          .map((v) => v.id!) ?? [];

      for (const calendarId of calendarIds) {
        // User is guaranteed to have accountIds by now
        await this.prisma.account.update({
          where: { id: accountId },
          data: {
            googleCalendarTokens: {
              upsert: {
                create: { calendarId, refreshToken: userGoogleRefreshToken },
                update: { calendarId, refreshToken: userGoogleRefreshToken },
                where: {
                  accountId_calendarId: {
                    accountId,
                    calendarId,
                  },
                },
              },
            },
          },
        });
      }
    } catch (error) {
      console.log(error);
    }
  }

  private async __loginEmail(
    input: EmailLoginInput,
  ): Promise<{ tokens: AuthTokensDto; userId: string }> {
    const { email, password } = input;
    const user = await this.usersRepository.findOne({ email });

    if (!user) {
      throw new InvalidCredentialException('User is not registered');
    }

    if (!user.activatedAt) {
      throw new InsufficientPermissionException('User is not activated');
    }

    if (!user.password) {
      throw new InvalidCredentialException('Incorrect password');
    }

    if (!user.accountId) {
      throw new InsufficientPermissionException('User is not activated');
    }

    if (!(await this.passwordHasher.verifyHash(password, user.password))) {
      throw new InvalidCredentialException('Incorrect password');
    }

    const tokens = await this.tokenManager.generateTokens(
      this.createdUserMapper.map(user),
    );

    if (user.googleRefreshToken) {
      await this.__updateAccountGoogleHolidayTokens(
        user.accountId,
        user.googleRefreshToken,
      );
    }
    return { tokens, userId: user.id };
  }

  private async __loginGoogle(
    input: GoogleLoginInputDto,
  ): Promise<{ tokens: AuthTokensDto; userId: string }> {
    const { code } = input;
    const { email, pictureUrl, refreshToken } =
      await this.googleLogin.verifyLogin(code);

    let user = await this.usersRepository.findOne({ email });

    if (!user) {
      throw new InvalidCredentialException('User is not registered');
    }

    if (!user.activatedAt) {
      throw new InsufficientPermissionException('User is not activated');
    }

    if (!user.accountId) {
      throw new InsufficientPermissionException('User is not activated');
    }

    let profilePictureId: string | undefined = undefined;

    // try to update the profile picture if user has no profile picture yet
    if (user.profilePictureId === null && pictureUrl) {
      const file = await this.externalImage.download(pictureUrl);
      const originalFileName = `${user.lastName}-${user.lastName}.jpg`;

      const modifiedFileName = await this.filesManager.upload(
        originalFileName,
        file,
      );

      const createFileData = this.createFileMapper.map({
        content: file,
        contentType: 'image/jpg',
        fileName: modifiedFileName,
        originalFileName: originalFileName,
        permission: FilePermission.Public,
        fileSize: file.byteLength,
        uploaderId: user.id,
      });

      const createdFileMeta = await this.filesRepository.create(createFileData);
      profilePictureId = createdFileMeta.id;
    }

    if (profilePictureId || refreshToken) {
      user = await this.usersRepository.update(user.id, {
        profilePictureId,
        googleRefreshToken: refreshToken,
      });
    }

    const useableRefreshToken = refreshToken ?? user.googleRefreshToken;

    if (useableRefreshToken) {
      await this.__updateAccountGoogleHolidayTokens(
        user.accountId!,
        useableRefreshToken,
      );
    }

    const tokens = await this.tokenManager.generateTokens(
      this.createdUserMapper.map(user),
    );
    return { tokens, userId: user.id };
  }

  async login(input: LoginInputDto): Promise<AuthTokensDto> {
    let res;
    switch (input.auth.scheme) {
      case 'email':
        res = await this.__loginEmail(input.auth);
        break;
      case 'google':
        res = await this.__loginGoogle(input.auth);
        break;
      default:
        throw new InvalidCredentialException();
    }

    if (input.timezone) {
      await this.usersRepository.update(res.userId, {
        timezone: input.timezone,
      });
    }

    return res.tokens;
  }
}
