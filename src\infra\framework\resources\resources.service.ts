import { FilesManager } from '@/core/abstracts/files-manager';
import { CreatedFileMapper } from '@/core/domain/mappers/files/created-file.mapper';
import { CreatedResourceMapper } from '@/core/domain/mappers/resources/created-resource.mapper';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { FileDto } from '@/shared/dtos/files/file.dto';
import { CreatedResourceDto } from '@/shared/dtos/resources/created-resource.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { Injectable } from '@nestjs/common';
import { MailBuilderService } from '../mailer/mail-builder.service';
import { EmailSender } from '@/core/abstracts/email-sender';

@Injectable()
export class ResourcesService {
  private createdResourceMapper: CreatedResourceMapper =
    new CreatedResourceMapper();
  private createdFileMapper: CreatedFileMapper = new CreatedFileMapper();

  constructor(
    private readonly prisma: PrismaService,
    private readonly filesManager: FilesManager,
    private readonly mailBuilder: MailBuilderService,
    private readonly emailSender: EmailSender,
  ) {}

  async setResourcePublish(
    id: string,
    isPublished: boolean,
    publisher: CreatedUserDetailsDto,
  ): Promise<CreatedResourceDto> {
    const resourceEntity = await this.prisma.resource.findUnique({
      where: { id },
      select: {
        accountId: true,
        resourceType: true,
        attachments: true,
        title: true,
        content: true,
      },
    });
    if (!resourceEntity || resourceEntity.accountId !== publisher.accountId) {
      throw new EntityNotFoundException('Resource not found');
    }

    const published = await this.prisma.resource.update({
      where: { id },
      data: { publishDate: isPublished ? new Date() : null },
      include: { uploader: true, attachments: true },
    });

    if (isPublished) {
      const mailAttachments: FileDto[] = [];

      for (const attachment of resourceEntity.attachments ?? []) {
        const content = await this.filesManager.download(attachment.fileName);
        mailAttachments.push({
          ...this.createdFileMapper.map(attachment),
          content,
        });
      }

      const account = await this.prisma.account.findUnique({
        where: { id: publisher.accountId },
        include: {
          users: {
            select: { firstName: true, email: true },
            where: { activatedAt: { not: null } },
          },
        },
      });
      if (account?.users) {
        for (const user of account.users) {
          const mailData = await this.mailBuilder.buildAnnouncementEmail(
            resourceEntity.title,
            resourceEntity.content,
            publisher.firstName,
            user.firstName,
            user.email,
            resourceEntity.resourceType,
            mailAttachments,
          );
          void this.emailSender.send(mailData);
        }
      }
    }

    return this.createdResourceMapper.map(published);
  }
}
