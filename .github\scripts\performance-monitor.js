#!/usr/bin/env node

/**
 * GitHub Actions Performance Monitor
 * Tracks and compares CI/CD pipeline performance metrics
 */

const fs = require('fs');
const path = require('path');

// Configuration
const METRICS_FILE = path.join(__dirname, '..', 'metrics', 'pipeline-performance.json');
const GITHUB_API_BASE = 'https://api.github.com';

/**
 * Performance metrics tracker for GitHub Actions workflows
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = this.loadMetrics();
  }

  /**
   * Load existing metrics from file
   * @returns {Object} Metrics data
   */
  loadMetrics() {
    try {
      if (fs.existsSync(METRICS_FILE)) {
        return JSON.parse(fs.readFileSync(METRICS_FILE, 'utf8'));
      }
    } catch (error) {
      console.warn('Could not load existing metrics:', error.message);
    }
    return {
      runs: [],
      averages: {},
      improvements: {}
    };
  }

  /**
   * Save metrics to file
   */
  saveMetrics() {
    try {
      const dir = path.dirname(METRICS_FILE);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      fs.writeFileSync(METRICS_FILE, JSON.stringify(this.metrics, null, 2));
    } catch (error) {
      console.error('Could not save metrics:', error.message);
    }
  }

  /**
   * Record a new pipeline run
   * @param {Object} runData - Pipeline run data
   */
  recordRun(runData) {
    const run = {
      id: runData.id,
      number: runData.run_number,
      branch: runData.head_branch,
      event: runData.event,
      status: runData.status,
      conclusion: runData.conclusion,
      created_at: runData.created_at,
      updated_at: runData.updated_at,
      duration: this.calculateDuration(runData.created_at, runData.updated_at),
      workflow_name: runData.name,
      commit_sha: runData.head_sha,
      actor: runData.actor?.login,
      optimized: runData.name.includes('Optimized') || runData.name.includes('Fast')
    };

    this.metrics.runs.push(run);
    this.calculateAverages();
    this.calculateImprovements();
    this.saveMetrics();

    return run;
  }

  /**
   * Calculate duration between two timestamps
   * @param {string} start - Start timestamp
   * @param {string} end - End timestamp
   * @returns {number} Duration in seconds
   */
  calculateDuration(start, end) {
    const startTime = new Date(start);
    const endTime = new Date(end);
    return Math.round((endTime - startTime) / 1000);
  }

  /**
   * Calculate average durations for different categories
   */
  calculateAverages() {
    const successfulRuns = this.metrics.runs.filter(run => run.conclusion === 'success');
    const optimizedRuns = successfulRuns.filter(run => run.optimized);
    const standardRuns = successfulRuns.filter(run => !run.optimized);

    this.metrics.averages = {
      all: this.calculateAverage(successfulRuns),
      optimized: this.calculateAverage(optimizedRuns),
      standard: this.calculateAverage(standardRuns),
      last_10: this.calculateAverage(successfulRuns.slice(-10)),
      by_branch: this.calculateAverageByBranch(successfulRuns)
    };
  }

  /**
   * Calculate average duration for a set of runs
   * @param {Array} runs - Array of run objects
   * @returns {Object} Average statistics
   */
  calculateAverage(runs) {
    if (runs.length === 0) return { count: 0, average: 0, min: 0, max: 0 };

    const durations = runs.map(run => run.duration);
    const sum = durations.reduce((a, b) => a + b, 0);
    const average = Math.round(sum / durations.length);
    const min = Math.min(...durations);
    const max = Math.max(...durations);

    return {
      count: runs.length,
      average,
      min,
      max,
      formatted: {
        average: this.formatDuration(average),
        min: this.formatDuration(min),
        max: this.formatDuration(max)
      }
    };
  }

  /**
   * Calculate average durations by branch
   * @param {Array} runs - Array of run objects
   * @returns {Object} Branch-specific averages
   */
  calculateAverageByBranch(runs) {
    const byBranch = {};
    
    runs.forEach(run => {
      if (!byBranch[run.branch]) {
        byBranch[run.branch] = [];
      }
      byBranch[run.branch].push(run);
    });

    const result = {};
    Object.keys(byBranch).forEach(branch => {
      result[branch] = this.calculateAverage(byBranch[branch]);
    });

    return result;
  }

  /**
   * Calculate performance improvements
   */
  calculateImprovements() {
    const { optimized, standard } = this.metrics.averages;
    
    if (optimized.count > 0 && standard.count > 0) {
      const timeSaved = standard.average - optimized.average;
      const percentImprovement = Math.round((timeSaved / standard.average) * 100);
      
      this.metrics.improvements = {
        time_saved_seconds: timeSaved,
        time_saved_formatted: this.formatDuration(timeSaved),
        percent_improvement: percentImprovement,
        standard_average: standard.formatted.average,
        optimized_average: optimized.formatted.average
      };
    }
  }

  /**
   * Format duration in seconds to human-readable format
   * @param {number} seconds - Duration in seconds
   * @returns {string} Formatted duration
   */
  formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  /**
   * Generate performance report
   * @returns {string} Formatted report
   */
  generateReport() {
    const { averages, improvements } = this.metrics;
    
    let report = '\n📊 CI/CD Pipeline Performance Report\n';
    report += '=====================================\n\n';
    
    if (averages.all.count > 0) {
      report += `📈 Overall Statistics (${averages.all.count} successful runs):\n`;
      report += `   Average: ${averages.all.formatted.average}\n`;
      report += `   Range: ${averages.all.formatted.min} - ${averages.all.formatted.max}\n\n`;
    }
    
    if (improvements.percent_improvement) {
      report += '🚀 Optimization Impact:\n';
      report += `   Standard Pipeline: ${improvements.standard_average}\n`;
      report += `   Optimized Pipeline: ${improvements.optimized_average}\n`;
      report += `   Time Saved: ${improvements.time_saved_formatted}\n`;
      report += `   Improvement: ${improvements.percent_improvement}%\n\n`;
    }
    
    if (Object.keys(averages.by_branch).length > 0) {
      report += '🌿 Performance by Branch:\n';
      Object.entries(averages.by_branch).forEach(([branch, stats]) => {
        report += `   ${branch}: ${stats.formatted.average} (${stats.count} runs)\n`;
      });
      report += '\n';
    }
    
    if (averages.last_10.count > 0) {
      report += `📊 Recent Performance (last ${averages.last_10.count} runs):\n`;
      report += `   Average: ${averages.last_10.formatted.average}\n`;
      report += `   Range: ${averages.last_10.formatted.min} - ${averages.last_10.formatted.max}\n`;
    }
    
    return report;
  }

  /**
   * Get performance summary for GitHub Actions output
   * @returns {Object} Summary data
   */
  getSummary() {
    return {
      total_runs: this.metrics.runs.length,
      successful_runs: this.metrics.runs.filter(r => r.conclusion === 'success').length,
      average_duration: this.metrics.averages.all.formatted?.average || 'N/A',
      improvement_percent: this.metrics.improvements.percent_improvement || 0,
      time_saved: this.metrics.improvements.time_saved_formatted || 'N/A'
    };
  }
}

// CLI usage
if (require.main === module) {
  const monitor = new PerformanceMonitor();
  
  // If run data is provided via environment variables (from GitHub Actions)
  if (process.env.GITHUB_RUN_ID) {
    const runData = {
      id: process.env.GITHUB_RUN_ID,
      run_number: process.env.GITHUB_RUN_NUMBER,
      head_branch: process.env.GITHUB_REF_NAME,
      event: process.env.GITHUB_EVENT_NAME,
      status: 'completed',
      conclusion: 'success', // Assume success if script runs
      created_at: process.env.GITHUB_RUN_STARTED_AT || new Date().toISOString(),
      updated_at: new Date().toISOString(),
      name: process.env.GITHUB_WORKFLOW,
      head_sha: process.env.GITHUB_SHA,
      actor: { login: process.env.GITHUB_ACTOR }
    };
    
    monitor.recordRun(runData);
    console.log('✅ Performance data recorded');
  }
  
  // Generate and display report
  console.log(monitor.generateReport());
  
  // Output summary for GitHub Actions
  const summary = monitor.getSummary();
  console.log('\n📋 Summary for GitHub Actions:');
  console.log(`::set-output name=total_runs::${summary.total_runs}`);
  console.log(`::set-output name=average_duration::${summary.average_duration}`);
  console.log(`::set-output name=improvement_percent::${summary.improvement_percent}`);
  console.log(`::set-output name=time_saved::${summary.time_saved}`);
}

module.exports = PerformanceMonitor;