-- CreateEnum
CREATE TYPE "TimeAwayUnits" AS ENUM ('HOURS', 'DAYS', 'WEEKS', 'MONTHS', 'YEARS');

-- CreateEnum
CREATE TYPE "CarryOverRule" AS ENUM ('ONLY', 'MAX', 'MIN');

-- AlterTable
ALTER TABLE "time_away_types" ADD COLUMN     "accountId" TEXT;

-- AlterTable
ALTER TABLE "time_aways" ADD COLUMN     "is_accrual" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "time_away_accrual_policies" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "timeAwayTypeId" TEXT NOT NULL,
    "accrualStart" INTEGER NOT NULL,
    "accrualStartUnit" "TimeAwayUnits" NOT NULL,
    "accrualFrequency" INTEGER NOT NULL,
    "accrualFrequencyUnit" "TimeAwayUnits" NOT NULL,
    "carryOverRule" "CarryOverRule" NOT NULL,
    "carryOverAmount" DOUBLE PRECISION NOT NULL,
    "carryOverAmountUnit" "TimeAwayUnits" NOT NULL,
    "carryOverDate" DATE NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "time_away_accrual_policies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_TimeAwayAccrualPolicyToUser" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "time_away_accrual_policies_title_key" ON "time_away_accrual_policies"("title");

-- CreateIndex
CREATE UNIQUE INDEX "_TimeAwayAccrualPolicyToUser_AB_unique" ON "_TimeAwayAccrualPolicyToUser"("A", "B");

-- CreateIndex
CREATE INDEX "_TimeAwayAccrualPolicyToUser_B_index" ON "_TimeAwayAccrualPolicyToUser"("B");

-- AddForeignKey
ALTER TABLE "time_away_types" ADD CONSTRAINT "time_away_types_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "accounts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "time_away_accrual_policies" ADD CONSTRAINT "time_away_accrual_policies_timeAwayTypeId_fkey" FOREIGN KEY ("timeAwayTypeId") REFERENCES "time_away_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_TimeAwayAccrualPolicyToUser" ADD CONSTRAINT "_TimeAwayAccrualPolicyToUser_A_fkey" FOREIGN KEY ("A") REFERENCES "time_away_accrual_policies"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_TimeAwayAccrualPolicyToUser" ADD CONSTRAINT "_TimeAwayAccrualPolicyToUser_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
