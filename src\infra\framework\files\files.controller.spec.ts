import { Test, TestingModule } from '@nestjs/testing';
import { Files<PERSON>ontroller } from './files.controller';
import { UploadFileUseCase } from '@/use-cases/files/upload-file.use-case';
import { DownloadFileUseCase } from '@/use-cases/files/download-file.use-case';

/**
 * Test suite for FilesController
 * Tests the file upload and download controller functionality
 */
describe('FilesController', () => {
  let controller: FilesController;
  let uploadFileUseCase: jest.Mocked<UploadFileUseCase>;
  let downloadFileUseCase: jest.Mocked<DownloadFileUseCase>;

  beforeEach(async () => {
    // Create mock implementations for the use cases
    const mockUploadFileUseCase = {
      execute: jest.fn(),
    };

    const mockDownloadFileUseCase = {
      execute: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [FilesController],
      providers: [
        {
          provide: UploadFileUseCase,
          useValue: mockUploadFileUseCase,
        },
        {
          provide: DownloadFileUseCase,
          useValue: mockDownloadFileUseCase,
        },
      ],
    }).compile();

    controller = module.get<FilesController>(FilesController);
    uploadFileUseCase = module.get(UploadFileUseCase);
    downloadFileUseCase = module.get(DownloadFileUseCase);
  });

  /**
   * Test that the controller is properly instantiated
   */
  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /**
   * Test that the required use cases are properly injected
   */
  it('should have upload and download use cases injected', () => {
    expect(uploadFileUseCase).toBeDefined();
    expect(downloadFileUseCase).toBeDefined();
  });
});
