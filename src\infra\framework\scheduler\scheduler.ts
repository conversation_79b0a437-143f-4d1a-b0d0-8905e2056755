import { CloudSchedulerClient, protos } from '@google-cloud/scheduler';
import { LoggerService } from '../logger/logger.service';
import { OpusConfig } from '../opus-config';

export async function createSchedule() {
  const logger = new LoggerService('CreateSchedule');

  const scheduler = new CloudSchedulerClient();
  type Job = protos.google.cloud.scheduler.v1.IJob;

  const parent = scheduler.locationPath(
    OpusConfig.GOOGLE_PROJECT_ID,
    'australia-southeast1',
  );

  // Define the job details
  const attendanceReminderJob: Job = {
    name: `${parent}/jobs/attendance-reminder-job-${OpusConfig.NODE_ENV}`,
    timeZone: 'UTC',
    schedule: '*/30 * * * *',
    description: 'Recurring job used to send attendance reminders',
    httpTarget: {
      uri: `${OpusConfig.API_URL}/attendances/remind`,
      httpMethod: 'POST',
      oidcToken: {
        serviceAccountEmail: OpusConfig.GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT,
      },
    },
  };

  const accrualJob: Job = {
    name: `${parent}/jobs/accrual-job-${OpusConfig.NODE_ENV}`,
    timeZone: 'UTC',
    schedule: '0 0 * * *',
    description: 'Recurring job used to accrue leave credits',
    httpTarget: {
      uri: `${OpusConfig.API_URL}/leave-types/accrual/process`,
      httpMethod: 'POST',
      oidcToken: {
        serviceAccountEmail: OpusConfig.GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT,
      },
    },
  };
  logger.log(`Creating cron jobs`);
  try {
    // Create the job
    try {
      const [reminderJobResult] = await scheduler.createJob({
        parent,
        job: attendanceReminderJob,
      });
      logger.log(`Job created: ${reminderJobResult.name}`);
    } catch (error) {
      const [reminderJobResult] = await scheduler.updateJob({
        job: attendanceReminderJob,
      });
      logger.log(`Job updated: ${reminderJobResult.name}`);
    }

    try {
      const [accrualJobResult] = await scheduler.createJob({
        parent,
        job: accrualJob,
      });
      logger.log(`Job created: ${accrualJobResult.name}`);
    } catch (error) {
      const [accrualJobResult] = await scheduler.updateJob({
        job: accrualJob,
      });
      logger.log(`Job updated: ${accrualJobResult.name}`);
    }
  } catch (error) {
    logger.error(error);
  }
}
