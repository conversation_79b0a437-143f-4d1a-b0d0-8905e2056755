import { OpusConfig } from '@/infra/framework/opus-config';
import { TransactionalAdapterPrisma } from '@nestjs-cls/transactional-adapter-prisma';
import {
  INestApplication,
  Injectable,
  OnModuleD<PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';

const millisExtension = Prisma.defineExtension((client) => {
  return client.$extends({
    result: {
      attendance: {
        millis: {
          needs: { startDatetime: true, endDatetime: true },
          compute(attendance) {
            let end = attendance.endDatetime;
            if (!end) {
              end = new Date();
            }
            const millisDiff =
              end.getTime() - attendance.startDatetime.getTime();

            return millisDiff;
          },
        },
      },
    },
  });
});

const filesUrlExtension = Prisma.defineExtension((client) => {
  return client.$extends({
    result: {
      file: {
        url: {
          needs: { id: true },
          compute(file) {
            return `${OpusConfig.FILES_BASE_URL}/${file.id}`;
          },
        },
      },
    },
  });
});

function extendClient(base: PrismaClient) {
  return base.$extends(filesUrlExtension).$extends(millisExtension);
}

class UntypedExtendedClient extends PrismaClient {
  constructor(options?: ConstructorParameters<typeof PrismaClient>[0]) {
    super(options);

    return extendClient(this) as this;
  }
}

const ExtendedPrismaClient = UntypedExtendedClient as unknown as new (
  options?: ConstructorParameters<typeof PrismaClient>[0],
) => ReturnType<typeof extendClient>;

export type ExtendedTransactionalAdapterPrisma = TransactionalAdapterPrisma<
  ReturnType<typeof extendClient>
>;

@Injectable()
export class PrismaService
  extends ExtendedPrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  async enableShutdownHooks(app: INestApplication) {
    process.on('beforeExit', async () => {
      await app.close();
    });
  }
}
