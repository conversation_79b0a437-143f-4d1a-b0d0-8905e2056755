import { TokenManager } from '@/core/abstracts/token-manager';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import {
  EntityNotFoundException,
  InvalidJwtException,
} from '@/core/exceptions/opus-exceptions';
import { UsersRepository } from '@/core/repositories/users.repository';
import { TokenPayloadDto } from '@/shared/dtos/auth/token.dto';
import { Injectable } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import { SocketType } from './socket-type';

@Injectable()
export class SocketService {
  private readonly createdUserMapper: CreatedUserMapper;

  constructor(
    private readonly tokenManager: TokenManager,
    private readonly usersRepository: UsersRepository,
  ) {
    this.createdUserMapper = new CreatedUserMapper();
  }

  async verifyClient(socket: SocketType, next: (data?: any) => void) {
    try {
      const [authType, authToken] =
        socket.handshake.headers.authorization?.split(' ') ?? [];

      if (authType !== 'Bearer') {
        throw new InvalidJwtException();
      }

      const tokenPayload =
        await this.tokenManager.verify<TokenPayloadDto>(authToken);
      const user = await this.usersRepository.findOne({ id: tokenPayload.sub });

      if (!user) {
        throw new EntityNotFoundException('User not found');
      }
      socket.user = this.createdUserMapper.map(user);

      next();
    } catch (err) {
      next(new WsException('Unauthorized'));
    }
  }
}
