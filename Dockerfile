ARG NODE_VERSION=20
# ------ Base image with Node.js ------
FROM node:${NODE_VERSION}-alpine AS base
WORKDIR /opus

# Install system dependencies in one layer
RUN apk add --no-cache \
    dumb-init \
    && addgroup -g 1001 -S nodejs \
    && adduser -S nestjs -u 1001

# ------ Dependencies stage ------
# Setup Bash
FROM base AS deps
WORKDIR /opus

# Install volta
RUN curl https://get.volta.sh | bash

# ------ The actual production image ------
FROM base AS prod
WORKDIR /opus

# Copy package files first for better caching
COPY package.json .
COPY package-lock.json .

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY . .

# Generate Prisma client and build the application
RUN npx prisma generate
RUN npm run build

# Copy and set permissions for start script
COPY ./scripts/start.sh ./scripts/start.sh
RUN chmod +x ./scripts/start.sh

# Expose the port that Cloud Run expects
EXPOSE 8080

# Set the default port for Cloud Run
ENV PORT=8080

CMD ["./scripts/start.sh"]