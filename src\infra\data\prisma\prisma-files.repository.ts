import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { FileEntity } from '@/core/domain/entities/file.entity';
import { EntityConflictException } from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { FindFileFilterDto } from '@/shared/dtos/files/find-file-filter.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { ExtendedTransactionalAdapterPrisma } from './prisma.service';

@Injectable()
export class PrismaFilesRepository implements FilesRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}

  async create(data: FileEntity): Promise<FileEntity> {
    const file = await this.txHost.tx.file.create({
      data: this.removeRelationships(data),
    });

    return file;
  }

  async findOne(
    filter: FindFileFilterDto,
    include: PickRelation<FileEntity>,
  ): Promise<FileEntity | null> {
    const file = await this.txHost.tx.file.findFirst({
      where: this.processFilter(filter),
      include,
    });

    return file;
  }

  @Transactional()
  async findAll(
    filter: FindFileFilterDto,
    paginationMeta?: PaginationMetaDto,
    include?: PickRelation<FileEntity>,
  ): Promise<EntityCount<FileEntity>> {
    const data = await this.txHost.tx.file.findMany({
      where: this.processFilter(filter),
      include,
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
    });
    const count = await this.txHost.tx.file.count({
      where: this.processFilter(filter),
    });
    return { data, count };
  }

  async update(id: string, data: Partial<FileEntity>): Promise<FileEntity> {
    try {
      return this.txHost.tx.file.update({
        where: { id },
        data: this.removeRelationships(data as FileEntity),
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003':
            throw new EntityConflictException('Foreign key constraint error');
          case 'P2025':
            throw new EntityConflictException('File does not exist');
        }
      }
      throw error;
    }
  }

  async remove(): Promise<void> {
    throw new Error('Method not implemented.');
  }

  protected removeRelationships(data: FileEntity) {
    const { uploader, ...rest } = data;
    return rest;
  }

  private processFilter(filter: FindFileFilterDto): Prisma.FileWhereInput {
    const { accountId, ...rest } = filter;
    const where: Prisma.FileWhereInput = { ...rest, uploader: { accountId } };

    return where;
  }
}
