import { Entity } from '@/core/base/entity';
import { TimeAwayTypeEntity } from './time-away-type.entity';
import { UserEntity } from './user.entity';

export class TimeAwayEntity extends Entity {
  id!: string;
  userId!: string;
  hours!: number[];
  startDate!: Date;
  endDate!: Date;
  timeAwayTypeId!: string;
  isApproved!: boolean | null;
  reason!: string | null;
  approvalMessage!: string | null;
  isUserRequest!: boolean;
  isSubtract!: boolean;

  timeAwayType?: TimeAwayTypeEntity;
  user?: UserEntity;
}
