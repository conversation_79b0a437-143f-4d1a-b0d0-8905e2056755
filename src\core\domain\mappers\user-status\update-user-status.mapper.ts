import { Mapper } from '@/core/base/mapper';
import { UpdateUserStatusInputDto } from '@/shared/dtos/user-statuses/update-user-status-input.dto';
import { UserStatusEntity } from '../../entities/user-status.entity';

export class UpdateUserStatusMapper
  implements Mapper<UpdateUserStatusInputDto, UserStatusEntity>
{
  public map(data: UpdateUserStatusInputDto): UserStatusEntity {
    const userStatus = new UserStatusEntity();

    if (data.id) userStatus.id = data.id;
    if (data.status !== undefined) userStatus.status = data.status;
    if (data.emoji !== undefined) userStatus.emoji = data.emoji;
    if (data.until !== undefined)
      userStatus.until = data.until ? new Date(data.until) : null;
    if (data.activity !== undefined) userStatus.activity = data.activity;

    return userStatus;
  }
}
