import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { createdProfile } from '../profiles/created-profile.dto';
import { createdTimeAway } from './created-time-away.dto';
import { totalUserTimeAwayCredits } from '../time-away-credits/total-user-time-away-credits.dto';

export const groupedLeaves = z.object({
  upcoming: createdTimeAway.array(),
  done: createdTimeAway.array(),
  pending: createdTimeAway.array(),
  rejected: createdTimeAway.array(),
});
export type GroupedLeaves = z.infer<typeof groupedLeaves>;

export const leaveProfile = createdProfile.pick({
  id: true,
  firstName: true,
  lastName: true,
  jobTitle: true,
  department: true,
  profilePicture: true,
});
export type LeaveProfile = z.infer<typeof leaveProfile>;

const count = z.object({
  requests: z.number().int(),
  employees: z.number().int(),
});

const leaveCounts = z.object({
  allRequests: count,
  upcoming: count,
  done: count,
  pending: count,
  rejected: count,
});

export type LeaveCounts = z.infer<typeof leaveCounts>;

export const manyUsersGroupedLeaves = z.object({
  profiles: leaveProfile.array(),
  credits: totalUserTimeAwayCredits.array(),
  leaves: groupedLeaves,
  count: leaveCounts,
});

export class ManyGroupedLeavesDto extends createZodDto(
  manyUsersGroupedLeaves,
) {}
