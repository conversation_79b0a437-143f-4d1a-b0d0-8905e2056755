import { ArgumentsHost, Catch, HttpException } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import * as Sentry from '@sentry/node';
import { LoggerService } from '../../logger/logger.service';

@Catch()
export class SentryFilter extends BaseExceptionFilter {
  private readonly logger: LoggerService = new LoggerService('SentryFilter');

  catch(exception: unknown, host: ArgumentsHost) {
    // Only report non HttpExceptions
    if (!(exception instanceof HttpException)) {
      this.logger.error((exception as any)?.stack ?? exception);
      Sentry.captureException(exception);
    }
    super.catch(exception, host);
  }
}
