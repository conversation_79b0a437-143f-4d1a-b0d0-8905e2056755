import { ExternalImageManager } from '@/core/abstracts/external-image-manager';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ExternalImageService implements ExternalImageManager {
  constructor(private readonly axiosClient: HttpService) {}
  async download(url: string): Promise<Buffer> {
    const { data } = await this.axiosClient.axiosRef.get(url, {
      responseType: 'arraybuffer',
    });

    return Buffer.from(data, 'binary');
  }
}
