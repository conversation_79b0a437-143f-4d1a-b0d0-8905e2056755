import { AccountsRepository } from '@/core/repositories/accounts.repository';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { FilesRepository } from '@/core/repositories/files.repository';
import { ProfilesRepository } from '@/core/repositories/profiles.repository';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { RolesRepository } from '@/core/repositories/roles.repository';
import { TimeAwayTypesRepository } from '@/core/repositories/time-away-types.repository';
import { TimeAwaysRepository } from '@/core/repositories/time-aways.repository';
import { UserStatusesRepository } from '@/core/repositories/user-statuses.repository';
import { UsersRepository } from '@/core/repositories/users.repository';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { Module } from '@nestjs/common';
import { PrismaAccountsRepository } from './prisma-accounts.repository';
import { PrismaAttendancesRepository } from './prisma-attendances.repository';
import { PrismaFilesRepository } from './prisma-files.repository';
import { PrismaProfilesRepository } from './prisma-profiles.repository';
import { PrismaResourcesRepository } from './prisma-resources.repository';
import { PrismaRolesRepository } from './prisma-roles.repository';
import { PrismaTimeAwayTypesRepository } from './prisma-time-away-types.repository';
import { PrismaTimeAwaysRepository } from './prisma-time-aways.repository';
import { PrismaUserStatusesRepository } from './prisma-user-statuses.repository';
import { PrismaUsersRepository } from './prisma-users.repository';

@Module({
  providers: [
    PrismaService,
    { provide: AccountsRepository, useClass: PrismaAccountsRepository },
    {
      provide: ResourcesRepository,
      useClass: PrismaResourcesRepository,
    },
    { provide: AttendancesRepository, useClass: PrismaAttendancesRepository },
    { provide: FilesRepository, useClass: PrismaFilesRepository },
    { provide: ProfilesRepository, useClass: PrismaProfilesRepository },
    { provide: RolesRepository, useClass: PrismaRolesRepository },
    {
      provide: TimeAwayTypesRepository,
      useClass: PrismaTimeAwayTypesRepository,
    },
    { provide: TimeAwaysRepository, useClass: PrismaTimeAwaysRepository },
    { provide: UserStatusesRepository, useClass: PrismaUserStatusesRepository },
    { provide: UsersRepository, useClass: PrismaUsersRepository },
  ],
  exports: [
    PrismaService,
    AccountsRepository,
    ResourcesRepository,
    AttendancesRepository,
    FilesRepository,
    ProfilesRepository,
    RolesRepository,
    TimeAwayTypesRepository,
    TimeAwaysRepository,
    UserStatusesRepository,
    UsersRepository,
  ],
})
export class PrismaModule {}
