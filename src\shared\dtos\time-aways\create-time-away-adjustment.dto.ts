import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdTimeAway } from './created-time-away.dto';
import { z } from 'zod';

export const createTimeAwayAdjustment = createdTimeAway
  .omit({
    id: true,
    isApproved: true,
    isUserRequest: true,
    createdAt: true,
    isSubtract: true,
    approvalHistory: true,
    checklist: true,
  })
  .extend({
    hours: z.coerce.number(),
  })
  .partial({ reason: true });

export class CreateTimeAwayAdjustmentDto extends createZodDto(
  createTimeAwayAdjustment,
) {}
