steps:
- id: "Build Docker Image"
  name: 'gcr.io/cloud-builders/docker:latest'
  dir: '.'
  args: ['build', 
          '-t', 'australia-southeast1-docker.pkg.dev/${PROJECT_ID}/opus-remote-backend/opus-remote-backend-prod:$COMMIT_SHA',
          '.']
- id: "Push Docker Image To Artifact Registry"
  name: 'gcr.io/cloud-builders/docker:latest'
  args: ['push', 'australia-southeast1-docker.pkg.dev/${PROJECT_ID}/opus-remote-backend/opus-remote-backend-prod:$COMMIT_SHA']
# Deploy container image to Cloud Run
#- id: "Deploy Cloud Run Service"
- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  entrypoint: gcloud
  args:
    - 'run'
    - 'deploy'
    - 'opus-remote-backend-prod'
    - '--image'
    - 'australia-southeast1-docker.pkg.dev/${PROJECT_ID}/opus-remote-backend/opus-remote-backend-prod:$COMMIT_SHA'
    - '--region'
    - 'australia-southeast2'
images:
  - 'australia-southeast1-docker.pkg.dev/eminent-yen-426407-n6/opus-remote-backend/opus-remote-backend-prod:$COMMIT_SHA'
options:
  logging: CLOUD_LOGGING_ONLY
  dynamic_substitutions: true