import { OpusRoles } from '@/core/enums/role.enum';
import { EntityNotFoundException } from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { SetUserAccrualPolicyInput } from '@/shared/dtos/time-away-credits/set-user-accrual-policy-input';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { ForbiddenException, Injectable } from '@nestjs/common';
import {
  addMonths,
  differenceInDays,
  differenceInHours,
  differenceInMonths,
  differenceInWeeks,
  differenceInYears,
  format,
  parse,
} from 'date-fns';
import { LoggerService } from '../logger/logger.service';

@Injectable()
export class LeaveTypesService {
  private readonly logger: LoggerService = new LoggerService(
    'LeaveTypesService',
  );

  constructor(private readonly prisma: PrismaService) {}

  async setUserAccrualPolicy(
    input: SetUserAccrualPolicyInput,
    requester: CreatedUserDetailsDto,
  ) {
    if (requester.role.title !== OpusRoles.Admin) {
      throw new ForbiddenException();
    }

    const policy = await this.prisma.timeAwayAccrualPolicy.findFirst({
      where: { id: input.timeAwayAccrualPolicyId },
      include: { timeAwayType: { select: { accountId: true } } },
    });

    if (!policy) {
      throw new EntityNotFoundException('Policy does not exist');
    }

    if (policy.timeAwayType.accountId !== requester.accountId) {
      throw new ForbiddenException();
    }

    const otherPolicies = await this.prisma.timeAwayAccrualPolicy.findMany({
      where: { timeAwayTypeId: policy.timeAwayTypeId, id: { not: policy.id } },
    });
    if ('all' in input.users && input.users) {
      const allUserIds = await this.prisma.user.findMany({
        where: { accountId: policy.timeAwayType.accountId },
        select: { id: true },
      });

      // Disconnect all other users from all other policies under the same time away type
      for (const otherPolicy of otherPolicies) {
        await this.prisma.timeAwayAccrualPolicy.update({
          where: { id: otherPolicy.id },
          data: { users: { set: [] } },
        });
      }

      await this.prisma.timeAwayAccrualPolicy.update({
        where: { id: policy.id },
        data: { users: { connect: allUserIds } },
      });
    } else if ('ids' in input.users) {
      // Disconnect the users from all other policies under the same time away type
      for (const otherPolicy of otherPolicies) {
        await this.prisma.timeAwayAccrualPolicy.update({
          where: { id: otherPolicy.id },
          data: {
            users: { disconnect: input.users.ids.map((id) => ({ id })) },
          },
        });
      }

      await this.prisma.timeAwayAccrualPolicy.update({
        where: { id: policy.id },
        data: {
          users: { set: input.users.ids.map((id) => ({ id })) },
        },
      });
    }
  }
  async processAccruals() {
    this.logger.log('Processing Accrual');
    const accounts = await this.prisma.account.findMany({
      select: {
        accountName: true,
        timeAwayTypes: {
          select: {
            timeAwayAccrualPolicies: {
              include: {
                users: {
                  where: { activatedAt: { not: null } },
                  select: {
                    id: true,
                    firstName: true,
                    profile: { select: { joinDate: true } },
                  },
                },
              },
            },
          },
        },
      },
    });

    for (const account of accounts) {
      this.logger.log(`Processing accruals for ${account.accountName}`);
      for (const timeAwayType of account.timeAwayTypes) {
        for (const policy of timeAwayType.timeAwayAccrualPolicies) {
          this.logger.log(
            `Processing ${policy.title}, applies ${policy.accrualStart} ${policy.accrualStartUnit} after hire date`,
          );

          let dateFormat = '';

          switch (policy.accrualFrequency) {
            case 'WEEKLY':
              dateFormat = 'e';
              break;
            case 'MONTHLY':
              dateFormat = 'd';
              break;
          }
          const nextAccrualDate = parse(
            `${policy.accrualFrequencyStart}`,
            dateFormat,
            new Date(),
          );

          for (const user of policy.users) {
            try {
              await this.prisma.$transaction(async (tx) => {
                this.logger.log(
                  `Processing ${user.firstName}, joined ${user.profile?.joinDate}`,
                );
                if (!user.profile?.joinDate) {
                  return;
                }
                const lastAccrual = await tx.timeAway.findFirst({
                  where: {
                    timeAwayTypeId: policy.timeAwayTypeId,
                    accrualDate: { not: null },
                    userId: user.id,
                  },
                  select: { accrualDate: true },
                  orderBy: { accrualDate: 'desc' },
                });

                if (
                  lastAccrual?.accrualDate &&
                  lastAccrual.accrualDate >= nextAccrualDate
                ) {
                  this.logger.log(
                    `Already accrued: ${policy.title} - ${user.firstName}`,
                  );
                  return;
                }

                if (!lastAccrual) {
                  let elapsedSinceJoin = -1;
                  switch (policy.accrualStartUnit) {
                    case 'HOURS':
                      elapsedSinceJoin = differenceInHours(
                        new Date(),
                        user.profile.joinDate,
                      );
                      break;
                    case 'DAYS':
                      elapsedSinceJoin = differenceInDays(
                        new Date(),
                        user.profile.joinDate,
                      );
                      break;
                    case 'WEEKS':
                      elapsedSinceJoin = differenceInWeeks(
                        new Date(),
                        user.profile.joinDate,
                      );
                      break;
                    case 'MONTHS':
                      elapsedSinceJoin = differenceInMonths(
                        new Date(),
                        user.profile.joinDate,
                      );
                      break;
                    case 'YEARS':
                      elapsedSinceJoin = differenceInYears(
                        new Date(),
                        user.profile.joinDate,
                      );
                      break;
                  }
                  const isAccruing = elapsedSinceJoin >= policy.accrualStart;

                  if (!isAccruing) {
                    this.logger.log(
                      `Not yet eligible: ${policy.title} - ${user.firstName}`,
                    );
                    return;
                  }
                }

                let { accrualAmount } = policy;
                let proportion = 0;
                const baseDate = lastAccrual?.accrualDate ?? new Date();
                switch (policy.accrualFrequency) {
                  case 'WEEKLY':
                    proportion =
                      differenceInDays(nextAccrualDate, baseDate) / 7;
                    break;
                  case 'MONTHLY':
                    const previousMonth = addMonths(nextAccrualDate, -1);
                    proportion =
                      differenceInDays(nextAccrualDate, baseDate) /
                      differenceInDays(nextAccrualDate, previousMonth);
                    break;
                }

                let message = `${proportion !== 1 ? `Prorated accrual` : 'Accrual'} for `;

                if (nextAccrualDate > baseDate) {
                  message += `${format(baseDate, 'yyyy/MM/dd')} to ${format(nextAccrualDate, 'yyyy/MM/dd')}`;
                } else {
                  message += `${format(nextAccrualDate, 'yyyy/MM/dd')} to ${format(baseDate, 'yyyy/MM/dd')}`;
                }
                accrualAmount = Number(
                  Math.abs(proportion * policy.accrualAmount).toFixed(3),
                );

                await tx.timeAway.create({
                  data: {
                    startDate: nextAccrualDate,
                    endDate: nextAccrualDate,
                    accrualDate: nextAccrualDate,
                    approvalMessage: message,
                    isUserRequest: false,
                    isApproved: true,
                    isSubtract: false,
                    hours: [accrualAmount],
                    userId: user.id,
                    timeAwayTypeId: policy.timeAwayTypeId,
                  },
                });
              });
            } catch (error) {
              this.logger.error(error);
            }
          }
        }
      }
    }
  }
}
