services:
  staging-db:
    image: postgres:15.3-alpine3.18
    restart: always
    environment:
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    ports:
      - '${DATABASE_PORT}:5432'
    volumes:
      - pgdata:/var/lib/postgresql/data

  gateway:
    build:
      context: ./
      dockerfile: ./Dockerfile
    env_file: '.env'
    restart: no
    hostname: gateway
    ports:
      - '${GATEWAY_PORT}:${GATEWAY_PORT}'
    depends_on:
      - staging-db

volumes:
  pgdata:
  cache:
    driver: local
