name: 'CI/CD Pipeline - with Single Repository Strategy'

on:
  push:
    #main disabled for safety
    branches: [staging, develop, 'hotfix/*']
  pull_request:
    branches: [staging]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
          - development

concurrency:
  group: '${{ github.workflow }}-${{ github.head_ref || github.ref }}-${{ inputs.environment || github.ref_name }}'
  cancel-in-progress: true

permissions:
  contents: 'read'
  id-token: 'write'
  deployments: 'write'

defaults:
  run:
    shell: 'bash'

env:
  IMAGE_NAME: opus-remote-backend # Single image name
  # Enable Docker BuildKit for faster builds
  DOCKER_BUILDKIT: 1
  BUILDKIT_PROGRESS: plain

jobs:
  # Slack notification for pipeline start
  notify-start:
    runs-on: ubuntu-latest
    outputs:
      thread-ts: ${{ steps.notify.outputs.thread-ts }}
      message-ts: ${{ steps.notify.outputs.message-ts }}
      environment: ${{ steps.env-detect.outputs.environment }}
    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      - name: '🎯 Determine environment for notification'
        id: env-detect
        run: |
          EVENT="${{ github.event_name }}"
          BRANCH="${{ github.ref_name }}"

          echo "Event: ${EVENT}"
          echo "Branch: ${BRANCH}"

          if [ "${EVENT}" = "workflow_dispatch" ]; then
            ENV="${{ inputs.environment }}"
            TRIGGER="Manual deployment (${{ github.actor }})"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "staging" ]; then
            ENV="staging"
            TRIGGER="Push to staging branch"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "main" ]; then
            ENV="production"
            TRIGGER="Push to main branch"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "develop" ]; then
            ENV="development"
            TRIGGER="Push to develop branch"
          elif [[ "${BRANCH}" == hotfix/* ]]; then
            ENV="hotfix"
            TRIGGER="Hotfix deployment"
          else
            ENV="${BRANCH}"
            TRIGGER="${EVENT} on ${BRANCH}"
          fi

          echo "environment=${ENV}" >> $GITHUB_OUTPUT
          echo "trigger=${TRIGGER}" >> $GITHUB_OUTPUT
          echo "Detected environment: ${ENV}"
          echo "Trigger type: ${TRIGGER}"

      - name: '📢 Notify deployment start'
        id: notify
        uses: './.github/actions/unified-slack-notify'
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: 'deployment-start'
          environment: ${{ steps.env-detect.outputs.environment }}
          data: '{"branch": "${{ github.ref_name }}", "commit": "${{ github.sha }}", "author": "${{ github.actor }}", "message": "${{ github.event.head_commit.message || github.event_name == ''workflow_dispatch'' && ''Manual deployment trigger'' || ''Automated deployment'' }}", "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", "environment": "${{ steps.env-detect.outputs.environment }}", "trigger": "${{ steps.env-detect.outputs.trigger }}"}"'

  # Determine deployment environment and settings
  setup:
    runs-on: ubuntu-latest
    needs: notify-start
    outputs:
      environment: ${{ steps.config.outputs.environment }}
      deploy: ${{ steps.config.outputs.deploy }}
      service_name: ${{ steps.config.outputs.service_name }}
      docker_tags: ${{ steps.config.outputs.docker_tags }}
      cpu_limit: ${{ steps.config.outputs.cpu_limit }}
      memory_limit: ${{ steps.config.outputs.memory_limit }}
      max_instances: ${{ steps.config.outputs.max_instances }}
    steps:
      - name: '🎯 Configure deployment settings'
        id: config
        run: |
          echo "=== FULL PRODUCTION PIPELINE ==="
          echo "Event: ${{ github.event_name }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Input Environment: ${{ inputs.environment }}"

          # Use environment detected by notify-start job
          ENV="${{ needs.notify-start.outputs.environment }}"
          echo "✅ Using environment from notify-start: ${ENV}"

          case "$ENV" in
            "staging")
              echo "environment=staging" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-staging" >> $GITHUB_OUTPUT
              echo "docker_tags=staging,latest" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=10" >> $GITHUB_OUTPUT
              echo "📋 STAGING CONFIGURATION READY"
              ;;
            "production")
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-production" >> $GITHUB_OUTPUT
              echo "docker_tags=production,v$(date +%Y%m%d)-${{ github.run_number }}" >> $GITHUB_OUTPUT
              echo "cpu_limit=2000m" >> $GITHUB_OUTPUT
              echo "memory_limit=1Gi" >> $GITHUB_OUTPUT
              echo "max_instances=50" >> $GITHUB_OUTPUT
              echo "📋 PRODUCTION CONFIGURATION READY"
              ;;
            "development")
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-dev" >> $GITHUB_OUTPUT
              echo "docker_tags=develop" >> $GITHUB_OUTPUT
              echo "cpu_limit=500m" >> $GITHUB_OUTPUT
              echo "memory_limit=256Mi" >> $GITHUB_OUTPUT
              echo "max_instances=3" >> $GITHUB_OUTPUT
              echo "📋 DEVELOPMENT CONFIGURATION READY"
              ;;
            "hotfix")
              HOTFIX_NAME=$(echo "${{ github.ref_name }}" | sed 's/hotfix\///' | tr '/' '-')
              echo "environment=staging" >> $GITHUB_OUTPUT  # Deploy hotfixes to staging first
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-hotfix-${HOTFIX_NAME}" >> $GITHUB_OUTPUT
              echo "docker_tags=hotfix-${HOTFIX_NAME}" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=5" >> $GITHUB_OUTPUT
              echo "📋 HOTFIX CONFIGURATION READY (Deploy to staging for testing)"
              ;;
            *)
              echo "environment=none" >> $GITHUB_OUTPUT
              echo "deploy=false" >> $GITHUB_OUTPUT
              echo "🚫 No deployment configured for this trigger"
              ;;
          esac

  # Full deployment job with dynamic environment support
  deploy:
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.deploy == 'true'
    environment:
      name: ${{ needs.setup.outputs.environment }}
      url: ${{ steps.deploy.outputs.url }}

    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      - name: '🔐 Authenticate to Google Cloud (Dynamic)'
        uses: 'google-github-actions/auth@v2'
        with:
          workload_identity_provider: ${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GCP_WIF_PROVIDER || secrets.STAGING_GCP_WIF_PROVIDER }}
          service_account: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL }}

      - name: '☁️ Set up Cloud SDK (Dynamic)'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          project_id: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_PROJECT_ID || vars.STAGING_GCP_PROJECT_ID }}

      - name: '🐳 Configure Docker for Artifact Registry (Dynamic)'
        run: |
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            REGION="${{ vars.PRODUCTION_GCP_REGION }}"
            echo "🔴 Configuring Docker for PRODUCTION region: ${REGION}"
          else
            REGION="${{ vars.STAGING_GCP_REGION }}"
            echo "🟡 Configuring Docker for STAGING region: ${REGION}"
          fi
          gcloud auth configure-docker ${REGION}-docker.pkg.dev

      - name: '📦 Setup Node.js'
        if: hashFiles('package.json') != ''
        uses: 'actions/setup-node@v4'
        with:
          node-version-file: 'package.json'
          cache: 'npm'

      - name: '🏗️ Install dependencies and build'
        if: hashFiles('package.json') != ''
        run: |
          npm ci
          npm run build

      - name: '🔨 Build and tag Docker image (Dynamic Environment)'
        run: |
          echo "=== DYNAMIC DOCKER BUILD ==="

          # Set variables based on environment
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            PROJECT_ID="${{ vars.PRODUCTION_GCP_PROJECT_ID }}"
            REGION="${{ vars.PRODUCTION_GCP_REGION }}"
            REPO_NAME="${{ vars.PRODUCTION_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
            echo "🔴 Building for PRODUCTION environment"
          else
            PROJECT_ID="${{ vars.STAGING_GCP_PROJECT_ID }}"
            REGION="${{ vars.STAGING_GCP_REGION }}"
            REPO_NAME="${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
            echo "🟡 Building for STAGING environment"
          fi

          echo "Project: ${PROJECT_ID}"
          echo "Region: ${REGION}"
          echo "Repository: ${REPO_NAME}"

          # Build image once
          docker build -t ${{ env.IMAGE_NAME }}:temp .

          # Set base registry URL
          IMAGE_BASE="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${{ env.IMAGE_NAME }}"
          echo "IMAGE_BASE=${IMAGE_BASE}" >> $GITHUB_ENV
          echo "Image base: ${IMAGE_BASE}"

          # Always tag with git SHA (traceability)
          docker tag ${{ env.IMAGE_NAME }}:temp ${IMAGE_BASE}:${{ github.sha }}

          # Tag with environment-specific tags
          IFS=',' read -ra TAGS <<< "${{ needs.setup.outputs.docker_tags }}"
          for tag in "${TAGS[@]}"; do
            echo "Tagging: ${tag}"
            docker tag ${{ env.IMAGE_NAME }}:temp ${IMAGE_BASE}:${tag}
          done

      - name: '📤 Push Docker image (Dynamic Environment)'
        run: |
          echo "=== DYNAMIC DOCKER PUSH ==="

          # Push SHA tag (always)
          echo "Pushing SHA tag: ${{ github.sha }}"
          docker push ${{ env.IMAGE_BASE }}:${{ github.sha }}

          # Push environment-specific tags
          IFS=',' read -ra TAGS <<< "${{ needs.setup.outputs.docker_tags }}"
          for tag in "${TAGS[@]}"; do
            echo "Pushing tag: ${tag}"
            docker push ${{ env.IMAGE_BASE }}:${tag}
          done

          echo "✅ All images pushed successfully!"
      - name: '🔍 Debug network variables before deployment'
        run: |
          echo "=== NETWORK CONFIGURATION DEBUG ==="
          echo "Environment: ${{ needs.setup.outputs.environment }}"
          echo ""

          # Show raw variable values
          echo "🔍 Raw Variable Values:"
          echo "STAGING_VPC_NETWORK: '${{ vars.STAGING_VPC_NETWORK }}'"
          echo "STAGING_VPC_SUBNET: '${{ vars.STAGING_VPC_SUBNET }}'"
          echo "PRODUCTION_VPC_NETWORK: '${{ vars.PRODUCTION_VPC_NETWORK }}'"
          echo "PRODUCTION_VPC_SUBNET: '${{ vars.PRODUCTION_VPC_SUBNET }}'"
          echo ""

          # Show computed values
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            COMPUTED_NETWORK="${{ vars.PRODUCTION_VPC_NETWORK }}"
            COMPUTED_SUBNET="${{ vars.PRODUCTION_VPC_SUBNET }}"
            echo "🔴 Production Environment Selected"
          else
            COMPUTED_NETWORK="${{ vars.STAGING_VPC_NETWORK }}"
            COMPUTED_SUBNET="${{ vars.STAGING_VPC_SUBNET }}"
            echo "🟡 Staging Environment Selected"
          fi

          echo "🎯 Computed Values for Deployment:"
          echo "Network: '${COMPUTED_NETWORK}'"
          echo "Subnet: '${COMPUTED_SUBNET}'"
          echo "VPC Egress: private-ranges-only"
          echo ""

          # Check if values are empty
          if [ -z "${COMPUTED_NETWORK}" ] || [ "${COMPUTED_NETWORK}" == "null" ]; then
            echo "❌ NETWORK VALUE IS EMPTY OR NULL!"
            echo "This will cause deployment without VPC configuration"
            exit 1
          fi

          if [ -z "${COMPUTED_SUBNET}" ] || [ "${COMPUTED_SUBNET}" == "null" ]; then
            echo "❌ SUBNET VALUE IS EMPTY OR NULL!"
            echo "This will cause deployment without VPC configuration"
            exit 1
          fi

          echo "✅ Network variables look good, proceeding with deployment"

      - name: '🔍 Verify network exists in GCP'
        run: |
          echo "=== GCP NETWORK VERIFICATION ==="

          # Set environment variables for this step
          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            NETWORK="${{ vars.PRODUCTION_VPC_NETWORK }}"
            SUBNET="${{ vars.PRODUCTION_VPC_SUBNET }}"
            PROJECT_ID="${{ vars.PRODUCTION_GCP_PROJECT_ID }}"
            REGION="${{ vars.PRODUCTION_GCP_REGION }}"
          else
            NETWORK="${{ vars.STAGING_VPC_NETWORK }}"
            SUBNET="${{ vars.STAGING_VPC_SUBNET }}"
            PROJECT_ID="${{ vars.STAGING_GCP_PROJECT_ID }}"
            REGION="${{ vars.STAGING_GCP_REGION }}"
          fi

          echo "Checking network: ${NETWORK}"
          echo "Checking subnet: ${SUBNET}"
          echo "In project: ${PROJECT_ID}"
          echo "In region: ${REGION}"

          # Verify network exists
          if gcloud compute networks describe "${NETWORK}" --project="${PROJECT_ID}" >/dev/null 2>&1; then
            echo "✅ Network '${NETWORK}' exists"
          else
            echo "❌ Network '${NETWORK}' not found!"
            echo "Available networks:"
            gcloud compute networks list --project="${PROJECT_ID}"
            exit 1
          fi

          # Verify subnet exists
          if gcloud compute networks subnets describe "${SUBNET}" --region="${REGION}" --project="${PROJECT_ID}" >/dev/null 2>&1; then
            echo "✅ Subnet '${SUBNET}' exists in region ${REGION}"
            
            # Get subnet details
            SUBNET_RANGE=$(gcloud compute networks subnets describe "${SUBNET}" \
              --region="${REGION}" \
              --project="${PROJECT_ID}" \
              --format="value(ipCidrRange)")
            echo "📊 Subnet IP Range: ${SUBNET_RANGE}"
          else
            echo "❌ Subnet '${SUBNET}' not found in region ${REGION}!"
            echo "Available subnets in ${REGION}:"
            gcloud compute networks subnets list --project="${PROJECT_ID}" --filter="region:${REGION}"
            exit 1
          fi
      - name: '🚀 Deploy to Cloud Run (Dynamic Environment)'
        id: deploy
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: ${{ needs.setup.outputs.service_name }}
          region: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_REGION || vars.STAGING_GCP_REGION }}
          image: ${{ env.IMAGE_BASE }}:${{ github.sha }}

          # existing environment variables...
          env_vars: |
            NODE_ENV=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_NODE_ENV || vars.STAGING_NODE_ENV }}
            DATABASE_HOSTNAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_HOSTNAME || vars.STAGING_DATABASE_HOSTNAME }}
            DATABASE_USER=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_USER || vars.STAGING_DATABASE_USER }}
            DATABASE_PASSWORD=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_PASSWORD || vars.STAGING_DATABASE_PASSWORD }}
            DATABASE_NAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_NAME || vars.STAGING_DATABASE_NAME }}
            DATABASE_PORT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_DATABASE_PORT || vars.STAGING_DATABASE_PORT }}
            DATABASE_URL=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_URL || secrets.STAGING_DATABASE_URL }}
            JWT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_JWT_SECRET || secrets.STAGING_JWT_SECRET }}
            SENTRY_DSN=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENTRY_DSN || secrets.STAGING_SENTRY_DSN }}
            GATEWAY_PORT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GATEWAY_PORT || vars.STAGING_GATEWAY_PORT }}
            USER_ACTIVATION_REDIRECT_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_USER_ACTIVATION_REDIRECT_URL || vars.STAGING_USER_ACTIVATION_REDIRECT_URL }}
            GOOGLE_PROJECT_ID=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_PROJECT_ID || vars.STAGING_GCP_PROJECT_ID }}
            GOOGLE_CLIENT_ID=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_ID || secrets.STAGING_GOOGLE_CLIENT_ID }}
            GOOGLE_CLIENT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_SECRET || secrets.STAGING_GOOGLE_CLIENT_SECRET }}
            FILES_MANAGER=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_FILES_MANAGER || vars.STAGING_FILES_MANAGER }}
            GOOGLE_BUCKET_NAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GOOGLE_BUCKET_NAME || vars.STAGING_GOOGLE_BUCKET_NAME }}
            PASSWORD_SALT_ROUNDS=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_PASSWORD_SALT_ROUNDS || vars.STAGING_PASSWORD_SALT_ROUNDS }}
            SENDGRID_API_KEY=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENDGRID_API_KEY || secrets.STAGING_SENDGRID_API_KEY }}
            FILES_BASE_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_FILES_BASE_URL || vars.STAGING_FILES_BASE_URL }}
            API_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_API_URL || vars.STAGING_API_URL }}
            WEB_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_WEB_URL || vars.STAGING_WEB_URL }}
            GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL }}
            VERSION=${{ github.sha }}
            BRANCH=${{ github.ref_name }}
            ENVIRONMENT=${{ needs.setup.outputs.environment }}
            DEPLOYED_AT=${{ github.event.head_commit.timestamp }}
            BUILD_NUMBER=${{ github.run_number }}

          # flags: '--cpu=${{ needs.setup.outputs.cpu_limit }} --memory=${{ needs.setup.outputs.memory_limit }} --concurrency=80 --max-instances=${{ needs.setup.outputs.max_instances }} --allow-unauthenticated'
          # flags: >
          #   --cpu=${{ needs.setup.outputs.cpu_limit }}
          #   --memory=${{ needs.setup.outputs.memory_limit }}
          #   --concurrency=80
          #   --max-instances=${{ needs.setup.outputs.max_instances }}
          #   --allow-unauthenticated
          flags: >
            --cpu=${{ needs.setup.outputs.cpu_limit }}
            --memory=${{ needs.setup.outputs.memory_limit }}
            --concurrency=80
            --max-instances=${{ needs.setup.outputs.max_instances }}
            --allow-unauthenticated
            --network=${{ (needs.setup.outputs.environment == 'production') && vars.PRODUCTION_VPC_NETWORK || vars.STAGING_VPC_NETWORK }}
            --subnet=${{ (needs.setup.outputs.environment == 'production') && vars.PRODUCTION_VPC_SUBNET || vars.STAGING_VPC_SUBNET }}
            --vpc-egress=private-ranges-only

      - name: '🔍 Verify VPC configuration was applied'
        run: |
          echo "=== POST-DEPLOYMENT VPC VERIFICATION ==="

          SERVICE_NAME="${{ needs.setup.outputs.service_name }}"
          REGION="${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_REGION || vars.STAGING_GCP_REGION }}"

          echo "Checking VPC configuration for: ${SERVICE_NAME} in ${REGION}"

          # Get full service description
          gcloud run services describe "${SERVICE_NAME}" --region="${REGION}" --format="yaml" > service_config.yaml

          # Check for VPC annotations
          echo "🔍 Looking for VPC network annotations..."

          if grep -q "run.googleapis.com/network-interfaces" service_config.yaml; then
            echo "✅ VPC network interfaces found:"
            grep "run.googleapis.com/network-interfaces" service_config.yaml
          else
            echo "❌ No VPC network interfaces found!"
          fi

          if grep -q "run.googleapis.com/vpc-access-egress" service_config.yaml; then
            echo "✅ VPC egress configuration found:"
            grep "run.googleapis.com/vpc-access-egress" service_config.yaml
          else
            echo "❌ No VPC egress configuration found!"
          fi

          echo ""
          echo "📋 Full service configuration:"
          cat service_config.yaml

      - name: '🧪 Comprehensive smoke tests'
        run: |
          echo "=== COMPREHENSIVE SMOKE TESTS ==="

          if [ -n "${{ steps.deploy.outputs.url }}" ]; then
            SERVICE_URL="${{ steps.deploy.outputs.url }}"
            echo "Testing service: ${SERVICE_URL}"
            echo "Environment: ${{ needs.setup.outputs.environment }}"
            
            # Wait for service to be ready
            echo "Waiting 30 seconds for service startup..."
            sleep 30
            
            # Basic connectivity test
            echo "🧪 Testing basic connectivity..."
            if curl -f -s "${SERVICE_URL}" > /dev/null; then
              echo "✅ Service responding to HTTP requests"
            else
              echo "⚠️ Service not responding (checking status...)"
              curl -I "${SERVICE_URL}" || echo "Could not get response headers"
            fi
            
            # Environment-specific tests
            if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
              echo "🔴 Running PRODUCTION-specific tests..."
              echo "- Higher resource limits: ${{ needs.setup.outputs.cpu_limit }} CPU, ${{ needs.setup.outputs.memory_limit }} RAM"
              echo "- Max instances: ${{ needs.setup.outputs.max_instances }}"
            else
              echo "🟡 Running STAGING/DEV tests..."
              echo "- Standard resource limits: ${{ needs.setup.outputs.cpu_limit }} CPU, ${{ needs.setup.outputs.memory_limit }} RAM"
            fi
            
            # Test health endpoint
            echo "🧪 Testing health endpoint..."
            curl -f -s "${SERVICE_URL}/health" > /dev/null && echo "✅ Health endpoint responding" || echo "⚠️ Health endpoint not available"
            
            # Test with specific headers
            echo "🧪 Testing with headers..."
            RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "${SERVICE_URL}")
            echo "HTTP Response Code: ${RESPONSE}"
            
          else
            echo "❌ No service URL available for testing"
          fi

      - name: '🔥 Hotfix-specific actions'
        if: startsWith(github.ref_name, 'hotfix/')
        run: |
          echo "=== HOTFIX DEPLOYMENT COMPLETED ==="
          echo "🚨 HOTFIX: ${{ github.ref_name }}"
          echo "🎯 Deployed to: ${{ needs.setup.outputs.environment }}"
          echo "🔗 URL: ${{ steps.deploy.outputs.url }}"
          echo ""
          echo "📋 HOTFIX CHECKLIST:"
          echo "  1. ✅ Deployed to staging for testing"
          echo "  2. ⏳ Test thoroughly in staging"
          echo "  3. ⏳ If successful, deploy to production"
          echo "  4. ⏳ Monitor production deployment"
          echo "  5. ⏳ Merge hotfix to main after verification"

      - name: '🎉 Production deployment celebration'
        if: needs.setup.outputs.environment == 'production'
        run: |
          echo "=== 🚀 PRODUCTION DEPLOYMENT SUCCESSFUL! 🚀 ==="
          echo "🔴 Environment: PRODUCTION"
          echo "🌐 Service URL: ${{ steps.deploy.outputs.url }}"
          echo "📦 Image: ${{ env.IMAGE_BASE }}:${{ github.sha }}"
          echo "🏷️ Tags: ${{ needs.setup.outputs.docker_tags }}"
          echo "⚡ Resources: ${{ needs.setup.outputs.cpu_limit }} CPU, ${{ needs.setup.outputs.memory_limit }} RAM"
          echo "📈 Max Instances: ${{ needs.setup.outputs.max_instances }}"
          echo ""
          echo "🎯 PRODUCTION DEPLOYMENT COMPLETE!"

      - name: '✅ Final Summary'
        run: |
          echo "## 🏆 PRODUCTION PIPELINE COMPLETE! 🏆" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🎯 Deployment Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: ${{ needs.setup.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Service**: ${{ needs.setup.outputs.service_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **URL**: ${{ steps.deploy.outputs.url }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Image**: ${{ env.IMAGE_BASE }}:${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tags**: ${{ needs.setup.outputs.docker_tags }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🎛️ Configuration" >> $GITHUB_STEP_SUMMARY
          echo "- **CPU**: ${{ needs.setup.outputs.cpu_limit }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Memory**: ${{ needs.setup.outputs.memory_limit }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Max Instances**: ${{ needs.setup.outputs.max_instances }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Build Number**: ${{ github.run_number }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### ✅ All Operations Successful" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Dynamic environment detection" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Docker build and tagging" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Push to Artifact Registry" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Cloud Run deployment" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Smoke tests completed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ needs.setup.outputs.environment }}" == "production" ]; then
            echo "### 🔴 PRODUCTION DEPLOYMENT COMPLETED" >> $GITHUB_STEP_SUMMARY
            echo "Your application is now live in production! 🚀" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ startsWith(github.ref_name, 'hotfix/') }}" == "true" ]; then
            echo "### 🔥 HOTFIX DEPLOYED TO STAGING" >> $GITHUB_STEP_SUMMARY
            echo "Test thoroughly, then deploy to production when ready." >> $GITHUB_STEP_SUMMARY
          else
            echo "### 🟡 STAGING/DEV DEPLOYMENT COMPLETED" >> $GITHUB_STEP_SUMMARY
            echo "Ready for testing and validation." >> $GITHUB_STEP_SUMMARY
          fi

  # Slack notification for successful deployment
  notify-success:
    runs-on: ubuntu-latest
    needs: [notify-start, setup, deploy]
    if: success() && needs.setup.outputs.deploy == 'true'
    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      - name: '🎉 Notify deployment success'
        uses: './.github/actions/unified-slack-notify'
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: 'deployment-success'
          thread-ts: ${{ needs.notify-start.outputs.thread-ts }}
          data: '{"branch": "${{ github.ref_name }}", "commit": "${{ github.sha }}", "author": "${{ github.actor }}", "message": "${{ github.event.head_commit.message || ''Manual deployment trigger'' }}", "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", "environment": "${{ needs.setup.outputs.environment }}", "serviceName": "${{ needs.setup.outputs.service_name }}", "serviceUrl": "${{ needs.deploy.outputs.url || ''N/A'' }}", "dockerTags": "${{ needs.setup.outputs.docker_tags }}", "resources": {"cpu": "${{ needs.setup.outputs.cpu_limit }}", "memory": "${{ needs.setup.outputs.memory_limit }}", "maxInstances": "${{ needs.setup.outputs.max_instances }}"}, "buildNumber": "${{ github.run_number }}", "duration": "${{ github.event.workflow_run.conclusion && github.event.workflow_run.updated_at || ''N/A'' }}"}'

  # Slack notification for failed deployment
  notify-failure:
    runs-on: ubuntu-latest
    needs: [notify-start, setup, deploy]
    if: failure() && needs.setup.outputs.deploy == 'true'
    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      - name: '❌ Notify deployment failure'
        uses: './.github/actions/unified-slack-notify'
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          notification-type: 'deployment-failure'
          thread-ts: ${{ needs.notify-start.outputs.thread-ts }}
          data: '{"branch": "${{ github.ref_name }}", "commit": "${{ github.sha }}", "author": "${{ github.actor }}", "message": "${{ github.event.head_commit.message || ''Manual deployment trigger'' }}", "workflowUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", "environment": "${{ needs.setup.outputs.environment }}", "serviceName": "${{ needs.setup.outputs.service_name }}", "buildNumber": "${{ github.run_number }}", "failedJob": "${{ github.job }}"}'
