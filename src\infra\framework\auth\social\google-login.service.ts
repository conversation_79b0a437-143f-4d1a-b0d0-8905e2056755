import { SocialOauth2 } from '@/core/abstracts/social-login';
import { TokenManager } from '@/core/abstracts/token-manager';
import { InvalidCredentialException } from '@/core/exceptions/opus-exceptions';
import { SocialDataDto } from '@/shared/dtos/auth/social-data.dto';
import { Inject, Injectable } from '@nestjs/common';
import { GaxiosError } from 'gaxios';
import {
  TokenPayload as GoogleTokenPayload,
  OAuth2Client,
} from 'google-auth-library';

@Injectable()
export class GoogleLoginService implements SocialOauth2 {
  static GOOGLE_LOGIN_OAUTH2CLIENT_KEY = 'google-login-oauth2client-key';

  constructor(
    private readonly tokenManager: TokenManager,
    @Inject(GoogleLoginService.GOOGLE_LOGIN_OAUTH2CLIENT_KEY)
    private readonly googleOauth2Client: OAuth2Client,
  ) {}

  async verifyLogin(token: string): Promise<SocialDataDto> {
    let idToken: string | null | undefined;
    let refreshToken: string | null;

    try {
      const tokens = await this.googleOauth2Client.getToken(token);
      idToken = tokens.tokens.id_token;
      refreshToken = tokens.tokens.refresh_token ?? null;
    } catch (error) {
      if (error instanceof GaxiosError) {
        throw new InvalidCredentialException(error.message);
      }
      throw error;
    }

    if (!idToken) {
      throw new InvalidCredentialException('No id token from Google');
    }

    const { email, picture } =
      await this.tokenManager.decode<GoogleTokenPayload>(idToken);

    if (!email) {
      throw new InvalidCredentialException('No email from Google');
    }

    return { email, pictureUrl: picture, refreshToken };
  }
}
