import { FindAttendanceFilterDto } from '@/shared/dtos/attendances/find-attendance-filter.dto';
import { OmitRelation, Repository } from '../base/repository';
import { AttendanceEntity } from '../domain/entities/attendance.entity';
import { FindAttendanceSortDto } from '@/shared/dtos/attendances/find-attendance-sort.dto';

export abstract class AttendancesRepository extends Repository<
  AttendanceEntity,
  FindAttendanceFilterDto,
  FindAttendanceSortDto
> {
  static removeRelationships(
    data: AttendanceEntity,
  ): OmitRelation<AttendanceEntity> {
    const { endPicture, startPicture, ...rest } = data;
    return rest;
  }
}
