import { UseCase } from '@/core/base/use-case';
import { CreateResourceMapper } from '@/core/domain/mappers/resources/create-resource.mapper';
import { CreatedResourceMapper } from '@/core/domain/mappers/resources/created-resource.mapper';
import { EntityConflictException } from '@/core/exceptions/opus-exceptions';
import { FilesRepository } from '@/core/repositories/files.repository';
import { ResourcesRepository } from '@/core/repositories/resources.repository';
import { CreateResourceInputDto } from '@/shared/dtos/resources/create-resource-input.dto';
import { CreatedResourceDto } from '@/shared/dtos/resources/created-resource.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

export class CreateResourceUseCase implements UseCase<CreatedResourceDto> {
  private createResourceMapper: CreateResourceMapper;
  private createdResourceMapper: CreatedResourceMapper;

  constructor(
    private readonly resourcesRepository: ResourcesRepository,
    private readonly filesRepository: FilesRepository,
  ) {
    this.createResourceMapper = new CreateResourceMapper();
    this.createdResourceMapper = new CreatedResourceMapper();
  }

  public async execute(
    data: CreateResourceInputDto,
    uploader: CreatedUserDetailsDto,
  ): Promise<CreatedResourceDto> {
    if (!uploader.accountId) {
      throw new EntityConflictException('User has no corresponding account');
    }

    const resourceEntity = await this.resourcesRepository.create(
      this.createResourceMapper.map({
        ...data,
        uploaderId: uploader.id,
        accountId: uploader.accountId,
      }),
    );

    const attachments = [];
    for (const attachmentId of data.attachmentIds) {
      attachments.push(
        await this.filesRepository.update(attachmentId, {
          resourceId: resourceEntity.id,
        }),
      );
    }

    return this.createdResourceMapper.map({
      ...resourceEntity,
      attachments: attachments,
    });
  }
}
