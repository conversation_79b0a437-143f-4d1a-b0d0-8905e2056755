import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

export const findProfileFilter = z
  .object({
    id: IdType,
    accountId: IdType,
    nameOrEmail: z.string(),
    isActivated: z.enum(['true', 'false']).transform((v) => {
      switch (v) {
        case 'true':
          return true;
        case 'false':
          return false;
      }
    }),
  })
  .partial();

export class FindProfileFilterDto extends createZodDto(findProfileFilter) {}
