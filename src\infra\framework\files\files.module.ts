import { FilesManager } from '@/core/abstracts/files-manager';
import { FilesRepository } from '@/core/repositories/files.repository';
import { FilesManagerModule } from '@/infra/data/files-manager/files-manager.module';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { DownloadFileUseCase } from '@/use-cases/files/download-file.use-case';
import { UploadFileUseCase } from '@/use-cases/files/upload-file.use-case';
import { Module } from '@nestjs/common';
import { FilesController } from './files.controller';

@Module({
  imports: [FilesManagerModule, PrismaModule],
  controllers: [FilesController],
  providers: [
    {
      provide: UploadFileUseCase,
      useFactory: (
        filesRepository: FilesRepository,
        filesManager: FilesManager,
      ) => new UploadFileUseCase(filesRepository, filesManager),
      inject: [FilesRepository, FilesManager],
    },
    {
      provide: DownloadFileUseCase,
      useFactory: (
        filesRepository: FilesRepository,
        filesManager: FilesManager,
      ) => new DownloadFileUseCase(filesRepository, filesManager),
      inject: [FilesRepository, FilesManager],
    },
  ],
})
export class FilesModule {}
