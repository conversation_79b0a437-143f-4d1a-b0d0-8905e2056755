import { Body, Controller, Get, Put } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { LeaveApprovalHierarchiesService } from './leave-approval-hierarchies.service';
import { UpdateTimeAwayApprovalHierarchy } from '@/shared/dtos/time-away-approval-hierarchies/time-away-approver.dto';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';

@Controller('leave-approval-hierarchies')
@ApiTags('leave-approval-hierarchies')
@ApiDefaultErrorMessage()
@SwaggerAuth()
export class LeaveApprovalHierarchiesController {
  constructor(
    private readonly leaveApprovalHierarchiesService: LeaveApprovalHierarchiesService,
  ) {}

  @Put()
  @ApiOperation({
    summary: 'Set the leave approval hierarchy for the account',
  })
  async updateApprovalHierarchy(
    @Body() input: UpdateTimeAwayApprovalHierarchy,
    @CurrentUser() user: CreatedUserDetailsDto,
  ) {
    return this.leaveApprovalHierarchiesService.setApprovalHierarchy(
      input,
      user,
    );
  }

  @Get()
  @ApiOperation({
    summary: 'Get the leave approval hierarchy for the account',
  })
  async getApprovalHierarchy(@CurrentUser() user: CreatedUserDetailsDto) {
    return this.leaveApprovalHierarchiesService.getApprovalHierarchy(user);
  }
}
