import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { paginationMeta } from '../pagination/page-meta.dto';
import { findProfileFilter } from './find-profile-filter.dto';
import { findProfileSort } from './find-profile-sort.dto';

export const findProfileQuery = z
  .object({
    filter: findProfileFilter,
    sort: z.object({ sort: findProfileSort }).partial(),
    paging: paginationMeta,
  })
  .partial();

export class FindProfileQueryDto extends createZodDto(findProfileQuery) {}
