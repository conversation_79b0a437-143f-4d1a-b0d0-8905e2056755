import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdResource } from './created-resource.dto';
import { z } from 'zod';

export const findResourceFilter = createdResource
  .pick({ id: true, accountId: true, resourceType: true })
  .extend({
    text: z.string().optional(),
    isPublished: z.enum(['true', 'false']).transform((v) => {
      switch (v) {
        case 'true':
          return true;
        case 'false':
          return false;
      }
    }),
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
  })
  .partial();

export class FindResourceFilterDto extends createZodDto(findResourceFilter) {}

export const findResourceFilterInput = findResourceFilter.omit({
  accountId: true,
});
export class FindResourceFilterInputDto extends createZodDto(
  findResourceFilterInput,
) {}
