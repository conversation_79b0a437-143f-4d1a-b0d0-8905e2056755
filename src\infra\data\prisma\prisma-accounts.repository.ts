import { EntityCount } from '@/core/base/entity';
import { PickRelation } from '@/core/base/repository';
import { AccountEntity } from '@/core/domain/entities/account.entity';
import { EntityConflictException } from '@/core/exceptions/opus-exceptions';
import { AccountsRepository } from '@/core/repositories/accounts.repository';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { Transactional, TransactionHost } from '@nestjs-cls/transactional';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { ExtendedTransactionalAdapterPrisma } from './prisma.service';

@Injectable()
export class PrismaAccountsRepository implements AccountsRepository {
  constructor(
    private readonly txHost: TransactionHost<ExtendedTransactionalAdapterPrisma>,
  ) {}
  async create(data: AccountEntity): Promise<AccountEntity> {
    try {
      return await this.txHost.tx.account.create({ data });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new EntityConflictException('Account name is already used');
        }
      }
      throw error;
    }
  }

  async findOne(filter: Partial<AccountEntity>): Promise<AccountEntity | null> {
    return this.txHost.tx.account.findFirst({ where: filter });
  }

  @Transactional()
  async findAll(
    filter: Partial<AccountEntity>,
    paginationMeta?: PaginationMetaDto,
    include?: PickRelation<AccountEntity> | undefined,
  ): Promise<EntityCount<AccountEntity>> {
    const data = await this.txHost.tx.account.findMany({
      where: filter,
      skip: paginationMeta
        ? (paginationMeta.page - 1) * paginationMeta.limit
        : undefined,
      take: paginationMeta ? paginationMeta.limit : undefined,
      include,
    });
    const count = await this.txHost.tx.account.count({ where: filter });
    return { data, count };
  }
  async update(): Promise<AccountEntity> {
    throw new Error('Method not implemented.');
  }
  async remove(): Promise<void> {
    throw new Error('Method not implemented.');
  }
}
