import { OpusBaseException } from '@/core/exceptions/opus-exceptions';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Catch,
  ExecutionContext,
  HttpException,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, catchError, throwError } from 'rxjs';
import { ZodError } from 'zod';
import { LoggerService } from '../logger/logger.service';
import { DefaultErrorMessageDto } from '@/shared/dtos/message/default-error-message.dto';

@Catch()
export class OpusExceptionsInterceptor implements NestInterceptor {
  private readonly logger = new LoggerService('ExceptionsInterceptor');

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error: any) => {
        this.logger.verbose(error?.stack ?? error);

        let statusCode = 500;
        const response: DefaultErrorMessageDto = {
          message: 'Internal server error',
          name: '<PERSON>rror',
        };

        if (error instanceof OpusBaseException) {
          statusCode = error.statusCode;

          if (error.message) {
            response.message = `${error.message}`;
          }
          response.name = error.name;
        } else if (error instanceof ZodError) {
          statusCode = 422;
          response.message = 'Validation Error';
          response.details = error.issues;
          response.name = error.name;
        } else if (error instanceof Error) {
          response.message = error.message;
          error.name = error.name;
        }

        return throwError(
          () => new HttpException(response, statusCode, { cause: error }),
        );
      }),
    );
  }
}
