import { Entity } from '@/core/base/entity';
import { FileEntity } from './file.entity';
import { UserEntity } from './user.entity';

export class AttendanceEntity extends Entity {
  id!: string;
  userId!: string;

  startLat!: number;
  startLong!: number;
  startDatetime!: Date;
  startPictureId!: string;
  startPicture!: FileEntity;

  endLat!: number | null;
  endLong!: number | null;
  endDatetime!: Date | null;
  endPictureId!: string | null;
  endPicture!: FileEntity | null;

  millis!: number;

  user!: Pick<UserEntity, 'timezone'>;
}
