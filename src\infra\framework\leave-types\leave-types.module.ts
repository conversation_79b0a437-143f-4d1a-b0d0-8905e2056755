import { TimeAwayTypesRepository } from '@/core/repositories/time-away-types.repository';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { FindAllTimeAwayTypesUseCase } from '@/use-cases/time-away-types/find-all-time-away-types.use-case';
import { Module } from '@nestjs/common';
import { LeaveTypesController } from './leave-types.controller';
import { LeaveTypesService } from './leave-types.service';

@Module({
  imports: [PrismaModule],
  controllers: [LeaveTypesController],
  providers: [
    {
      provide: FindAllTimeAwayTypesUseCase,
      useFactory: (repository: TimeAwayTypesRepository) =>
        new FindAllTimeAwayTypesUseCase(repository),
      inject: [TimeAwayTypesRepository],
    },
    LeaveTypesService,
  ],
})
export class LeaveTypesModule {}
