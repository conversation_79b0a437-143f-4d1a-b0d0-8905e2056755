import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { createdUser } from '../users/created-user.dto';
import { fileMeta } from '../files/file-meta.dto';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { ResourceType } from '@/core/enums/resource-type.enum';

export const createdResource = z.object({
  id: IdType,
  title: z.string(),
  content: z.string(),
  resourceType: z.nativeEnum(ResourceType),
  accountId: IdType,
  uploader: createdUser
    .pick({ id: true, firstName: true, lastName: true })
    .nullable(),
  attachments: fileMeta.array(),
  publishDate: z.string().datetime().nullable(),
  createdAt: z.string().datetime(),
});

export class CreatedResourceDto extends createZodDto(createdResource) {}
