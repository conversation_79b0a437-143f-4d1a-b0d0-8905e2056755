import { Mapper } from '@/core/base/mapper';
import { FileEntity } from '@/core/domain/entities/file.entity';
import { CreateFileInputDto } from '@/shared/dtos/files/create-file-input.dto';

export class CreateFileMapper extends Mapper<CreateFileInputDto, FileEntity> {
  public map(data: CreateFileInputDto): FileEntity {
    const file = new FileEntity();

    file.contentType = data.contentType;
    file.permission = data.permission;
    file.uploaderId = data.uploaderId;
    file.fileName = data.fileName;
    file.originalFileName = data.originalFileName;
    file.fileSize = data.fileSize;

    return file;
  }
}
