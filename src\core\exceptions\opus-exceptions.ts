export class OpusBaseException extends Error {
  name = 'OpusBaseException';
  statusCode = 500;

  constructor(message: string = '') {
    super(message);
  }
}

// Entity Related Exceptions
export class EntityNotFoundException extends OpusBaseException {
  name = 'EntityNotFoundException';
  statusCode = 404;

  constructor(message: string = 'Resource not found') {
    super(message);
  }
}
export class EntityConflictException extends OpusBaseException {
  name = 'EntityConflictException';
  statusCode = 409;
}

// Credentials Related Exceptions
export class InvalidCredentialException extends OpusBaseException {
  name = 'InvalidCredentialException';
  statusCode = 401;
}
export class InvalidJwtException extends OpusBaseException {
  name = 'InvalidJwtException';
  statusCode = 401;
}
export class InsufficientPermissionException extends OpusBaseException {
  name = 'InsufficientPermissionException';
  statusCode = 403;
}
export class InvalidPayloadException extends OpusBaseException {
  name = 'InvalidPayloadException';
  statusCode = 422;
}
