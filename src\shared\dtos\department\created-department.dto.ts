import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { IdType } from '../base/base.dto';
import { createdUser } from '../users/created-user.dto';

export const createdDepartment = z.object({
  id: IdType,
  departmentName: z.string(),
  accountId: IdType,

  headUser: createdUser
    .pick({ id: true, firstName: true, lastName: true })
    .nullable(),
});
export class CreatedDepartmentDto extends createZodDto(createdDepartment) {}

export const manyCreatedDepartments = z.object({
  departments: createdDepartment.array(),
});
export class ManyCreatedDepartmentsDto extends createZodDto(
  manyCreatedDepartments,
) {}
