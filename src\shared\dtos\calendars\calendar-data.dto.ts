import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { externalCalendarEvents } from './external-calendar-events.dto';
import { createdTimeAway } from '../time-aways/created-time-away.dto';
import { createdProfile } from '../profiles/created-profile.dto';

export const leavesCalendar = z
  .object({
    leave: createdTimeAway.pick({
      id: true,
      startDate: true,
      endDate: true,
      timeAwayTypeId: true,
    }),
    profile: createdProfile.pick({
      id: true,
      firstName: true,
      lastName: true,
      profilePicture: true,
    }),
  })
  .array();
export type LeavesCalendar = z.infer<typeof leavesCalendar>;

export const birthdaysCalendar = z
  .object({
    firstName: z.string(),
    lastName: z.string(),
    birthDate: z.string().date(),
    nextBirthday: z.string().date(),
    picUrl: z.string().url().nullable(),
  })
  .array();
export type BirthdaysCalendar = z.infer<typeof birthdaysCalendar>;

export const anniversariesCalendar = z
  .object({
    firstName: z.string(),
    lastName: z.string(),
    joinDate: z.string().date(),
    nextAnniversary: z.string().date(),
    years: z.number(),
    picUrl: z.string().url().nullable(),
  })
  .array();
export type AnniversariesCalendar = z.infer<typeof anniversariesCalendar>;

export const calendarEventsList = z
  .object({
    google: externalCalendarEvents.nullable(),
    googleHolidays: externalCalendarEvents.nullable(),
    leaves: leavesCalendar,
    birthdays: birthdaysCalendar,
    anniversaries: anniversariesCalendar,
  })
  .partial();

export class CalendarEventsListDto extends createZodDto(calendarEventsList) {}
