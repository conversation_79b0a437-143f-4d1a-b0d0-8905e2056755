import { UserEntity } from '@/core/domain/entities/user.entity';
import { FindUserFilterDto } from '@/shared/dtos/users/find-user-filter.dto';
import { OmitRelation, Repository } from '../base/repository';
import { FindUserSortDto } from '@/shared/dtos/users/find-user-sort.dto';

export abstract class UsersRepository extends Repository<
  UserEntity,
  FindUserFilterDto,
  FindUserSortDto
> {
  static removeRelationships(data: UserEntity): OmitRelation<UserEntity> {
    const { account, profilePicture, role, uploadedFiles, ...rest } = data;
    return rest;
  }
}
