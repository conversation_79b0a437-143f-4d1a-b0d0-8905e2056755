import { Module } from '@nestjs/common';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';

/**
 * HealthModule
 *
 * NestJS module that encapsulates all health monitoring functionality for the Opus Remote Backend.
 * This module provides a complete health monitoring solution including REST API endpoints,
 * service health checks, and comprehensive system diagnostics.
 *
 * **Module Components:**
 * - **HealthController**: REST API endpoints for health checks (/health, /health/ready, /health/live)
 * - **HealthService**: Core service performing actual health assessments
 * - **PrismaModule**: Database connectivity for health checks
 *
 * **Key Features:**
 * - Comprehensive health monitoring (database, memory, disk)
 * - Kubernetes-compatible readiness and liveness probes
 * - Public endpoints (no authentication required)
 * - Detailed diagnostic information and performance metrics
 * - Swagger/OpenAPI documentation
 *
 * **Usage in Application:**
 * This module should be imported into the main application module to enable
 * health monitoring capabilities. The health endpoints are essential for:
 *
 * - Container orchestration (Kubernetes health probes)
 * - Load balancer health checks
 * - Monitoring and alerting systems
 * - DevOps automation and deployment pipelines
 *
 * @module HealthModule
 *
 * @example
 * ```typescript
 * // Import in app.module.ts
 * @Module({
 *   imports: [
 *     // ... other modules
 *     HealthModule,
 *   ],
 * })
 * export class AppModule {}
 *
 * // Health endpoints will be available at:
 * // GET /health - Comprehensive health check
 * // GET /health/ready - Readiness probe
 * // GET /health/live - Liveness probe
 * ```
 */
@Module({
  imports: [PrismaModule],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}
