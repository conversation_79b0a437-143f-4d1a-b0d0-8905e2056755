import { UserStatusUpdateNotifier } from '@/core/abstracts/user-status-update-manager';
import { UseCase } from '@/core/base/use-case';
import { CreatedUserStatusMapper } from '@/core/domain/mappers/user-status/created-user-status.mapper';
import { UpdateUserStatusMapper } from '@/core/domain/mappers/user-status/update-user-status.mapper';
import { UserStatusesRepository } from '@/core/repositories/user-statuses.repository';
import { CreatedUserStatusDto } from '@/shared/dtos/user-statuses/created-user-status.dto';
import { UpdateUserStatusNoIdInputDto } from '@/shared/dtos/user-statuses/update-user-status-no-id-input.dto';

export class UpdateUserStatusUseCase implements UseCase<CreatedUserStatusDto> {
  private updateUserStatusMapper: UpdateUserStatusMapper;
  private createdUserStatusMapper: CreatedUserStatusMapper;

  constructor(
    private readonly repository: UserStatusesRepository,
    private readonly userStatusUpdateNotifier: UserStatusUpdateNotifier,
  ) {
    this.updateUserStatusMapper = new UpdateUserStatusMapper();
    this.createdUserStatusMapper = new CreatedUserStatusMapper();
  }

  public async execute(
    userId: string,
    data: UpdateUserStatusNoIdInputDto,
  ): Promise<CreatedUserStatusDto> {
    const entity = this.updateUserStatusMapper.map({ ...data, id: userId });

    const createdUserStatus = await this.repository.update(userId, entity);
    if (entity.until) {
      this.userStatusUpdateNotifier.resetUserStatus(userId, entity.until);
    }
    this.userStatusUpdateNotifier.notifyUserStatusUpdate(userId);
    return this.createdUserStatusMapper.map(createdUserStatus);
  }
}
