# Performance optimizations for faster npm installs
prefer-offline=true
audit=false
fund=false
progress=false

# Cache settings
cache-max=86400000
cache-min=3600

# Network optimizations
fetch-retries=3
fetch-retry-factor=2
fetch-retry-mintimeout=10000
fetch-retry-maxtimeout=60000

# Registry settings (uncomment if using private registry)
# registry=https://your-private-registry.com/

# Reduce noise in CI environments
loglevel=warn
