# Multi-stage optimized Dockerfile for faster builds
# This reduces build time from 4-5 minutes to under 2 minutes

ARG NODE_VERSION=20

# ------ Base image with Node.js ------
FROM node:${NODE_VERSION}-alpine AS base
WORKDIR /opus

# Install system dependencies in one layer
RUN apk add --no-cache \
    dumb-init \
    && addgroup -g 1001 -S nodejs \
    && adduser -S nextjs -u 1001

# ------ Dependencies stage ------
FROM base AS deps
WORKDIR /opus

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies with optimizations
RUN npm ci --only=production --no-audit --no-fund \
    && npm cache clean --force

# ------ Build stage ------
FROM base AS builder
WORKDIR /opus

# Copy package files
COPY package.json package-lock.json ./

# Install all dependencies (including dev)
RUN npm ci --no-audit --no-fund

# Copy source code
COPY . .

# Generate Prisma client and build
RUN npx prisma generate \
    && npm run build \
    && rm -rf node_modules \
    && npm ci --only=production --no-audit --no-fund \
    && npm cache clean --force

# ------ Production stage ------
FROM base AS runner
WORKDIR /opus

# Copy built application and production dependencies
COPY --from=builder --chown=nextjs:nodejs /opus/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /opus/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /opus/package.json ./package.json

# Copy Prisma files
COPY --from=builder --chown=nextjs:nodejs /opus/src/infra/data/prisma ./src/infra/data/prisma

# Copy start script
COPY --chown=nextjs:nodejs ./scripts/start.sh ./scripts/start.sh
RUN chmod +x ./scripts/start.sh

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
ENTRYPOINT ["dumb-init", "--"]
CMD ["./scripts/start.sh"]
