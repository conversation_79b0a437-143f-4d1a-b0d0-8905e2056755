import { PickRelation } from '@/core/base/repository';
import { UseCase } from '@/core/base/use-case';
import { AttendanceEntity } from '@/core/domain/entities/attendance.entity';
import { CreatedAttendanceMapper } from '@/core/domain/mappers/attendances/created-attendance.mapper';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { CreatedAttendanceDto } from '@/shared/dtos/attendances/created-attendance.dto';
import { PaginatedCreatedAttendancesDto } from '@/shared/dtos/attendances/created-attendances-paginated.dto';
import { FindAttendanceFilterDto } from '@/shared/dtos/attendances/find-attendance-filter.dto';
import { FindAttendanceSortDto } from '@/shared/dtos/attendances/find-attendance-sort.dto';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';

export class FindAttendancesUseCase
  implements UseCase<PaginatedCreatedAttendancesDto>
{
  private readonly createdAttendanceMapper: CreatedAttendanceMapper;

  constructor(private readonly attendancesRepository: AttendancesRepository) {
    this.createdAttendanceMapper = new CreatedAttendanceMapper();
  }

  public async execute(
    filter: FindAttendanceFilterDto,
    paginationMeta: PaginationMetaDto,
    include?: PickRelation<AttendanceEntity> | undefined,
    sort?: FindAttendanceSortDto,
  ): Promise<PaginatedCreatedAttendancesDto> {
    const { data, count } = await this.attendancesRepository.findAll(
      filter,
      paginationMeta,
      include,
      sort,
    );
    const { mapped, totalMillis } = data.reduce<{
      mapped: CreatedAttendanceDto[];
      totalMillis: number;
    }>(
      (prev, curr) => {
        prev.mapped.push(this.createdAttendanceMapper.map(curr));
        prev.totalMillis += curr.millis;
        return prev;
      },
      { mapped: [], totalMillis: 0 },
    );
    return {
      data: mapped,
      totalMillis,
      paging: getPaginationDetails(data, paginationMeta, count),
    };
  }
}
