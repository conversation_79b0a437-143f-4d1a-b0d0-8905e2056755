import { OpusRoles } from '@/core/enums/role.enum';
import {
  EntityConflictException,
  InsufficientPermissionException,
} from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { UpsertDepartmentInputDto } from '@/shared/dtos/department/create-department.dto';
import {
  CreatedDepartmentDto,
  ManyCreatedDepartmentsDto,
} from '@/shared/dtos/department/created-department.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { departmentMapper } from './department.mapper';

@Injectable()
export class DepartmentsService {
  static DEPARTMENT_INCLUDE = {
    headUser: {
      select: {
        user: { select: { id: true, firstName: true, lastName: true } },
      },
    },
  } satisfies Prisma.DepartmentInclude;

  constructor(private readonly prisma: PrismaService) {}

  async upsertDepartment(
    input: UpsertDepartmentInputDto,
    creator: CreatedUserDetailsDto,
  ): Promise<CreatedDepartmentDto> {
    if (creator.role.title !== OpusRoles.Admin) {
      throw new InsufficientPermissionException();
    }
    if (!creator.accountId) {
      throw new InsufficientPermissionException();
    }

    try {
      const upserted = await this.prisma.department.upsert({
        create: {
          departmentName: input.departmentName,
          accountId: creator.accountId,
          headId: input.headId,
        },
        where: { id: input.id ?? '' },
        update: {
          departmentName: input.departmentName,
          headId: input.headId,
        },
        include: {
          headUser: {
            select: {
              user: { select: { id: true, firstName: true, lastName: true } },
            },
          },
        },
      });
      return departmentMapper(upserted);
    } catch (error) {
      throw new EntityConflictException('Department head user is invalid');
    }
  }

  async getDepartments(
    user: CreatedUserDetailsDto,
  ): Promise<ManyCreatedDepartmentsDto> {
    if (!user.accountId) {
      throw new InsufficientPermissionException();
    }

    const departments = await this.prisma.department.findMany({
      where: { accountId: user.accountId },
      include: DepartmentsService.DEPARTMENT_INCLUDE,
    });

    return { departments: departments.map(departmentMapper) };
  }

  async deleteDepartment(id: string, user: CreatedUserDetailsDto) {
    if (user.role.title !== OpusRoles.Admin) {
      throw new InsufficientPermissionException();
    }
    if (!user.accountId) {
      throw new InsufficientPermissionException();
    }

    try {
      await this.prisma.department.delete({
        where: { id, accountId: user.accountId },
      });
    } catch (error) {
      console.log(error);
    }
  }
}
