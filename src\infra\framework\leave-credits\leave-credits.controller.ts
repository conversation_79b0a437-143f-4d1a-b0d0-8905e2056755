import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { PaginatedTotalUserTimeAwayCreditsDto } from '@/shared/dtos/time-away-credits/total-user-time-away-credits-paginated.dto';
import { CreateTimeAwayAdjustmentInputDto } from '@/shared/dtos/time-aways/create-time-away-adjustment-input.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { ApiDefaultErrorMessage } from '../auth/decorators/error.decorator';
import { SwaggerAuth } from '../auth/decorators/swagger-auth.decorator';
import { LeaveCreditsService } from './leave-credits.service';

@Controller('leave-credits')
@ApiTags('leave-credits')
@ApiDefaultErrorMessage()
@SwaggerAuth()
export class LeaveCreditsController {
  constructor(private readonly leaveCreditsService: LeaveCreditsService) {}

  @Post()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse()
  @ApiOperation({
    summary: 'Add leave credits to a user',
  })
  async addLeaveCredits(
    @Body() input: CreateTimeAwayAdjustmentInputDto,
    @CurrentUser() updater: CreatedUserDetailsDto,
  ) {
    return this.leaveCreditsService.addCredits(input, updater);
  }

  @Get()
  @ApiOperation({
    summary: 'Get leave credits of users',
    description: 'Get the leave credits of users under an account',
  })
  @ApiOkResponse({
    type: PaginatedTotalUserTimeAwayCreditsDto,
  })
  async getLeaveCredits(
    @Query() paging: PaginationMetaDto,
    @CurrentUser() requester: CreatedUserDetailsDto,
  ) {
    return this.leaveCreditsService.getManyLeaveCredits(paging, requester);
  }
}
