generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["omitApi", "views"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                   String  @id @default(uuid())
  accountName          String  @unique @map("account_name")
  attendanceWebhookUrl String? @map("attendance_webhook_url")

  department                Department[]
  users                     User[]
  resources                 Resource[]
  googleCalendarTokens      GoogleCalendarToken[]
  timeAwayApprovalHierarchy TimeAwayApprovalHierarchy?

  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  deletedAt     DateTime?      @map("deleted_at")
  timeAwayTypes TimeAwayType[]

  @@map("accounts")
}

model GoogleCalendarToken {
  accountId    String
  calendarId   String
  refreshToken String
  account      Account @relation(fields: [accountId], references: [id])

  @@id([accountId, calendarId])
  @@map("google_calendar_tokens")
}

model Department {
  id             String  @id @default(uuid())
  departmentName String  @map("department_name")
  accountId      String  @map("account_id")
  headId         String? @unique @map("head_id")

  account      Account       @relation(fields: [accountId], references: [id])
  headUser     Profile?      @relation(name: "head_profile", fields: [headId], references: [id], onDelete: SetNull)
  profiles     Profile[]
  profileInfos ProfileInfo[]

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("departments")
}

model Role {
  id          String @id @default(uuid())
  title       String @unique
  description String @default("A role")

  users             User[]
  timeAwayApprovers TimeAwayApprover[]

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("roles")
}

model User {
  id                 String    @id @default(uuid())
  email              String    @unique
  firstName          String    @map("first_name")
  lastName           String    @map("last_name")
  timezone           String    @default("UTC")
  password           String?
  accountId          String?   @map("account_id")
  roleId             String    @map("role_id")
  profilePictureId   String?   @unique @map("profile_picture_id")
  googleRefreshToken String?   @map("google_refresh_token")
  lastPasswordChange DateTime? @map("last_password_change")
  activatedAt        DateTime? @map("activated_at")

  account                 Account?                      @relation(fields: [accountId], references: [id])
  role                    Role                          @relation(fields: [roleId], references: [id])
  timeAways               TimeAway[]
  uploadedFiles           File[]                        @relation(name: "uploader")
  profilePicture          File?                         @relation(name: "profile_picture", fields: [profilePictureId], references: [id])
  attendances             Attendance[]
  userStatus              UserStatus?
  resources               Resource[]
  profile                 Profile?
  profileInfo             ProfileInfo?
  timeAwayAccrualPolicies TimeAwayAccrualPolicy[]
  timeAwayApprovers       TimeAwayApprover[]
  timeAwayApprovalHistory TimeAwayApprovalHistoryItem[]
  timeAwayFinalApprovals  TimeAway[]                    @relation(name: "time_away_final_approver_user")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("users")
}

model Profile {
  id                    String         @id @default(uuid())
  birthDate             DateTime?      @map("birth_date") @db.Date
  maritalStatus         MaritalStatus? @map("marital_status")
  gender                Gender?
  addressStreet1        String?        @map("address_street_1")
  addressStreet2        String?        @map("address_street_2")
  city                  String?
  province              String?
  postalCode            String?        @map("postal_code")
  country               String?
  workPhone             String?        @map("work_phone")
  mobilePhone           String?        @map("mobile_phone")
  homePhone             String?        @map("home_phone")
  personalEmail         String?        @map("personal_email")
  emergencyName         String?        @map("emergency_name")
  emergencyRelationship String?        @map("emergency_relationship")
  emergencyMobilePhone  String?        @map("emergency_mobile_phone")
  jobTitle              String?        @map("job_title")
  employmentStatus      String?        @map("employment_status")
  departmentId          String?        @map("department_id")
  contractId            String?        @unique @map("contract_id")

  department        Department? @relation(fields: [departmentId], references: [id])
  managedDepartment Department? @relation(name: "head_profile")
  user              User        @relation(fields: [id], references: [id], onDelete: Cascade)
  contract          File?       @relation(name: "contract", fields: [contractId], references: [id])
  joinDate          DateTime    @default(now()) @map("join_date") @db.Date

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  profileInfo ProfileInfo?

  @@map("profiles")
}

model TimeAway {
  id              String    @id @default(uuid())
  userId          String    @map("user_id")
  hours           Float[]
  startDate       DateTime  @map("start_date")
  endDate         DateTime  @map("end_date")
  timeAwayTypeId  String    @map("time_away_type_id")
  isApproved      Boolean?
  reason          String?
  approvalMessage String?
  isUserRequest   Boolean   @default(true)
  isSubtract      Boolean   @default(true)
  accrualDate     DateTime? @db.Date

  approvalHistory     TimeAwayApprovalHistoryItem[]
  finalApproverUserId String?
  finalApproverUser   User?                         @relation(name: "time_away_final_approver_user", fields: [finalApproverUserId], references: [id])

  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  timeAwayType TimeAwayType @relation(fields: [timeAwayTypeId], references: [id])

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("time_aways")
}

model TimeAwayType {
  id          String @id @default(uuid())
  description String @unique

  accountId String?
  account   Account? @relation(fields: [accountId], references: [id])

  timeAways TimeAway[]

  createdAt               DateTime                @default(now()) @map("created_at")
  updatedAt               DateTime                @updatedAt @map("updated_at")
  deletedAt               DateTime?               @map("deleted_at")
  timeAwayAccrualPolicies TimeAwayAccrualPolicy[]

  @@map("time_away_types")
}

model TimeAwayAccrualPolicy {
  id    String @id @default(uuid())
  title String @unique

  timeAwayTypeId String       @map("timeAway_type_id")
  timeAwayType   TimeAwayType @relation(fields: [timeAwayTypeId], references: [id])
  users          User[]

  accrualAmount Float @default(0)

  // Represents the start of accrual
  // DAY & 1 (1 day after join date)
  // MONTH & 1 (1 month after join date) 
  accrualStart     Int           @map("accrual_start")
  accrualStartUnit TimeAwayUnits @map("accrual_start_unit")

  accrualFrequency      AccrualFrequency @default(WEEKLY) @map("accrual_frequency")
  // Represents the day of accrual
  // WEEKLY & 1 (every sunday)
  // MONTHLY & 1 (every 1st of month) 
  accrualFrequencyStart Int              @map("accrual_frequency_start")

  carryOverRule       CarryOverRule @map("carry_over_rule")
  carryOverAmount     Float         @map("carry_over_amount")
  carryOverAmountUnit TimeAwayUnits @map("carry_over_amount_unit")
  carryOverDate       DateTime      @map("carry_over_date") @db.Date

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("time_away_accrual_policies")
}

enum TimeAwayUnits {
  HOURS
  DAYS
  WEEKS
  MONTHS
  YEARS
}

enum AccrualFrequency {
  WEEKLY
  MONTHLY
}

enum CarryOverRule {
  ONLY
  MAX
  MIN
}

model Resource {
  id           String       @id @default(uuid())
  resourceType ResourceType @default(Announcement) @map("resource_type")
  title        String
  content      String
  accountId    String       @map("account_id")
  uploaderId   String?      @map("uploader_id")

  attachments File[]
  uploader    User?     @relation(fields: [uploaderId], references: [id], onDelete: SetNull)
  account     Account   @relation(fields: [accountId], references: [id])
  publishDate DateTime? @map("publish_date")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("resources")
}

model File {
  id               String  @id @default(uuid())
  permission       String
  fileName         String  @unique @map("file_name")
  contentType      String  @map("content_type")
  uploaderId       String  @map("uploader_id")
  originalFileName String  @default("file") @map("original_file_name")
  fileSize         Int     @default(10) @map("file_size ")
  resourceId       String? @map("resource_id")

  uploader               User         @relation(name: "uploader", fields: [uploaderId], references: [id], onDelete: Cascade)
  pictureOwner           User?        @relation(name: "profile_picture")
  attendanceStartPicture Attendance?  @relation(name: "start_picture")
  attendanceEndPicture   Attendance?  @relation(name: "end_picture")
  resource               Resource?    @relation(fields: [resourceId], references: [id])
  contractOwner          Profile?     @relation(name: "contract")
  profileInfoContract    ProfileInfo? @relation(name: "profile_info_picture")
  profileInfoPicture     ProfileInfo? @relation(name: "profile_info_contract")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("files")
}

model Attendance {
  id             String    @id @default(uuid())
  userId         String    @map("user_id")
  startLat       Float     @map("start_lat")
  startLong      Float     @map("start_long")
  startDatetime  DateTime  @map("start_datetime")
  startPictureId String    @unique @map("start_picture_id")
  endLat         Float?    @map("end_lat")
  endLong        Float?    @map("end_long")
  endDatetime    DateTime? @map("end_datetime")
  endPictureId   String?   @unique @map("end_picture_id")

  startPicture File  @relation(name: "start_picture", fields: [startPictureId], references: [id], onDelete: Cascade)
  endPicture   File? @relation(name: "end_picture", fields: [endPictureId], references: [id], onDelete: Cascade)
  user         User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("attendances")
}

model UserStatus {
  id     String    @id @default(uuid())
  status String?
  emoji  String?
  until  DateTime?

  activity Activity @default(Offline)

  user        User         @relation(fields: [id], references: [id], onDelete: Cascade)
  profileInfo ProfileInfo?

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("user_status")
}

view ProfileInfo {
  id                    String         @id @default(uuid())
  firstName             String         @map("first_name")
  lastName              String         @map("last_name")
  birthDate             DateTime?      @map("birth_date") @db.Date
  maritalStatus         MaritalStatus? @map("marital_status")
  gender                Gender?
  addressStreet1        String?        @map("address_street_1")
  addressStreet2        String?        @map("address_street_2")
  city                  String?
  province              String?
  postalCode            String?        @map("postal_code")
  country               String?
  workEmail             String         @unique @map("work_email")
  workPhone             String?        @map("work_phone")
  mobilePhone           String?        @map("mobile_phone")
  homePhone             String?        @map("home_phone")
  personalEmail         String?        @map("personal_email")
  emergencyName         String?        @map("emergency_name")
  emergencyRelationship String?        @map("emergency_relationship")
  emergencyMobilePhone  String?        @map("emergency_mobile_phone")
  jobTitle              String?        @map("job_title")
  employmentStatus      String?        @map("employment_status")
  accountId             String?        @map("account_id")
  departmentId          String?        @map("department_id")
  contractId            String?        @unique @map("contract_id")
  profilePictureId      String?        @unique @map("profile_picture_id")
  createdAt             DateTime       @default(now()) @map("created_at")
  activatedAt           DateTime?      @map("activated_at")
  joinDate              DateTime       @default(now()) @map("join_date") @db.Date

  profilePicture File?       @relation(name: "profile_info_picture", fields: [profilePictureId], references: [id])
  contract       File?       @relation(name: "profile_info_contract", fields: [contractId], references: [id])
  department     Department? @relation(fields: [departmentId], references: [id])
  profile        Profile?    @relation(fields: [id], references: [id])
  user           User?       @relation(fields: [id], references: [id])
  userStatus     UserStatus? @relation(fields: [id], references: [id])

  @@map("profile_info")
}

enum ResourceType {
  Announcement
  Resource
}

enum Activity {
  Online
  Idle
  Offline
}

enum MaritalStatus {
  Single
  Married
  Separated
  Divorced
  Widowed
}

enum Gender {
  Male
  Female
}

model TimeAwayApprovalHierarchy {
  id String @id @default(uuid())

  approvers TimeAwayApprover[]

  account   Account                 @relation(fields: [accountId], references: [id])
  accountId String                  @unique @map("account_id")
  checkList TimeAwayCheckListItem[]

  @@map("time_away_approval_hierarchies")
}

model TimeAwayApprover {
  id String @id @default(uuid())

  approvalHierarchy   TimeAwayApprovalHierarchy? @relation(fields: [approvalHierarchyId], references: [id])
  approvalHierarchyId String?                    @map("time_away_approval_hierarchy_id")

  approverType            TimeAwayApproverType          @map("approver_type")
  approverUserId          String?                       @map("approver_user_id")
  approverUser            User?                         @relation(fields: [approverUserId], references: [id])
  approverRoleId          String?                       @map("approver_role_id")
  approverRole            Role?                         @relation(fields: [approverRoleId], references: [id])
  order                   Int                           @default(0)
  timeAwayApproverHistory TimeAwayApprovalHistoryItem[]

  @@map("time_away_approvers")
}

model TimeAwayCheckListItem {
  id       String @id @default(uuid())
  question String
  order    Int    @default(0)

  approvalHierarchy   TimeAwayApprovalHierarchy?    @relation(fields: [approvalHierarchyId], references: [id])
  approvalHierarchyId String?                       @map("time_away_approval_hierarchy_id")
  answers             TimeAwayCheckListItemAnswer[]

  @@map("time_away_check_list_item")
}

enum TimeAwayApproverType {
  DEPARTMENT_HEAD
  SPECIFIC_ROLE
  SPECIFIC_PERSON

  @@map("time_away_approver_types")
}

model TimeAwayApprovalHistoryItem {
  id String @id @default(uuid())

  message    String?
  isApproved Boolean? @map("is_approved")

  approverUserId String?          @map("approver_user_id")
  approverUser   User?            @relation(fields: [approverUserId], references: [id], onDelete: Cascade)
  approverId     String           @map("approver_id")
  approver       TimeAwayApprover @relation(fields: [approverId], references: [id])

  timeAwayId String?
  timeAway   TimeAway? @relation(fields: [timeAwayId], references: [id], onDelete: Cascade)

  createdAt        DateTime                      @default(now()) @map("created_at")
  updatedAt        DateTime                      @updatedAt @map("updated_at")
  deletedAt        DateTime?                     @map("deleted_at")
  checkListAnswers TimeAwayCheckListItemAnswer[]

  @@map("time_away_approval_history_item")
}

model TimeAwayCheckListItemAnswer {
  id String @id @default(uuid())

  checkListItemId String                      @map("check_list_id")
  checkListItem   TimeAwayCheckListItem       @relation(fields: [checkListItemId], references: [id], onDelete: Cascade)
  answer          Boolean?
  historyItemId   String                      @map("history_item_id")
  historyItem     TimeAwayApprovalHistoryItem @relation(fields: [historyItemId], references: [id], onDelete: Cascade)

  @@map("time_away_check_list_item_answers")
}
