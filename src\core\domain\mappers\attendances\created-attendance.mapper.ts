import { Mapper } from '@/core/base/mapper';
import { AttendanceEntity } from '@/core/domain/entities/attendance.entity';
import {
  createdAttendance,
  CreatedAttendanceDto,
} from '@/shared/dtos/attendances/created-attendance.dto';
import { FileMetaDto } from '@/shared/dtos/files/file-meta.dto';
import { startOfDayAtTimezone } from '@/shared/helpers/date';

export class CreatedAttendanceMapper
  implements Mapper<AttendanceEntity, CreatedAttendanceDto>
{
  public map(data: AttendanceEntity): CreatedAttendanceDto {
    const attendance: CreatedAttendanceDto = {
      id: data.id,
      userId: data.userId,
      day: startOfDayAtTimezone(
        data.startDatetime,
        data.user.timezone,
      ).toISOString(),
      millis: data.millis,
      in: {
        lat: data.startLat,
        long: data.startLong,
        datetime: data.startDatetime.toISOString(),
        picture: data.startPicture as FileMetaDto,
      },
      out:
        data.endLat !== null &&
        data.endLong !== null &&
        data.endDatetime &&
        data.endPictureId
          ? {
              lat: data.endLat,
              long: data.endLong,
              datetime: data.endDatetime && data.endDatetime.toISOString(),
              picture: data.endPicture as FileMetaDto,
            }
          : null,
    };

    return createdAttendance.parse(attendance);
  }
}
