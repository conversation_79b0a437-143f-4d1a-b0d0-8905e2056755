import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdTimeAway } from './created-time-away.dto';
import { z } from 'zod';
import { IdType } from '../base/base.dto';

export const findTimeAwayFilter = createdTimeAway
  .pick({
    id: true,
    userId: true,
    startDate: true,
    endDate: true,
    timeAwayTypeId: true,
  })
  .extend({
    accountId: IdType,
    isApproved: z.enum(['true', 'false', 'null']).transform((v) => {
      switch (v) {
        case 'true':
          return true;
        case 'false':
          return false;
        case 'null':
          return null;
      }
    }),
    isUserRequest: z.enum(['true', 'false']).transform((v) => {
      switch (v) {
        case 'true':
          return true;
        case 'false':
          return false;
      }
    }),
  })
  .partial();

export class FindTimeAwayFilterDto extends createZodDto(findTimeAwayFilter) {}
