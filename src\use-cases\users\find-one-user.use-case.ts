import { UseCase } from '@/core/base/use-case';
import { CreatedUserMapper } from '@/core/domain/mappers/users/created-user.mapper';
import { UsersRepository } from '@/core/repositories/users.repository';
import { CreatedUserDto } from '@/shared/dtos/users/created-user.dto';
import { FindUserFilterDto } from '@/shared/dtos/users/find-user-filter.dto';

export class FindOneUserUseCase implements UseCase<CreatedUserDto | null> {
  private createdUserMapper: CreatedUserMapper;

  constructor(private readonly repository: UsersRepository) {
    this.createdUserMapper = new CreatedUserMapper();
  }

  public async execute(
    filter: FindUserFilterDto,
  ): Promise<CreatedUserDto | null> {
    const user = await this.repository.findOne(filter, {
      profilePicture: true,
    });

    if (!user) {
      return null;
    }

    return this.createdUserMapper.map(user);
  }
}
