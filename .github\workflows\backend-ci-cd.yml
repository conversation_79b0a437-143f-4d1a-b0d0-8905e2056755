name: 'CI/CD Pipeline - with Single Repository Strategy'

on:
  push:
    branches: [main, staging, develop, 'hotfix/*']
  pull_request:
    branches: [staging]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
          - development

concurrency:
  group: '${{ github.workflow }}-${{ github.head_ref || github.ref }}-${{ inputs.environment || github.ref_name }}'
  cancel-in-progress: true

permissions:
  contents: 'read'
  id-token: 'write'
  deployments: 'write'

defaults:
  run:
    shell: 'bash'

env:
  IMAGE_NAME: opus-remote-backend # Single image name
  # Enable Docker BuildKit for faster builds
  DOCKER_BUILDKIT: 1
  BUILDKIT_PROGRESS: plain

jobs:
  # Determine deployment environment and settings
  setup:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.config.outputs.environment }}
      deploy: ${{ steps.config.outputs.deploy }}
      service_name: ${{ steps.config.outputs.service_name }}
      docker_tags: ${{ steps.config.outputs.docker_tags }}
      cpu_limit: ${{ steps.config.outputs.cpu_limit }}
      memory_limit: ${{ steps.config.outputs.memory_limit }}
      max_instances: ${{ steps.config.outputs.max_instances }}
      container_concurrency: ${{ steps.config.outputs.container_concurrency }}
      max_scale_annotation: ${{ steps.config.outputs.max_scale_annotation }}
      cpu_boost_annotation: ${{ steps.config.outputs.cpu_boost_annotation }}
      project_id: ${{ steps.config.outputs.project_id }}
      region: ${{ steps.config.outputs.region }}
      registry_url: ${{ steps.config.outputs.registry_url }}
    steps:
      - name: '🎯 Configure deployment settings'
        id: config
        run: |
          # Detect environment based on event and branch
          EVENT="${{ github.event_name }}"
          BRANCH="${{ github.ref_name }}"

          if [ "${EVENT}" = "workflow_dispatch" ]; then
            ENV="${{ inputs.environment }}"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "staging" ]; then
            ENV="staging"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "main" ]; then
            ENV="production"
          elif [ "${EVENT}" = "push" ] && [ "${BRANCH}" = "develop" ]; then
            ENV="development"
          elif [[ "${BRANCH}" == hotfix/* ]]; then
            ENV="staging" # Deploy hotfixes to staging
          else
            ENV="${BRANCH}"
          fi

          case "$ENV" in
            "production")
              echo "🚨 PRODUCTION ENVIRONMENT DETECTED"
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-production" >> $GITHUB_OUTPUT
              echo "docker_tags=production,v$(date +%Y%m%d)-${{ github.run_number }}" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=1Gi" >> $GITHUB_OUTPUT
              echo "max_instances=50" >> $GITHUB_OUTPUT
              echo "container_concurrency=80" >> $GITHUB_OUTPUT
              echo "max_scale_annotation=10" >> $GITHUB_OUTPUT
              echo "cpu_boost_annotation=true" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.PRODUCTION_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.PRODUCTION_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.PRODUCTION_GCP_REGION }}-docker.pkg.dev/${{ vars.PRODUCTION_GCP_PROJECT_ID }}/${{ vars.PRODUCTION_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "staging")
              echo "📍 STAGING ENVIRONMENT DETECTED"
              echo "environment=staging" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-staging" >> $GITHUB_OUTPUT
              echo "docker_tags=staging,latest" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=10" >> $GITHUB_OUTPUT
              echo "container_concurrency=20" >> $GITHUB_OUTPUT
              echo "max_scale_annotation=1" >> $GITHUB_OUTPUT
              echo "cpu_boost_annotation=false" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.STAGING_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.STAGING_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.STAGING_GCP_REGION }}-docker.pkg.dev/${{ vars.STAGING_GCP_PROJECT_ID }}/${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            "development")
              echo "🔧 DEVELOPMENT ENVIRONMENT DETECTED"
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "deploy=true" >> $GITHUB_OUTPUT
              echo "service_name=opus-remote-backend-dev-new" >> $GITHUB_OUTPUT
              echo "docker_tags=develop" >> $GITHUB_OUTPUT
              echo "cpu_limit=1000m" >> $GITHUB_OUTPUT
              echo "memory_limit=512Mi" >> $GITHUB_OUTPUT
              echo "max_instances=3" >> $GITHUB_OUTPUT
              echo "container_concurrency=20" >> $GITHUB_OUTPUT
              echo "max_scale_annotation=1" >> $GITHUB_OUTPUT
              echo "cpu_boost_annotation=false" >> $GITHUB_OUTPUT
              echo "project_id=${{ vars.DEVELOPMENT_GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.DEVELOPMENT_GCP_REGION }}" >> $GITHUB_OUTPUT
              echo "registry_url=${{ vars.DEVELOPMENT_GCP_REGION }}-docker.pkg.dev/${{ vars.DEVELOPMENT_GCP_PROJECT_ID }}/${{ vars.DEVELOPMENT_GCP_DOCKER_REPO || 'opus-remote-backend' }}" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "❌ INVALID ENVIRONMENT: $ENV"
              echo "Valid environments: production, staging, development"
              echo "environment=none" >> $GITHUB_OUTPUT
              echo "deploy=false" >> $GITHUB_OUTPUT
              exit 1
              ;;
          esac
  # 🔒 SECURITY VALIDATION JOB
  security_check:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: '🛡️ Validate Environment Security'
        run: |
          ENV="${{ needs.setup.outputs.environment }}"

          echo "🔍 Validating environment: $ENV"

          # Ensure environment is one of the allowed values
          if [[ ! "$ENV" =~ ^(production|staging|development)$ ]]; then
            echo "❌ SECURITY ERROR: Invalid environment '$ENV'"
            exit 1
          fi

          # Production-specific security checks
          if [ "$ENV" = "production" ]; then
            echo "🚨 PRODUCTION DEPLOYMENT - Additional security checks"
            
            # Verify this is coming from main branch or manual dispatch
            if [ "${{ github.event_name }}" = "push" ] && [ "${{ github.ref_name }}" != "main" ]; then
              echo "❌ SECURITY ERROR: Production deployment must come from 'main' branch"
              exit 1
            fi
          fi

          echo "✅ Environment security validation passed"
  # build: Parallel build and dependency installation
  # build:
  #   runs-on: ubuntu-latest
  #   needs: setup
  # deploy : Full deployment job with dynamic environment support
  deploy:
    runs-on: ubuntu-latest
    needs: setup
    if: needs.setup.outputs.deploy == 'true'
    environment:
      name: ${{ needs.setup.outputs.environment }}
      url: ${{ steps.deploy.outputs.url }}

    steps:
      - name: '🔄 Checkout code'
        uses: 'actions/checkout@v4'

      # 🔒 SECURITY: Explicit environment-based authentication
      - name: '🔐 Authenticate to Google Cloud'
        uses: 'google-github-actions/auth@v2'
        with:
          workload_identity_provider: ${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GCP_WIF_PROVIDER || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_GCP_WIF_PROVIDER || secrets.STAGING_GCP_WIF_PROVIDER) }}
          service_account: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL) }}

      - name: '☁️ Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          project_id: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_PROJECT_ID || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GCP_PROJECT_ID || vars.STAGING_GCP_PROJECT_ID) }}

      - name: '🐳 Configure Docker for Artifact Registry'
        run: |
          ENV="${{ needs.setup.outputs.environment }}"

          case "$ENV" in
            "production")
              REGION="${{ vars.PRODUCTION_GCP_REGION }}"
              ;;
            "development")
              REGION="${{ vars.DEVELOPMENT_GCP_REGION }}"
              ;;
            "staging")
              REGION="${{ vars.STAGING_GCP_REGION }}"
              ;;
            *)
              echo "❌ Invalid environment for Docker config: $ENV"
              exit 1
              ;;
          esac

          gcloud auth configure-docker ${REGION}-docker.pkg.dev

      - name: '📦 Setup Node.js'
        if: hashFiles('package.json') != ''
        uses: 'actions/setup-node@v4'
        with:
          node-version-file: 'package.json'
          cache: 'npm'

      - name: '🏗️ Install dependencies and build'
        if: hashFiles('package.json') != ''
        run: |
          npm ci
          npm run build

      - name: '🔨 Build and tag Docker image'
        run: |
          ENV="${{ needs.setup.outputs.environment }}"

          case "$ENV" in
            "production")
              PROJECT_ID="${{ vars.PRODUCTION_GCP_PROJECT_ID }}"
              REGION="${{ vars.PRODUCTION_GCP_REGION }}"
              REPO_NAME="${{ vars.PRODUCTION_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
              ;;
            "development")
              PROJECT_ID="${{ vars.DEVELOPMENT_GCP_PROJECT_ID }}"
              REGION="${{ vars.DEVELOPMENT_GCP_REGION }}"
              REPO_NAME="${{ vars.DEVELOPMENT_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
              ;;
            "staging")
              PROJECT_ID="${{ vars.STAGING_GCP_PROJECT_ID }}"
              REGION="${{ vars.STAGING_GCP_REGION }}"
              REPO_NAME="${{ vars.STAGING_GCP_DOCKER_REPO || 'opus-remote-backend' }}"
              ;;
            *)
              echo "❌ Invalid environment for Docker build: $ENV"
              exit 1
              ;;
          esac

          # Build image once
          docker build -t ${{ env.IMAGE_NAME }}:temp .

          # Set base registry URL
          IMAGE_BASE="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${{ env.IMAGE_NAME }}"
          echo "IMAGE_BASE=${IMAGE_BASE}" >> $GITHUB_ENV

          # Tag with SHA
          docker tag ${{ env.IMAGE_NAME }}:temp ${IMAGE_BASE}:${{ github.sha }}

          # Tag with environment-specific tags
          IFS=',' read -ra TAGS <<< "${{ needs.setup.outputs.docker_tags }}"
          for tag in "${TAGS[@]}"; do
            docker tag ${{ env.IMAGE_NAME }}:temp ${IMAGE_BASE}:${tag}
          done

      - name: '📤 Push Docker image (Dynamic Environment)'
        run: |
          # Push SHA tag (always)
          docker push ${{ env.IMAGE_BASE }}:${{ github.sha }}

          # Push environment-specific tags
          IFS=',' read -ra TAGS <<< "${{ needs.setup.outputs.docker_tags }}"
          for tag in "${TAGS[@]}"; do
            docker push ${{ env.IMAGE_BASE }}:${tag}
          done

      - name: '🔍 Validate network configuration'
        run: |
          ENV="${{ needs.setup.outputs.environment }}"

          case "$ENV" in
            "production")
              NETWORK="${{ vars.PRODUCTION_VPC_NETWORK }}"
              SUBNET="${{ vars.PRODUCTION_VPC_SUBNET }}"
              ;;
            "development")
              NETWORK="${{ vars.DEVELOPMENT_VPC_NETWORK }}"
              SUBNET="${{ vars.DEVELOPMENT_VPC_SUBNET }}"
              ;;
            "staging")
              NETWORK="${{ vars.STAGING_VPC_NETWORK }}"
              SUBNET="${{ vars.STAGING_VPC_SUBNET }}"
              ;;
            *)
              echo "❌ Invalid environment for network validation: $ENV"
              exit 1
              ;;
          esac

          if [ -z "${NETWORK}" ] || [ "${NETWORK}" == "null" ]; then
            echo "❌ Network configuration missing for $ENV"
            exit 1
          fi

          if [ -z "${SUBNET}" ] || [ "${SUBNET}" == "null" ]; then
            echo "❌ Subnet configuration missing for $ENV"
            exit 1
          fi

      - name: '🚀 Deploy to Cloud Run (Dynamic Environment)'
        id: deploy
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: ${{ needs.setup.outputs.service_name }}
          region: ${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_REGION || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GCP_REGION || vars.STAGING_GCP_REGION) }}
          image: ${{ env.IMAGE_BASE }}:${{ github.sha }}

          # Environment-specific variables with proper secrets handling
          env_vars: |
            NODE_ENV=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_NODE_ENV || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_NODE_ENV || vars.STAGING_NODE_ENV) }}
            DATABASE_HOSTNAME=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_HOSTNAME || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_DATABASE_HOSTNAME || secrets.STAGING_DATABASE_HOSTNAME) }}
            DATABASE_USER=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_USER || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_DATABASE_USER || secrets.STAGING_DATABASE_USER) }}
            DATABASE_PASSWORD=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_PASSWORD || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_DATABASE_PASSWORD || secrets.STAGING_DATABASE_PASSWORD) }}
            DATABASE_NAME=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_NAME || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_DATABASE_NAME || secrets.STAGING_DATABASE_NAME) }}
            DATABASE_PORT=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_PORT || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_DATABASE_PORT || secrets.STAGING_DATABASE_PORT) }}
            DATABASE_URL=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_DATABASE_URL || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_DATABASE_URL || secrets.STAGING_DATABASE_URL) }}
            JWT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_JWT_SECRET || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_JWT_SECRET || secrets.STAGING_JWT_SECRET) }}
            SENTRY_DSN=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENTRY_DSN || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_SENTRY_DSN || secrets.STAGING_SENTRY_DSN) }}
            GATEWAY_PORT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GATEWAY_PORT || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GATEWAY_PORT || vars.STAGING_GATEWAY_PORT) }}
            USER_ACTIVATION_REDIRECT_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_USER_ACTIVATION_REDIRECT_URL || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_USER_ACTIVATION_REDIRECT_URL || vars.STAGING_USER_ACTIVATION_REDIRECT_URL) }}
            GOOGLE_PROJECT_ID=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_PROJECT_ID || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GCP_PROJECT_ID || vars.STAGING_GCP_PROJECT_ID) }}
            GOOGLE_CLIENT_ID=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_ID || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_GOOGLE_CLIENT_ID || secrets.STAGING_GOOGLE_CLIENT_ID) }}
            GOOGLE_CLIENT_SECRET=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_GOOGLE_CLIENT_SECRET || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_GOOGLE_CLIENT_SECRET || secrets.STAGING_GOOGLE_CLIENT_SECRET) }}
            FILES_MANAGER=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_FILES_MANAGER || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_FILES_MANAGER || vars.STAGING_FILES_MANAGER) }}
            GOOGLE_BUCKET_NAME=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GOOGLE_BUCKET_NAME || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GOOGLE_BUCKET_NAME || vars.STAGING_GOOGLE_BUCKET_NAME) }}
            PASSWORD_SALT_ROUNDS=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_PASSWORD_SALT_ROUNDS || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_PASSWORD_SALT_ROUNDS || vars.STAGING_PASSWORD_SALT_ROUNDS) }}
            SENDGRID_API_KEY=${{ needs.setup.outputs.environment == 'production' && secrets.PRODUCTION_SENDGRID_API_KEY || (needs.setup.outputs.environment == 'development' && secrets.DEVELOPMENT_SENDGRID_API_KEY || secrets.STAGING_SENDGRID_API_KEY) }}
            FILES_BASE_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_FILES_BASE_URL || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_FILES_BASE_URL || vars.STAGING_FILES_BASE_URL) }}
            API_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_API_URL || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_API_URL || vars.STAGING_API_URL) }}
            WEB_URL=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_WEB_URL || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_WEB_URL || vars.STAGING_WEB_URL) }}
            GOOGLE_CLOUD_RUN_SERVICE_ACCOUNT=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_GCP_SA_EMAIL || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_GCP_SA_EMAIL || vars.STAGING_GCP_SA_EMAIL) }}
            VERSION=${{ github.sha }}
            BRANCH=${{ github.ref_name }}
            ENVIRONMENT=${{ needs.setup.outputs.environment }}
            DEPLOYED_AT=${{ github.event.head_commit.timestamp }}
            BUILD_NUMBER=${{ github.run_number }}

          # Environment-specific flags for autoscaling, performance optimization, and networking
          flags: >
            --cpu=${{ needs.setup.outputs.cpu_limit }}
            --memory=${{ needs.setup.outputs.memory_limit }}
            --concurrency=${{ needs.setup.outputs.container_concurrency }}
            --max-instances=${{ needs.setup.outputs.max_instances }}
            --allow-unauthenticated
            --network=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_VPC_NETWORK || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_VPC_NETWORK || vars.STAGING_VPC_NETWORK) }}
            --subnet=${{ needs.setup.outputs.environment == 'production' && vars.PRODUCTION_VPC_SUBNET || (needs.setup.outputs.environment == 'development' && vars.DEVELOPMENT_VPC_SUBNET || vars.STAGING_VPC_SUBNET) }}
            --vpc-egress=private-ranges-only

      - name: '🧪 Smoke tests'
        run: |
          SERVICE_URL="${{ steps.deploy.outputs.url }}"
          HEALTH_FOUND=false

          # Health endpoint test (primary check)
          HEALTH_ENDPOINTS=("/health" "/healthz" "/ping" "/status")
          for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
            if curl -f -s "${SERVICE_URL}${endpoint}" > /dev/null 2>&1; then
              echo "✅ Health endpoint available: ${endpoint}"
              HEALTH_FOUND=true
              break
            fi
          done

          # Verify at least one health endpoint is working
          if [ "$HEALTH_FOUND" = false ]; then
            echo "❌ No health endpoints are accessible"
            exit 1
          fi

          # Additional API endpoint test (optional - don't fail if 404)
          if curl -f -s "${SERVICE_URL}/api" > /dev/null 2>&1; then
            echo "✅ API endpoint is accessible"
          else
            echo "ℹ️ Root/API endpoint returns 404 (expected for some services)"
          fi

          echo "✅ Smoke tests completed successfully"

      - name: '🎉 Production deployment success'
        if: needs.setup.outputs.environment == 'production'
        run: |
          echo "🚀 PRODUCTION DEPLOYMENT COMPLETED SUCCESSFULLY!"
          echo "📦 Service: ${{ needs.setup.outputs.service_name }}"
          echo "🔗 URL: ${{ steps.deploy.outputs.url }}"
          echo "🏷️ Version: ${{ github.sha }}"
          echo "📅 Deployed at: $(date -u +\"%Y-%m-%d %H:%M:%S UTC\")"
          echo "👤 Deployed by: ${{ github.actor }}"

      - name: '✅ Deployment Summary'
        run: |
          echo "✅ Deployment completed successfully!"
          echo "🎯 Environment: ${{ needs.setup.outputs.environment }}"
          echo "📦 Service: ${{ needs.setup.outputs.service_name }}"
          echo "🔗 URL: ${{ steps.deploy.outputs.url }}"
          echo "🌿 Branch: ${{ github.ref_name }}"
          echo "👤 Actor: ${{ github.actor }}"

  # Slack notification for deployment result
  notify:
    runs-on: ubuntu-latest
    needs: [setup, deploy]
    if: always() && needs.setup.outputs.deploy == 'true'
    steps:
      - name: '🎉 Notify deployment success'
        if: success()
        run: |
          ENVIRONMENT="${{ needs.setup.outputs.environment }}"
          ENV_EMOJI="🧪"
          if [[ "$ENVIRONMENT" == "production" ]]; then
            ENV_EMOJI="🚀"
          elif [[ "$ENVIRONMENT" == "staging" ]]; then
            ENV_EMOJI="🎭"
          fi

          COMMIT_SHORT="${{ github.sha }}"
          COMMIT_SHORT="${COMMIT_SHORT:0:7}"

          # Handle potentially null deployment URL
          DEPLOY_URL="${{ needs.deploy.outputs.url }}"
          if [[ -z "$DEPLOY_URL" || "$DEPLOY_URL" == "null" ]]; then
            # Determine region based on environment
            case "$ENVIRONMENT" in
              "production")
                REGION="${{ vars.PRODUCTION_GCP_REGION }}"
                PROJECT_ID="${{ vars.PRODUCTION_GCP_PROJECT_ID }}"
                ;;
              "development")
                REGION="${{ vars.DEVELOPMENT_GCP_REGION }}"
                PROJECT_ID="${{ vars.DEVELOPMENT_GCP_PROJECT_ID }}"
                ;;
              "staging")
                REGION="${{ vars.STAGING_GCP_REGION }}"
                PROJECT_ID="${{ vars.STAGING_GCP_PROJECT_ID }}"
                ;;
            esac
            DEPLOY_URL="https://console.cloud.google.com/run/detail/${REGION}/${{ needs.setup.outputs.service_name }}/metrics?project=${PROJECT_ID}"
          fi

          PAYLOAD=$(cat <<EOF
          {
            "text": "${ENV_EMOJI} Deployment Success - ${ENVIRONMENT^^}",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "${ENV_EMOJI} Deployment Success - ${ENVIRONMENT^^}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {"type": "mrkdwn", "text": "*Environment:*\\n${ENVIRONMENT}"},
                  {"type": "mrkdwn", "text": "*Service:*\\n${{ needs.setup.outputs.service_name }}"},
                  {"type": "mrkdwn", "text": "*Branch:*\\n${{ github.ref_name }}"},
                  {"type": "mrkdwn", "text": "*Commit:*\\n${COMMIT_SHORT}"},
                  {"type": "mrkdwn", "text": "*Author:*\\n${{ github.actor }}"},
                  {"type": "mrkdwn", "text": "*Build:*\\n#${{ github.run_number }}"}
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Resources:* CPU: ${{ needs.setup.outputs.cpu_limit }}, Memory: ${{ needs.setup.outputs.memory_limit }}, Max Instances: ${{ needs.setup.outputs.max_instances }}"
                }
              },
              {
                "type": "actions",
                "elements": [
                  {
                    "type": "button",
                    "text": {"type": "plain_text", "text": "View Service", "emoji": true},
                    "url": "${DEPLOY_URL}",
                    "style": "primary"
                  },
                  {
                    "type": "button",
                    "text": {"type": "plain_text", "text": "View Workflow", "emoji": true},
                    "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                ]
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "✅ Deployed at $(date -u +'%Y-%m-%d %H:%M:%S UTC') | Tags: ${{ needs.setup.outputs.docker_tags }}"
                  }
                ]
              }
            ]
          }
          EOF
          )

          curl -X POST -H 'Content-type: application/json' \
            --data "$PAYLOAD" \
            "${{ secrets.SLACK_WEBHOOK_URL }}" || echo "⚠️ Slack notification failed but continuing"

      - name: '❌ Notify deployment failure'
        if: failure()
        run: |
          ENVIRONMENT="${{ needs.setup.outputs.environment }}"
          ENV_EMOJI="🧪"
          if [[ "$ENVIRONMENT" == "production" ]]; then
            ENV_EMOJI="🚀"
          elif [[ "$ENVIRONMENT" == "staging" ]]; then
            ENV_EMOJI="🎭"
          fi

          COMMIT_SHORT="${{ github.sha }}"
          COMMIT_SHORT="${COMMIT_SHORT:0:7}"

          # Get commit message safely
          COMMIT_MESSAGE="${{ github.event.head_commit.message }}"
          if [[ -z "$COMMIT_MESSAGE" ]]; then
            COMMIT_MESSAGE="Manual deployment trigger"
          fi
          # Escape quotes in commit message for JSON
          COMMIT_MESSAGE=$(echo "$COMMIT_MESSAGE" | sed 's/"/\\"/g')

          PAYLOAD=$(cat <<EOF
          {
            "text": "❌ Deployment Failed - ${ENVIRONMENT^^}",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ Deployment Failed - ${ENVIRONMENT^^}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {"type": "mrkdwn", "text": "*Environment:*\\n${ENVIRONMENT}"},
                  {"type": "mrkdwn", "text": "*Service:*\\n${{ needs.setup.outputs.service_name }}"},
                  {"type": "mrkdwn", "text": "*Branch:*\\n${{ github.ref_name }}"},
                  {"type": "mrkdwn", "text": "*Commit:*\\n${COMMIT_SHORT}"},
                  {"type": "mrkdwn", "text": "*Author:*\\n${{ github.actor }}"},
                  {"type": "mrkdwn", "text": "*Build:*\\n#${{ github.run_number }}"}
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "🔍 *Failed Job:* deploy\\n📝 *Message:* ${COMMIT_MESSAGE}"
                }
              },
              {
                "type": "actions",
                "elements": [
                  {
                    "type": "button",
                    "text": {"type": "plain_text", "text": "View Logs", "emoji": true},
                    "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
                    "style": "danger"
                  }
                ]
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "❌ Failed at $(date -u +'%Y-%m-%d %H:%M:%S UTC')"
                  }
                ]
              }
            ]
          }
          EOF
          )

          curl -X POST -H 'Content-type: application/json' \
            --data "$PAYLOAD" \
            "${{ secrets.SLACK_WEBHOOK_URL }}" || echo "⚠️ Slack notification failed but continuing"\
