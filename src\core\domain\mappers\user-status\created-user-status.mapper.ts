import { Mapper } from '@/core/base/mapper';
import {
  createdUserStatus,
  CreatedUserStatusDto,
} from '@/shared/dtos/user-statuses/created-user-status.dto';
import { UserStatusEntity } from '../../entities/user-status.entity';

export class CreatedUserStatusMapper
  implements Mapper<UserStatusEntity, CreatedUserStatusDto>
{
  public map(data: UserStatusEntity): CreatedUserStatusDto {
    return createdUserStatus.parse({
      ...data,
      until: data.until ? data.until.toISOString() : null,
    });
  }
}
