import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { Module } from '@nestjs/common';
import { LeaveApprovalHierarchiesModule } from '../leave-approval-hierarchies/leave-approval-hierarchies.module';
import { LeaveCreditsModule } from '../leave-credits/leave-credits.module';
import { MailerModule } from '../mailer/mailer.module';
import { LeavesController } from './leaves.controller';
import { LeavesService } from './leaves.service';

@Module({
  imports: [
    MailerModule,
    PrismaModule,
    LeaveApprovalHierarchiesModule,
    LeaveCreditsModule,
  ],
  controllers: [LeavesController],
  providers: [LeavesService],
  exports: [LeavesService],
})
export class LeavesModule {}
