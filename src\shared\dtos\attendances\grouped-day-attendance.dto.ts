import { z } from 'zod';
import { paginateSchema } from '../pagination/paginated-base';
import {
  dayLog,
  groupedUserDayAttendance,
} from './grouped-user-day-attendance.dto';
import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { createdProfile } from '../profiles/created-profile.dto';

export enum DaylogStatus {
  present = 'present',
  onLeave = 'onLeave',
  pending = 'pending',
}
export const dayLogWithStatus = dayLog.extend({
  status: z.nativeEnum(DaylogStatus),
});

export type DayLogWithStatus = z.infer<typeof dayLogWithStatus>;

export const groupedUserDayAttendanceStatus = groupedUserDayAttendance.extend({
  dayLogs: dayLogWithStatus.array(),
});
export type GroupedUserDayAttendanceWithStatus = z.infer<
  typeof groupedUserDayAttendanceStatus
>;

export const dayAttendanceDetails = z.object({
  day: z.string().datetime(),
  present: z.number().int(),
  onLeave: z.number().int(),
  pending: z.number().int(),
  totalEmployees: z.number().int(),
});
export type DayAttendanceDetails = z.infer<typeof dayAttendanceDetails>;

export const paginatedGroupedDayAttendance = paginateSchema(
  groupedUserDayAttendanceStatus,
).extend({
  days: dayAttendanceDetails.array(),
  profiles: createdProfile
    .pick({
      id: true,
      firstName: true,
      lastName: true,
      department: true,
      profilePicture: true,
    })
    .array(),
});

export class PaginatedGroupedDayAttendanceDto extends createZodDto(
  paginatedGroupedDayAttendance,
) {}
