import { PasswordHasher } from '@/core/abstracts/password-hasher';
import { UserStatusUpdateNotifier } from '@/core/abstracts/user-status-update-manager';
import { AttendancesRepository } from '@/core/repositories/attendances.respository';
import { TimeAwaysRepository } from '@/core/repositories/time-aways.repository';
import { UserStatusesRepository } from '@/core/repositories/user-statuses.repository';
import { UsersRepository } from '@/core/repositories/users.repository';
import { PrismaModule } from '@/infra/data/prisma/prisma.module';
import { DeleteOneAttendanceUseCase } from '@/use-cases/attendances/delete-one-attendance.use-case';
import { FindOneAttendancesUseCase } from '@/use-cases/attendances/find-one-attendance.use-case';
import { GroupUserAttendancesByDayUseCase } from '@/use-cases/attendances/group-user-attendances-by-day.use-case';
import { UpsertAttendanceUseCase } from '@/use-cases/attendances/upsert-attendance.use-case';
import { DeleteTimeAwayUseCase } from '@/use-cases/time-aways/delete-time-away.use-case';
import { FindOneUserStatusUseCase } from '@/use-cases/user-status/find-one-user-status.use-case';
import { UpdateUserStatusUseCase } from '@/use-cases/user-status/update-user-status.use-case';
import { CreateUserUseCase } from '@/use-cases/users/create-user.use-case';
import { FindUsersUseCase } from '@/use-cases/users/find-users.use-case';
import { Module } from '@nestjs/common';
import { AttendancesModule } from '../attendances/attendances.module';
import { AttendancesService } from '../attendances/attendances.service';
import { HasherModule } from '../hasher/hasher.module';
import { LeaveApprovalHierarchiesModule } from '../leave-approval-hierarchies/leave-approval-hierarchies.module';
import { LeaveCreditsModule } from '../leave-credits/leave-credits.module';
import { LeavesModule } from '../leaves/leaves.module';
import { MailerModule } from '../mailer/mailer.module';
import { SocketsGateway } from '../sockets/sockets.gateway';
import { SocketsModule } from '../sockets/sockets.module';
import { UsersAttendanceController } from './users-attendance.controller';
import { UsersLeaveController } from './users-leave.controller';
import { UsersStatusController } from './users-status.controller';
import { UsersController } from './users.controller';

@Module({
  imports: [
    SocketsModule,
    MailerModule,
    AttendancesModule,
    HasherModule,
    PrismaModule,
    LeavesModule,
    LeaveCreditsModule,
    LeaveApprovalHierarchiesModule,
  ],
  controllers: [
    UsersController,
    UsersStatusController,
    UsersAttendanceController,
    UsersLeaveController,
  ],
  providers: [
    { provide: UserStatusUpdateNotifier, useExisting: SocketsGateway },

    {
      provide: CreateUserUseCase,
      useFactory: (
        repository: UsersRepository,
        passwordHasher: PasswordHasher,
      ) => new CreateUserUseCase(repository, passwordHasher),
      inject: [UsersRepository, PasswordHasher],
    },
    {
      provide: FindUsersUseCase,
      useFactory: (repository: UsersRepository) =>
        new FindUsersUseCase(repository),
      inject: [UsersRepository],
    },
    {
      provide: UpsertAttendanceUseCase,
      useFactory: (
        attendancesRepository: AttendancesRepository,
        attendancesService: AttendancesService,
      ) =>
        new UpsertAttendanceUseCase(attendancesRepository, attendancesService),
      inject: [AttendancesRepository, AttendancesService],
    },
    {
      provide: FindOneAttendancesUseCase,
      useFactory: (attendancesRepository: AttendancesRepository) =>
        new FindOneAttendancesUseCase(attendancesRepository),
      inject: [AttendancesRepository],
    },
    {
      provide: DeleteOneAttendanceUseCase,
      useFactory: (attendancesRepository: AttendancesRepository) =>
        new DeleteOneAttendanceUseCase(attendancesRepository),
      inject: [AttendancesRepository],
    },
    {
      provide: GroupUserAttendancesByDayUseCase,
      useFactory: (attendancesRepository: AttendancesRepository) =>
        new GroupUserAttendancesByDayUseCase(attendancesRepository),
      inject: [AttendancesRepository],
    },
    {
      provide: UpdateUserStatusUseCase,
      useFactory: (
        repository: UserStatusesRepository,
        userStatusUpdateNotifier: UserStatusUpdateNotifier,
      ) => new UpdateUserStatusUseCase(repository, userStatusUpdateNotifier),
      inject: [UserStatusesRepository, UserStatusUpdateNotifier],
    },
    {
      provide: FindOneUserStatusUseCase,
      useFactory: (repository: UserStatusesRepository) =>
        new FindOneUserStatusUseCase(repository),
      inject: [UserStatusesRepository],
    },
    {
      provide: DeleteTimeAwayUseCase,
      useFactory: (repository: TimeAwaysRepository) =>
        new DeleteTimeAwayUseCase(repository),
      inject: [TimeAwaysRepository],
    },
  ],
})
export class UsersModule {}
