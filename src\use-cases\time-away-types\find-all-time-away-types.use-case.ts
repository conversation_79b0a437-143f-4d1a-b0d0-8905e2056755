import { UseCase } from '@/core/base/use-case';
import { CreatedTimeAwayTypeMapper } from '@/core/domain/mappers/time-away-types/created-time-away-type.mapper';
import { TimeAwayTypesRepository } from '@/core/repositories/time-away-types.repository';
import { CreatedTimeAwayTypeListDto } from '@/shared/dtos/time-away-types/created-time-away-type-list.dto';

export class FindAllTimeAwayTypesUseCase
  implements UseCase<CreatedTimeAwayTypeListDto>
{
  private createdTimeAwayTypeMapper: CreatedTimeAwayTypeMapper;

  constructor(private readonly repository: TimeAwayTypesRepository) {
    this.createdTimeAwayTypeMapper = new CreatedTimeAwayTypeMapper();
  }

  public async execute(): Promise<CreatedTimeAwayTypeListDto> {
    const { data } = await this.repository.findAll({});

    const mapped = data.map((timeAwayType) =>
      this.createdTimeAwayTypeMapper.map(timeAwayType),
    );

    return {
      data: mapped,
    };
  }
}
