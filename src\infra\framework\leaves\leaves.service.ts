import { EmailSender } from '@/core/abstracts/email-sender';
import { OpusRoles } from '@/core/enums/role.enum';
import {
  EntityConflictException,
  EntityNotFoundException,
  InsufficientPermissionException,
  InvalidPayloadException,
} from '@/core/exceptions/opus-exceptions';
import { PrismaService } from '@/infra/data/prisma/prisma.service';
import { PaginationMetaDto } from '@/shared/dtos/pagination/page-meta.dto';
import { TotalUserTimeAwayCreditsDto } from '@/shared/dtos/time-away-credits/total-user-time-away-credits.dto';
import { CreateTimeAwayDto } from '@/shared/dtos/time-aways/create-time-away.dto';
import { CreatedTimeAwayDto } from '@/shared/dtos/time-aways/created-time-away.dto';
import { FinalizeTimeAwayInputDto } from '@/shared/dtos/time-aways/finalize-time-away.dto';
import { FindTimeAwayFilterDto } from '@/shared/dtos/time-aways/find-time-away-filter.dto';
import { FindUserTimeAwaySortDto } from '@/shared/dtos/time-aways/find-user-time-away-sort.dto';
import { GroupedLeavesFilterDto } from '@/shared/dtos/time-aways/grouped-leaves-filter.dto';
import {
  GroupedLeaves,
  leaveProfile,
  LeaveProfile,
  ManyGroupedLeavesDto,
} from '@/shared/dtos/time-aways/grouped-leaves.dto';
import { PaginatedTimeAwayHistoryDto } from '@/shared/dtos/time-aways/time-away-history-paginated.dto';
import { UpdateTimeAwayDto } from '@/shared/dtos/time-aways/update-time-away.dto';
import { CreatedUserDetailsDto } from '@/shared/dtos/users/created-user-details.dto';
import { eachDayOfIntervalAtTimezone } from '@/shared/helpers/date';
import { getPaginationDetails } from '@/shared/helpers/entities-to-paginated';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { LeaveApprovalHierarchiesService } from '../leave-approval-hierarchies/leave-approval-hierarchies.service';
import { LeaveCreditsService } from '../leave-credits/leave-credits.service';
import { MailBuilderService } from '../mailer/mail-builder.service';
import {
  leaveHistoryMapper,
  leaveMapper,
  LeaveMapperInput,
} from './leaves.mapper';

@Injectable()
export class LeavesService {
  static LEAVES_INCLUDE = {
    user: {
      include: {
        profileInfo: {
          include: {
            department: {
              include: {
                headUser: {
                  select: {
                    user: {
                      select: { id: true, firstName: true, lastName: true },
                    },
                  },
                },
              },
            },
            profilePicture: true,
          },
        },
      },
    },
    approvalHistory: {
      include: {
        checkListAnswers: { include: { checkListItem: true } },
        approver: {
          select: {
            id: true,
            approverUser: {
              select: { id: true, firstName: true, lastName: true },
            },
            approverRole: {
              select: { id: true, title: true },
            },
            approverType: true,
          },
        },
        approverUser: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
      orderBy: { approver: { order: 'asc' } },
    },
    timeAwayType: { select: { description: true } },
    finalApproverUser: {
      select: { id: true, firstName: true, lastName: true },
    },
  } satisfies Prisma.TimeAwayInclude;

  constructor(
    private readonly prisma: PrismaService,
    private readonly leaveApprovalHierarchiesService: LeaveApprovalHierarchiesService,
    private readonly mailBuilderService: MailBuilderService,
    private readonly emailSender: EmailSender,
    private readonly leaveCreditsService: LeaveCreditsService,
  ) {}

  private async __approveOverride(
    raw: LeaveMapperInput,
    input: FinalizeTimeAwayInputDto,
    actor: CreatedUserDetailsDto,
  ) {
    await this.prisma.timeAway.update({
      where: { id: raw.id },
      data: {
        isApproved: input.isApproved,
        approvalMessage: input.approvalMessage,
        finalApproverUserId: actor.id,
      },
    });

    const mailData = await this.mailBuilderService.buildFinalizedLeaveEmail(
      raw.user.email,
      actor.account?.accountName ?? '',
      raw.user.firstName,
      raw.timeAwayType.description,
      input.isApproved ? 'APPROVED' : 'REJECTED',
    );
    void this.emailSender.send(mailData);
  }

  private async __approveNormal(
    raw: LeaveMapperInput,
    input: FinalizeTimeAwayInputDto,
    actor: CreatedUserDetailsDto,
  ) {
    const timeAway = leaveMapper(raw);
    const currentApprover =
      timeAway.approvalHistory[timeAway.currentApproverIndex];

    // Final when NOT APPROVED or last approver
    const isFinal =
      !input.isApproved ||
      timeAway.currentApproverIndex === timeAway.approvalHistory.length - 1;

    await this.prisma.$transaction(async (tx) => {
      await tx.timeAwayApprovalHistoryItem.update({
        where: { id: currentApprover.id },
        data: {
          message: input.approvalMessage,
          isApproved: input.isApproved,
          approverUserId: actor.id,
        },
      });

      for (const answer of input.checkListAnswers ?? []) {
        try {
          await tx.timeAwayCheckListItemAnswer.update({
            where: {
              id: answer.id,
              historyItem: { timeAwayId: timeAway.id, id: currentApprover.id },
            },
            data: {
              answer: answer.answer,
            },
          });
        } catch (error) {}
      }

      if (!input.isApproved || isFinal) {
        await tx.timeAway.update({
          where: { id: timeAway.id },
          data: {
            isApproved: input.isApproved,
            approvalMessage: input.approvalMessage,
            finalApproverUserId: actor.id,
          },
        });
      }
    });

    // Send emails to next approvers when not approved

    const userIds: string[] = [];
    const roleIds: string[] = [];
    const where = {
      OR: [
        {
          id: { in: userIds },
        },
        {
          role: { id: { in: roleIds } },
        },
      ],
    } satisfies Prisma.UserWhereInput;
    for (
      let i = timeAway.currentApproverIndex + 1;
      i < timeAway.approvalHistory.length;
      i++
    ) {
      const nextApprover = timeAway.approvalHistory[i];

      switch (nextApprover.approverDetails.approverType) {
        case 'DEPARTMENT_HEAD':
        case 'SPECIFIC_PERSON':
          const nextUser = nextApprover.approverDetails.user;
          nextUser && userIds.push(nextUser.id);
          break;
        case 'SPECIFIC_ROLE':
          const nextRole = nextApprover.approverDetails.role;
          nextRole && roleIds.push(nextRole.id);
          break;
      }

      // Only send email to the next approver when APPROVED
      // Otherwise send to all
      if (input.isApproved) {
        break;
      }
    }

    const toEmailUsers = await this.prisma.user.findMany({
      where,
      select: { firstName: true, lastName: true, email: true },
    });

    for (const toEmailUser of toEmailUsers) {
      const mailData =
        await this.mailBuilderService.buildLeaveProcessedNotifEmail(
          actor.firstName,
          toEmailUser.email,
          toEmailUser.firstName,
          raw.user.firstName,
          raw.timeAwayType.description,
          input.isApproved ? 'APPROVED' : 'REJECTED',
        );
      void this.emailSender.send(mailData);
    }

    if (isFinal) {
      const mailData = await this.mailBuilderService.buildFinalizedLeaveEmail(
        raw.user.email,
        actor.account?.accountName ?? '',
        raw.user.firstName,
        raw.timeAwayType.description,
        input.isApproved ? 'APPROVED' : 'REJECTED',
      );
      void this.emailSender.send(mailData);
    }
  }

  async approve(
    timeAwayId: string,
    input: FinalizeTimeAwayInputDto,
    actor: CreatedUserDetailsDto,
  ) {
    if (!actor.accountId) {
      throw new InsufficientPermissionException();
    }
    const raw = await this.prisma.timeAway.findUnique({
      where: { id: timeAwayId },
      include: LeavesService.LEAVES_INCLUDE satisfies Prisma.TimeAwayInclude,
    });

    if (!raw) {
      throw new EntityNotFoundException();
    }

    const timeAway = leaveMapper(raw);

    if (timeAway.isApproved !== null) {
      throw new EntityConflictException('Leave has already been finalized.');
    }

    if (input.override) {
      if (actor.role.title !== OpusRoles.Admin) {
        throw new InsufficientPermissionException(
          'User cannot override approvals',
        );
      }

      await this.__approveOverride(raw, input, actor);
    } else {
      let isApprover = false;
      for (const approver of timeAway.approvalHistory) {
        if (approver.approverDetails.approverType === 'SPECIFIC_ROLE') {
          isApprover = actor.role.id === approver.approverDetails.role?.id;
        } else {
          isApprover = actor.id === approver.approverDetails.user?.id;
        }
        if (isApprover) {
          break;
        }
      }

      if (!isApprover) {
        throw new InsufficientPermissionException('User is not an approver');
      }

      await this.__approveNormal(raw, input, actor);
    }
  }

  async requestLeave(user: CreatedUserDetailsDto, input: CreateTimeAwayDto) {
    if (!user.account) {
      throw new InsufficientPermissionException();
    }

    // Check the length of hours array against the number of days
    const days = eachDayOfIntervalAtTimezone(input.startDate, input.endDate);

    if (days.length != input.hours.length) {
      throw new InvalidPayloadException(
        'The number of days must match the number of hours',
      );
    }

    const existing = await this.prisma.timeAway.findFirst({
      where: {
        userId: user.id,
        OR: [
          {
            AND: [
              {
                startDate: { lte: input.startDate },
              },
              { endDate: { gte: input.startDate } },
            ],
          },
          {
            AND: [
              {
                startDate: { lte: input.endDate },
              },
              { endDate: { gte: input.endDate } },
            ],
          },
        ],
      },
    });

    if (
      existing &&
      (existing.isApproved === true || existing.isApproved === null)
    ) {
      throw new EntityConflictException(
        'There is an existing leave request at the specified dates',
      );
    }

    const hierarchy =
      await this.leaveApprovalHierarchiesService.getApprovalHierarchy(user);

    const timeAway = await this.prisma.$transaction(async (tx) => {
      const timeAway = await tx.timeAway.create({
        data: {
          userId: user.id,
          startDate: input.startDate,
          endDate: input.endDate,
          hours: input.hours,
          timeAwayTypeId: input.timeAwayTypeId,
          reason: input.reason,
          approvalHistory: {
            createMany: {
              data: hierarchy.approvers.map((approver) => {
                return { approverId: approver.id };
              }),
            },
          },
        },
        include: { timeAwayType: true, approvalHistory: true },
      });

      for (const historyItem of timeAway.approvalHistory) {
        await tx.timeAwayApprovalHistoryItem.update({
          where: { id: historyItem.id },
          data: {
            checkListAnswers: {
              createMany: {
                data: hierarchy.checklist.map((item) => ({
                  checkListItemId: item.id,
                })),
              },
            },
          },
        });
      }
      return timeAway;
    });

    const mailData = await this.mailBuilderService.buildRequestLeaveEmail(
      user.email,
      user.account.accountName,
      user.firstName,
      timeAway.timeAwayType.description,
    );

    void this.emailSender.send(mailData);

    /**
     * DEFAULT notification behavior without hierarchy is ALL ADMINS
     */
    const where: Prisma.UserWhereInput = { accountId: user.account.id };

    if (hierarchy.approvers.length < 1) {
      where.role = { title: OpusRoles.Admin };
    } else {
      where.OR = [];
      for (const approver of hierarchy.approvers) {
        switch (approver.approver.approverType) {
          case 'DEPARTMENT_HEAD':
            user.profile.departmentId &&
              where.OR.push({
                profile: {
                  managedDepartment: {
                    id: user.profile.departmentId,
                  },
                },
              });
            break;
          case 'SPECIFIC_ROLE':
            where.OR.push({ roleId: approver.approver.roleId });
            break;
          case 'SPECIFIC_PERSON':
            approver.approver.approverType === 'SPECIFIC_PERSON';
            where.OR.push({ id: approver.approver.userId });
            break;
        }
      }
    }

    const admins = await this.prisma.user.findMany({
      where,
      select: {
        email: true,
        firstName: true,
      },
    });

    for (const admin of admins) {
      const adminMail =
        await this.mailBuilderService.buildRequestLeaveAdminNotifEmail(
          admin.email,
          admin.firstName,
          user.firstName,
          timeAway.timeAwayType?.description ?? '',
        );
      void this.emailSender.send(adminMail);
    }

    return timeAway;
  }

  async getLeavesAdminOverview(
    requester: CreatedUserDetailsDto,
    filter: GroupedLeavesFilterDto,
  ): Promise<ManyGroupedLeavesDto> {
    if (!requester.isLeaveApprover) {
      throw new InsufficientPermissionException('Insufficient permissions');
    }
    const where: Prisma.TimeAwayWhereInput = {
      ...filter,
      isUserRequest: true,
      user: {
        accountId: requester.accountId,
        activatedAt: { not: null },
      },
    };

    if (requester.role.title !== OpusRoles.Admin) {
      where.OR = [
        {
          approvalHistory: {
            some: {
              approver: {
                OR: [
                  { approverUserId: requester.id },
                  { approverRoleId: requester.role.id },
                ],
              },
            },
          },
        },
      ];

      if (requester.profile.managedDepartmentId) {
        where.OR.push({
          user: {
            profile: {
              departmentId: requester.profile.managedDepartmentId,
            },
          },
        });
      }
    }

    const leaves = await this.prisma.timeAway.findMany({
      where,
      orderBy: { startDate: 'asc' },
      include: LeavesService.LEAVES_INCLUDE,
    });

    const profiles: LeaveProfile[] = [];
    const credits: TotalUserTimeAwayCreditsDto[] = [];
    const groupedLeaves: GroupedLeaves = {
      done: [],
      pending: [],
      rejected: [],
      upcoming: [],
    };

    let upcoming = 0;
    let done = 0;
    let pending = 0;
    let rejected = 0;

    for (const leave of leaves) {
      const parsed = leaveMapper(leave);

      if (leave.isApproved === false) {
        groupedLeaves.rejected.push(parsed);
        rejected++;
      } else if (leave.isApproved === null) {
        groupedLeaves.pending.push(parsed);
        pending++;
      } else {
        if (new Date(leave.endDate) > new Date()) {
          groupedLeaves.upcoming.push(parsed);
          upcoming++;
        } else {
          groupedLeaves.done.push(parsed);
          done++;
        }
      }

      if (!profiles.find((profile) => profile.id === leave.userId)) {
        const userCredits = await this.leaveCreditsService.getLeaveCredits(
          leave.userId,
        );

        const profile = leave.user.profileInfo;
        const department = profile?.department;
        const headUser = department?.headUser?.user;
        const profilePicture = profile?.profilePicture;

        credits.push(userCredits);
        profiles.push(
          leaveProfile.parse({
            id: profile?.id,
            department: department
              ? {
                  id: department.id,
                  accountId: department.accountId,
                  departmentName: department.departmentName,
                  headUser: headUser ? headUser : null,
                }
              : null,
            firstName: profile?.firstName,
            lastName: profile?.lastName,
            jobTitle: profile?.jobTitle,
            profilePicture,
          }),
        );
      }
    }

    const countEmployees = (input: { userId: string }[]) =>
      new Set(input.map((item) => item.userId)).size;

    return {
      profiles,
      leaves: groupedLeaves,
      credits,
      count: {
        allRequests: {
          requests: upcoming + pending + done + rejected,
          employees: profiles.length,
        },
        upcoming: {
          requests: upcoming,
          employees: countEmployees(groupedLeaves.upcoming),
        },
        pending: {
          requests: pending,
          employees: countEmployees(groupedLeaves.pending),
        },
        done: {
          requests: done,
          employees: countEmployees(groupedLeaves.done),
        },
        rejected: {
          requests: rejected,
          employees: countEmployees(groupedLeaves.rejected),
        },
      },
    };
  }

  async paginateLeaves(
    input: Omit<Prisma.TimeAwayFindManyArgs, 'skip' | 'take' | 'include'>,
    pagination?: PaginationMetaDto,
  ) {
    return this.prisma.$transaction(async (tx) => {
      const data = await tx.timeAway.findMany({
        ...input,
        include: LeavesService.LEAVES_INCLUDE,
        skip: pagination ? (pagination.page - 1) * pagination.limit : undefined,
        take: pagination ? pagination.limit : undefined,
      });
      const count = await tx.timeAway.count({ where: input.where });

      return { data: data, count };
    });
  }

  async findUserLeaves(
    filter: FindTimeAwayFilterDto,
    pagination: PaginationMetaDto,
    sort?: FindUserTimeAwaySortDto,
  ) {
    const { data, count } = await this.paginateLeaves(
      {
        where: {
          ...filter,
          AND: [
            {
              startDate: { gte: filter.startDate },
            },
            { endDate: { lte: filter.endDate } },
          ],
        },
        orderBy: sort,
      },
      pagination,
    );

    const paging = getPaginationDetails(data, pagination, count);
    return { data: data.map(leaveMapper), paging };
  }

  async getLeavesHistory(
    userId: string,
    pagination: PaginationMetaDto,
  ): Promise<PaginatedTimeAwayHistoryDto> {
    const { data, count } = await this.paginateLeaves(
      { where: { userId }, orderBy: { startDate: 'desc' } },
      pagination,
    );
    const paging = getPaginationDetails(data, pagination, count);
    return { data: data.map(leaveHistoryMapper), paging };
  }

  public async updateLeave(
    user: CreatedUserDetailsDto,
    timeAwayId: string,
    input: UpdateTimeAwayDto,
  ): Promise<CreatedTimeAwayDto> {
    // Check the length of hours array against the number of days
    const days = eachDayOfIntervalAtTimezone(input.startDate, input.endDate);

    if (days.length != input.hours.length) {
      throw new InvalidPayloadException(
        'The number of days must match the number of hours',
      );
    }

    const existing = await this.prisma.timeAway.findFirst({
      where: {
        id: timeAwayId,
      },
    });

    if (!existing) {
      throw new EntityNotFoundException();
    }

    if (existing?.isApproved) {
      throw new EntityConflictException('The leave has already been finalized');
    }

    const timeAway = await this.prisma.timeAway.update({
      where: { id: timeAwayId },
      data: {
        userId: user.id,
        startDate: input.startDate,
        endDate: input.endDate,
        hours: input.hours,
        timeAwayTypeId: input.timeAwayTypeId,
        reason: input.reason,

        approvalHistory: {
          updateMany: {
            where: { timeAwayId },
            data: { isApproved: null, message: null },
          },
        },
      },
    });
    // CRITICAL://
    return timeAway as unknown as CreatedTimeAwayDto;
  }
}
