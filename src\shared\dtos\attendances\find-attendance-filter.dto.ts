import { createZodDto } from '@wahyubucil/nestjs-zod-openapi';
import { z } from 'zod';
import { createdAttendance } from './created-attendance.dto';

export const findAttendanceFilter = createdAttendance
  .pick({
    id: true,
    userId: true,
  })
  .extend({
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
  })
  .partial();

export class FindAttendanceFilterDto extends createZodDto(
  findAttendanceFilter,
) {}
